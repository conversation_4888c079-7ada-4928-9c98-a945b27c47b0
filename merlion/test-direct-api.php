<?php
/**
 * Direct API Test - Test Appika API call directly
 */

session_start();
include('../functions/server.php');
include('../functions/graphql_functions.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$message = '';
$messageType = '';
$debugInfo = [];

// Handle test request
if (isset($_POST['test_api'])) {
    $ticketId = intval($_POST['ticket_id']);
    $priority = $_POST['priority'];
    $status = $_POST['status'];
    
    // Test data for Appika API
    $updateData = [
        'contact_id' => null,
        'agent_id' => null,
        'subject' => '16-6-25 Appika test ticket',
        'type' => 1,
        'type_name' => 'starter',
        'priority' => $priority,
        'status' => $status,
        'req_email' => '<EMAIL>',
        'time_track' => '00:00:00',
        'reply_msg' => "Direct API test - Priority: {$priority}, Status: {$status}",
        'tags' => ''
    ];
    
    $debugInfo['request_data'] = $updateData;
    $debugInfo['ticket_id'] = $ticketId;
    
    // Call Appika API directly
    $apiResult = updateAppikaTicket($ticketId, $updateData);
    $debugInfo['api_result'] = $apiResult;
    
    if ($apiResult['success']) {
        $message = 'API call successful! Check Appika to see if data was updated.';
        $messageType = 'success';
    } else {
        $message = 'API call failed: ' . ($apiResult['error'] ?? 'Unknown error');
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct API Test</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    
    <style>
    body { background-color: #f8f9fa; }
    .container { max-width: 1000px; margin: 50px auto; }
    .card { box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; }
    .debug-info { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
    pre { background-color: #f1f1f1; padding: 10px; border-radius: 4px; font-size: 12px; max-height: 400px; overflow-y: auto; }
    </style>
</head>

<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="fas fa-flask"></i> Direct Appika API Test
                </h4>
                <small>Test API calls directly to Appika (Ticket ID 121)</small>
            </div>
            
            <div class="card-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="ticket_id"><strong>Appika Ticket ID:</strong></label>
                            <input type="number" name="ticket_id" id="ticket_id" class="form-control" value="121" required>
                            <small class="text-muted">Use 121 for the test ticket</small>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="priority"><strong>Priority:</strong></label>
                            <select name="priority" id="priority" class="form-control" required>
                                <option value="LOW">LOW</option>
                                <option value="MEDIUM">MEDIUM</option>
                                <option value="HIGH">HIGH</option>
                                <option value="URGENT">URGENT</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="status"><strong>Status:</strong></label>
                            <select name="status" id="status" class="form-control" required>
                                <option value="OPEN">OPEN</option>
                                <option value="WIP">WIP</option>
                                <option value="CLOSED">CLOSED</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" name="test_api" class="btn btn-warning">
                                <i class="fas fa-rocket"></i> Test API Call
                            </button>
                        </div>
                    </div>
                </form>

                <?php if (!empty($debugInfo)): ?>
                    <div class="debug-info mt-4">
                        <h5><i class="fas fa-bug"></i> Debug Information:</h5>
                        
                        <h6>Request Data Sent to Appika:</h6>
                        <pre><?php echo json_encode($debugInfo['request_data'], JSON_PRETTY_PRINT); ?></pre>
                        
                        <h6>API Response from Appika:</h6>
                        <pre><?php echo json_encode($debugInfo['api_result'], JSON_PRETTY_PRINT); ?></pre>
                    </div>
                <?php endif; ?>
                
                <div class="mt-3">
                    <a href="admin-tickets.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Admin
                    </a>
                    <a href="admin-ticket-detail.php?id=193" class="btn btn-info ml-2">
                        <i class="fas fa-ticket-alt"></i> View Ticket #193
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Instructions</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Set values:</strong> Choose Priority and Status you want to test</li>
                    <li><strong>Click "Test API Call":</strong> This will send the request directly to Appika</li>
                    <li><strong>Check the debug output:</strong> See exactly what was sent and received</li>
                    <li><strong>Verify in Appika:</strong> Check if the ticket was actually updated in Appika system</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <strong>Note:</strong> This bypasses all local database logic and calls Appika API directly.
                    If this works, the issue is in the sync logic. If this fails, the issue is with the API connection.
                </div>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
