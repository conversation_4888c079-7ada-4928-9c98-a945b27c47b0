<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to add location to a customer in Appika
function addLocationToAppika($customerDbId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for adding a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

// Check if admin_logs table exists, create it if it doesn't
$check_table_sql = "SHOW TABLES LIKE 'admin_logs'";
$table_result = mysqli_query($conn, $check_table_sql);

if (mysqli_num_rows($table_result) == 0) {
    // Create admin_logs table
    $create_table_sql = "CREATE TABLE admin_logs (
        id INT(11) NOT NULL AUTO_INCREMENT,
        admin_id INT(11) NOT NULL,
        action VARCHAR(50) NOT NULL,
        description TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        PRIMARY KEY (id)
    )";

    mysqli_query($conn, $create_table_sql);
}

// Get search parameter
$search = isset($_GET['search']) ? trim($_GET['search']) : "";

// Build search condition
$where_clause = "";
if (!empty($search)) {
    $search_safe = mysqli_real_escape_string($conn, $search);
    $where_clause = "WHERE username LIKE '%$search_safe%' OR
                     email LIKE '%$search_safe%' OR
                     company_name LIKE '%$search_safe%'";
}

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $items_per_page;

// Count total users for pagination
$count_sql = "SELECT COUNT(*) as total FROM user $where_clause";
$count_result = mysqli_query($conn, $count_sql);
$total_items = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get users with pagination
$users_sql = "SELECT id, username, email, starter_tickets, premium_tickets, ultimate_tickets, company_name, tell, registration_time, appika_id FROM user $where_clause ORDER BY registration_time DESC LIMIT $items_per_page OFFSET $offset";
$users_result = mysqli_query($conn, $users_sql);

// Process add tickets form and create user form
$message = '';
$message_type = '';

// Process create user form
if (isset($_POST['create_user'])) {
    $first_name = mysqli_real_escape_string($conn, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($conn, $_POST['last_name']);
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password = mysqli_real_escape_string($conn, $_POST['password']);
    $confirm_password = mysqli_real_escape_string($conn, $_POST['confirm_password']);
    $phone = mysqli_real_escape_string($conn, $_POST['phone']);
    $company_name = mysqli_real_escape_string($conn, $_POST['company_name']);
    $tax_id = mysqli_real_escape_string($conn, $_POST['tax_id']);
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $address2 = mysqli_real_escape_string($conn, $_POST['address2']);
    $district = mysqli_real_escape_string($conn, $_POST['district']);
    $city = mysqli_real_escape_string($conn, $_POST['city']);
    $state = mysqli_real_escape_string($conn, $_POST['state']);
    $postal_code = mysqli_real_escape_string($conn, $_POST['postal_code']);
    $country = mysqli_real_escape_string($conn, $_POST['country']);
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Validate input
    $errors = [];

    // Required fields validation
    $required_fields = [
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'username' => 'Username',
        'email' => 'Email',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'phone' => 'Phone',
        'address' => 'Address',
        'district' => 'District',
        'city' => 'City',
        'postal_code' => 'Postal Code',
        'country' => 'Country'
    ];

    foreach ($required_fields as $field => $label) {
        if (empty($$field)) {
            $errors[] = "$label is required";
        }
    }

    // If any required fields are missing, no need to continue with other validations
    if (!empty($errors)) {
        // Return early with the errors
    } else {
        // Username validation
        $check_username = "SELECT id FROM user WHERE username = '$username'";
        $result_username = mysqli_query($conn, $check_username);
        if (mysqli_num_rows($result_username) > 0) {
            $errors[] = "Username already exists";
        }

        // Email validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format";
        } else {
            // Check if email already exists
            $check_email = "SELECT id FROM user WHERE email = '$email'";
            $result_email = mysqli_query($conn, $check_email);
            if (mysqli_num_rows($result_email) > 0) {
                $errors[] = "Email already exists";
            }
        }

        // Password validation
        if (strlen($password) < 6) {
            $errors[] = "Password must be at least 6 characters";
        }

        // Confirm password validation
        if ($password !== $confirm_password) {
            $errors[] = "Passwords do not match";
        }
    }

    // If no errors, create user
    if (empty($errors)) {
        $hashed_password = md5($password); // Note: In a real application, use a more secure hashing method

        $insert_sql = "INSERT INTO user (first_name, last_name, username, email, password, tell, company_name, tax_id, address, address2, district, city, state, postal_code, country)
                      VALUES ('$first_name', '$last_name', '$username', '$email', '$hashed_password', '$phone', '$company_name', '$tax_id', '$address', '$address2', '$district', '$city', '$state', '$postal_code', '$country')";

        if (mysqli_query($conn, $insert_sql)) {
            $new_user_id = mysqli_insert_id($conn);

            // Generate appika_id
            $appika_id = 'HI' . $new_user_id;

            // Update the user with the generated appika_id
            $update_sql = "UPDATE user SET appika_id = '$appika_id' WHERE id = $new_user_id";
            mysqli_query($conn, $update_sql);

            // Create logs directory if it doesn't exist
            if (!file_exists("../logs")) {
                mkdir("../logs", 0755, true);
            }

            // Log file for debugging
            $log_file = fopen("../logs/appika_api_admin.log", "a");

            // Prepare data for Appika API
            $customerName = $first_name . ' ' . $last_name;
            $customerNo = $appika_id;
            $customerStartDate = date('Y-m-d');

            // Set fixed values as per requirements
            $customerGroup = '10';
            $ofcId = '511';
            $assignTo = '1';
            $creator = '1';
            $status = 'a';

            // Create customer data structure for Appika API
            $customerData = [
                'no' => $customerNo,
                'name' => $customerName,
                'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
                'grp_id' => $customerGroup,
                'ofc_id' => $ofcId,
                'assign2' => $assignTo,
                'creator' => $creator,
                'start_date' => $customerStartDate,
                'status' => $status
            ];

            // Step 1: Send customer data to Appika API
            $apiResult = sendToAppikaAPI($customerData);

            // Log API result for debugging
            fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $new_user_id - Create Customer API Result: " . json_encode($apiResult) . "\n");

            // Step 2: If customer creation was successful, add location data
            if ($apiResult['success'] && isset($apiResult['data'][0]['id'])) {
                // Get the Appika customer ID
                $appikaCustomerId = $apiResult['data'][0]['id'];

                // Prepare location data
                $locationData = [
                    'loc_code' => 'LOC-' . $customerNo,
                    'loc_name' => $customerName . ' Location',
                    'add1' => $address,
                    'add2' => $address2,
                    'ccode' => $country,
                    'state_code' => $state,
                    'city' => $city,
                    'status' => $status,
                    'is_primary_loc' => 'y',
                    'zip' => $postal_code,
                    'parent_id' => 0
                ];

                // Add location to Appika
                $locationResult = addLocationToAppika($appikaCustomerId, $locationData);

                // Log location API result
                fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $new_user_id - Add Location API Result: " . json_encode($locationResult) . "\n");
            }

            // Close log file
            fclose($log_file);

            $message = "User created successfully";
            $message_type = 'success';

            // If AJAX request, return JSON success
            if ($is_ajax) {
                $user_query = "SELECT id, first_name, last_name, username, email, starter_tickets, premium_tickets, ultimate_tickets, company_name, tell, registration_time, address, address2, district, city, postal_code, country, appika_id FROM user WHERE id = $new_user_id";
                $user_result = mysqli_query($conn, $user_query);
                $new_user = mysqli_fetch_assoc($user_result);

                $response = [
                    'success' => true,
                    'message' => $message,
                    'user' => $new_user
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error creating user: " . mysqli_error($conn);
            $message_type = 'danger';

            // If AJAX request, return JSON error
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'danger';

        // If AJAX request, return JSON error
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}

if (isset($_POST['add_tickets'])) {
    $user_id = (int)$_POST['user_id'];
    $ticket_type = mysqli_real_escape_string($conn, $_POST['ticket_type']);
    $ticket_amount = (int)$_POST['ticket_amount'];
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    if ($ticket_amount > 0 && in_array($ticket_type, ['starter_tickets', 'premium_tickets', 'ultimate_tickets'])) {
        // Update user's ticket count
        $update_sql = "UPDATE user SET $ticket_type = $ticket_type + $ticket_amount WHERE id = $user_id";

        if (mysqli_query($conn, $update_sql)) {
            // Get user info for log and response
            $user_query = "SELECT id, username, email, starter_tickets, premium_tickets, ultimate_tickets, company_name, tell, registration_time FROM user WHERE id = $user_id";
            $user_result = mysqli_query($conn, $user_query);
            $user = mysqli_fetch_assoc($user_result);

            // Create log entry
            $ticket_type_clean = str_replace('_tickets', '', $ticket_type);
            // Display 'Business' for premium tickets in logs
            if ($ticket_type_clean == 'premium') {
                $ticket_type_clean = 'Business';
            }
            $description = "Admin added $ticket_amount $ticket_type_clean tickets";

            // Check if a similar log entry already exists within the last 5 seconds
            // This prevents duplicate logs when the page is refreshed or when multiple requests are sent
            $check_duplicate_sql = "SELECT id FROM ticket_logs
                                   WHERE user_id = $user_id
                                   AND ticket_type = '$ticket_type_clean'
                                   AND amount = '$ticket_amount'
                                   AND description = '$description'
                                   AND performed_by_admin_id = $admin_id
                                   AND created_at >= NOW() - INTERVAL 5 SECOND";
            $duplicate_result = mysqli_query($conn, $check_duplicate_sql);

            // Only insert if no duplicate found
            if (mysqli_num_rows($duplicate_result) == 0) {
                $log_sql = "INSERT INTO ticket_logs (action, description, user_id, ticket_type, amount, performed_by_admin_id, created_at)
                            VALUES ('success', '$description', $user_id, '$ticket_type_clean', '$ticket_amount', $admin_id, NOW())";
                mysqli_query($conn, $log_sql);
            }

            $message = "Successfully added $ticket_amount $ticket_type_clean tickets to user {$user['username']}";
            $message_type = 'success';

            // If this is an AJAX request, return JSON response
            if ($is_ajax) {
                $response = [
                    'success' => true,
                    'message' => $message,
                    'user' => $user,
                    'ticket_type' => $ticket_type,
                    'ticket_amount' => $ticket_amount
                ];
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error adding tickets: " . mysqli_error($conn);
            $message_type = 'danger';

            // If this is an AJAX request, return JSON error response
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = "Invalid ticket type or amount";
        $message_type = 'danger';

        // If this is an AJAX request, return JSON error response
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}

// Process delete user form
if (isset($_POST['delete_user'])) {
    $user_id = (int)$_POST['user_id'];
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Get user info for log before deletion
    $user_query = "SELECT username FROM user WHERE id = $user_id";
    $user_result = mysqli_query($conn, $user_query);

    if (mysqli_num_rows($user_result) > 0) {
        $user = mysqli_fetch_assoc($user_result);
        $username = $user['username'];

        // Start transaction
        mysqli_begin_transaction($conn);

        try {
            // First, check the structure of the tickets table to find the correct column name
            $check_tickets_sql = "SHOW COLUMNS FROM tickets";
            $tickets_columns = mysqli_query($conn, $check_tickets_sql);

            $user_id_column_tickets = 'user_id'; // Default column name
            if ($tickets_columns) {
                while ($column = mysqli_fetch_assoc($tickets_columns)) {
                    // Look for columns that might refer to user ID
                    if (in_array($column['Field'], ['user_id', 'uid', 'userid', 'user'])) {
                        $user_id_column_tickets = $column['Field'];
                        break;
                    }
                }
            }

            // Check the structure of the ticket_logs table
            $check_logs_sql = "SHOW COLUMNS FROM ticket_logs";
            $logs_columns = mysqli_query($conn, $check_logs_sql);

            $user_id_column_logs = 'user_id'; // Default column name
            if ($logs_columns) {
                while ($column = mysqli_fetch_assoc($logs_columns)) {
                    // Look for columns that might refer to user ID
                    if (in_array($column['Field'], ['user_id', 'uid', 'userid', 'user'])) {
                        $user_id_column_logs = $column['Field'];
                        break;
                    }
                }
            }

            // Let's first check the structure of all tables to understand what we're working with
            $tables_info = [];

            // Get list of all tables
            $tables_query = mysqli_query($conn, "SHOW TABLES");
            while ($table = mysqli_fetch_array($tables_query)) {
                $table_name = $table[0];

                // Get columns for each table
                $columns_query = mysqli_query($conn, "DESCRIBE $table_name");
                $columns = [];
                while ($column = mysqli_fetch_assoc($columns_query)) {
                    $columns[] = $column['Field'];
                }

                $tables_info[$table_name] = $columns;
            }

            // Log all tables and their columns for debugging
            error_log("Database structure: " . json_encode($tables_info));

            // Try a direct approach for known tables

            // 1. Handle tickets table - try different possible column names
            $possible_ticket_columns = ['user_id', 'uid', 'userid', 'user', 'id_user', 'customer_id'];
            $ticket_deleted = false;

            // First check if tickets table exists
            $check_tickets_table = mysqli_query($conn, "SHOW TABLES LIKE 'tickets'");
            if (mysqli_num_rows($check_tickets_table) > 0) {
                foreach ($possible_ticket_columns as $column) {
                    // Check if this column exists in the tickets table
                    $check_column = mysqli_query($conn, "SHOW COLUMNS FROM tickets LIKE '$column'");
                    if ($check_column && mysqli_num_rows($check_column) > 0) {
                        // Column exists, try to delete
                        $delete_tickets_sql = "DELETE FROM tickets WHERE $column = $user_id";
                        $result = mysqli_query($conn, $delete_tickets_sql);
                        if ($result) {
                            $ticket_deleted = true;
                            error_log("Successfully deleted from tickets using column: $column");
                            break;
                        } else {
                            error_log("Failed to delete from tickets using column $column: " . mysqli_error($conn));
                        }
                    }
                }

                if (!$ticket_deleted) {
                    error_log("Could not delete from tickets table. Tried columns: " . implode(", ", $possible_ticket_columns));
                }
            } else {
                error_log("Tickets table does not exist in the database");
            }

            // 2. Handle ticket_logs table - try different possible column names
            $possible_log_columns = ['user_id', 'uid', 'userid', 'user', 'id_user', 'customer_id'];
            $logs_deleted = false;

            // First check if ticket_logs table exists
            $check_logs_table = mysqli_query($conn, "SHOW TABLES LIKE 'ticket_logs'");
            if (mysqli_num_rows($check_logs_table) > 0) {
                foreach ($possible_log_columns as $column) {
                    // Check if this column exists in the ticket_logs table
                    $check_column = mysqli_query($conn, "SHOW COLUMNS FROM ticket_logs LIKE '$column'");
                    if ($check_column && mysqli_num_rows($check_column) > 0) {
                        // Column exists, try to delete
                        $delete_logs_sql = "DELETE FROM ticket_logs WHERE $column = $user_id";
                        $result = mysqli_query($conn, $delete_logs_sql);
                        if ($result) {
                            $logs_deleted = true;
                            error_log("Successfully deleted from ticket_logs using column: $column");
                            break;
                        } else {
                            error_log("Failed to delete from ticket_logs using column $column: " . mysqli_error($conn));
                        }
                    }
                }

                if (!$logs_deleted) {
                    error_log("Could not delete from ticket_logs table. Tried columns: " . implode(", ", $possible_log_columns));
                }
            } else {
                error_log("Ticket_logs table does not exist in the database");
            }

            // 3. Try to find and delete from any other tables that might reference the user
            foreach ($tables_info as $table_name => $columns) {
                // Skip the user table and already handled tables
                if ($table_name == 'user' || $table_name == 'tickets' || $table_name == 'ticket_logs') {
                    continue;
                }

                // Look for columns that might reference users
                foreach ($columns as $column) {
                    if (strpos(strtolower($column), 'user') !== false ||
                        strpos(strtolower($column), 'uid') !== false ||
                        strpos(strtolower($column), 'customer') !== false) {

                        // Try to delete from this table
                        $delete_sql = "DELETE FROM $table_name WHERE $column = $user_id";
                        $result = mysqli_query($conn, $delete_sql);
                        if ($result) {
                            error_log("Successfully deleted from $table_name using column: $column");
                        }
                    }
                }
            }

            // Check if the user still exists (might have been deleted by a foreign key constraint)
            $check_user_sql = "SELECT id FROM user WHERE id = $user_id";
            $check_user_result = mysqli_query($conn, $check_user_sql);

            if (mysqli_num_rows($check_user_result) > 0) {
                // User still exists, delete them
                $delete_user_sql = "DELETE FROM user WHERE id = $user_id";
                $result = mysqli_query($conn, $delete_user_sql);
                if (!$result) {
                    throw new Exception("Error deleting user record: " . mysqli_error($conn));
                }
                error_log("Successfully deleted user with ID: $user_id");
            } else {
                error_log("User with ID $user_id no longer exists in the database");
            }

            // Create admin log entry
            try {
                $log_description = "Admin deleted user: $username (ID: $user_id)";
                $admin_log_sql = "INSERT INTO admin_logs (admin_id, action, description, created_at)
                                VALUES ($admin_id, 'delete_user', '$log_description', NOW())";
                $log_result = mysqli_query($conn, $admin_log_sql);

                if (!$log_result) {
                    error_log("Failed to create admin log entry: " . mysqli_error($conn));
                } else {
                    error_log("Successfully created admin log entry for user deletion");
                }
            } catch (Exception $log_error) {
                // Just log the error but don't throw an exception to allow the transaction to complete
                error_log("Error creating admin log: " . $log_error->getMessage());
            }

            // Commit transaction
            mysqli_commit($conn);

            $message = "User $username has been successfully deleted";
            $message_type = 'success';

            // If this is an AJAX request, return JSON response
            if ($is_ajax) {
                $response = [
                    'success' => true,
                    'message' => $message,
                    'user_id' => $user_id
                ];
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);

            // Get the actual MySQL error if available
            $mysql_error = mysqli_error($conn);

            // Create a detailed error message for debugging
            $debug_info = "Tables and columns found:\n";
            foreach ($tables_info as $table => $columns) {
                $debug_info .= "Table: $table\nColumns: " . implode(", ", $columns) . "\n";
            }

            // Log the detailed error information
            error_log("User deletion error details: " . $debug_info);
            error_log("Exception message: " . $e->getMessage());
            if (!empty($mysql_error)) {
                error_log("MySQL error: " . $mysql_error);
                $message = "Error deleting user: " . $mysql_error;
            } else {
                $message = "Error deleting user: " . $e->getMessage();
            }

            // For admin users, show more detailed error
            if ($admin_role === 'super_admin') {
                $message .= "<br><small>Please check the error log for more details.</small>";
            }

            $message_type = 'danger';

            // If this is an AJAX request, return JSON error response
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message,
                    'error' => $mysql_error ?: $e->getMessage()
                ];
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = "User not found";
        $message_type = 'danger';

        // If this is an AJAX request, return JSON error response
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - User Management</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
            height: calc(100vh - 90px);
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 15px;
        }

        .admin-sidebar ul {
            display: flex;
            flex-wrap: wrap;
        }

        .admin-sidebar ul li {
            margin-right: 5px;
            margin-bottom: 5px;
            width: calc(50% - 5px);
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
            text-align: center;
        }

        .admin-sidebar ul li a i {
            margin-right: 5px;
            width: auto;
        }
    }

    @media (max-width: 480px) {
        .admin-sidebar ul li {
            width: 100%;
            margin-right: 0;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
            height: calc(100vh - 90px);
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
            margin-bottom: 20px;
        }
    }

    @media (max-width: 480px) {
        .admin-content {
            padding: 10px;
        }
    }

    .filter-row {
        margin-bottom: 20px;
    }

    /* Table styles */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 20px;
    }

    /* Responsive table styles */
    @media (max-width: 991px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.6rem 0.4rem;
        }
    }

    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.3rem;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 12px;
        }

        .table th,
        .table td {
            padding: 0.3rem 0.2rem;
        }
    }

    /* Search box and filter styles */
    .search-box {
        width: 100%;
    }

    .search-box .input-group {
        width: 100%;
        max-width: 450px;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-select {
        font-size: 14px;
        height: 38px;
    }

    /* Responsive search and filters */
    @media (max-width: 991px) {
        .search-box .input-group {
            max-width: 100%;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 767px) {
        .search-input {
            font-size: 13px;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .search-input::placeholder {
            font-size: 12px;
        }

        .filter-select {
            font-size: 12px;
        }
    }

    /* Pagination styles */
    .pagination-container {
        margin-top: 20px;
    }

    .pagination .page-link {
        color: #473BF0;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    @media (max-width: 767px) {
        .pagination {
            font-size: 14px;
        }

        .pagination .page-link {
            padding: 0.3rem 0.6rem;
        }
    }

    @media (max-width: 480px) {
        .pagination {
            font-size: 12px;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
        }
    }

    /* Badge styles */
    .badge {
        font-size: 14px;
        padding: 5px 8px;
        border-radius: 4px;
        font-weight: 500;
    }

    @media (max-width: 767px) {
        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }
    }

    @media (max-width: 480px) {
        .badge {
            font-size: 11px;
            padding: 3px 5px;
        }
    }

    /* Ticket Status Badge Colors */
    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket Type Badge Colors */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    /* Action button styles */
    .btn-action {
        width: 30px !important;
        height: 30px !important;
        min-width: 30px !important;
        max-width: 30px !important;
        min-height: 30px !important;
        max-height: 30px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 12px !important;
        border-radius: 4px !important;
        margin: 2px !important;
    }

    .btn-action i {
        margin: 0 !important;
    }

    /* Responsive button styles */
    @media (max-width: 767px) {
        .btn-action {
            width: 28px !important;
            height: 28px !important;
            min-width: 28px !important;
            max-width: 28px !important;
            min-height: 28px !important;
            max-height: 28px !important;
            font-size: 11px !important;
        }
    }

    @media (max-width: 480px) {
        .btn-action {
            width: 26px !important;
            height: 26px !important;
            min-width: 26px !important;
            max-width: 26px !important;
            min-height: 26px !important;
            max-height: 26px !important;
            font-size: 10px !important;
        }
    }

    .modal-header {
        background-color: #473BF0;
        color: white;
    }

    .modal-header .close {
        color: white;
    }

    .modal-title {
        font-size: 22px;
        color: white;
        font-weight: 500;
    }

    /* Modal button styles - matching admin-staff.php */
    .modal-btn-primary {
        background-color: #473BF0 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        min-width: 80px;
    }

    .modal-btn-primary:hover {
        background-color: #3a30c0 !important;
    }

    .modal-btn-cancel {
        background-color: #dc3545 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        min-width: 80px;
    }

    .modal-btn-cancel:hover {
        background-color: #c82333 !important;
    }

    @media (max-width: 767px) {

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 13px !important;
            padding: 7px 14px !important;
        }
    }

    @media (max-width: 575px) {

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 12px !important;
            padding: 6px 12px !important;
        }
    }

    /* Create User Button Styles */
    .create-user-btn {
        background-color: #473BF0 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        float: right;
    }

    .create-user-btn:hover {
        background-color: #3a30c0 !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 767px) {
        .create-user-btn {
            width: 100%;
            margin-top: 10px;
            font-size: 13px !important;
        }
    }

    /* Password toggle eye icon styling */
    .toggle-password {
        cursor: pointer;
        border-left: none;
    }

    .toggle-password:hover {
        color: #473BF0;
    }

    .input-group-text {
        background-color: #fff;
        border-left: 0;
        height: 50px;
    }

    .input-group .form-control:focus+.input-group-append .input-group-text {
        border-color: #80bdff;
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }

    h5#deleteUserModalLabel12 {
        color: white;
    }

    button.btn.btn-secondary {
        color: white;
        background-color: red;
    }

    button.btn.btn-danger {
        background-color: blue;
    }

    input#first_name,
    input#last_name,
    input#username,
    input#email,
    input#password,
    input#confirm_password,
    input#phone,
    input#company_name,
    input#tax_id,
    input#address,
    input#address2,
    input#district,
    input#city,
    input#state,
    input#postal_code,
    input#country {
        height: 50px;
    }

    button.btn.btn-danger.btn-action {
        background-color: red;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>User Management</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <div class="filter-row">
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <form method="GET">
                                    <div class="search-box">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by username, email or company name"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="col-md-4 text-right">
                                <button type="button" class="btn create-user-btn" data-toggle="modal"
                                    data-target="#createUserModal">
                                    <i class="fas fa-plus"></i> &nbsp; Create New User
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>APK No</th>
                                    <th>Starter Tickets</th>
                                    <th>Premium Tickets</th>
                                    <th>Ultimate Tickets</th>
                                    <th>Company</th>
                                    <th>Phone</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($users_result) > 0): ?>
                                <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                <tr data-user-id="<?php echo $user['id']; ?>">
                                    <td><?php echo $user['id']; ?></td>
                                    <td>
                                        <a href="admin-user-detail.php?id=<?php echo $user['id']; ?>">
                                            <?php echo htmlspecialchars($user['username']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo htmlspecialchars($user['appika_id'] ?? '-'); ?></td>
                                    <td><?php echo $user['starter_tickets']; ?></td>
                                    <td><?php echo $user['premium_tickets']; ?></td>
                                    <td><?php echo $user['ultimate_tickets']; ?></td>
                                    <td><?php echo htmlspecialchars($user['company_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['tell']); ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($user['registration_time'])); ?></td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-primary btn-action" data-toggle="modal"
                                                data-target="#addTicketsModal<?php echo $user['id']; ?>"
                                                title="Add Tickets">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <a href="admin-user-detail.php?id=<?php echo $user['id']; ?>"
                                                class="btn btn-info btn-action" title="View User Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-action" data-toggle="modal"
                                                data-target="#deleteUserModal<?php echo $user['id']; ?>"
                                                title="Delete User">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Add Tickets Modal -->
                                <div class="modal fade" id="addTicketsModal<?php echo $user['id']; ?>" tabindex="-1"
                                    role="dialog" aria-labelledby="addTicketsModalLabel<?php echo $user['id']; ?>"
                                    aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" style="color: white;"
                                                    id="addTicketsModalLabel<?php echo $user['id']; ?>">Add Tickets for
                                                    <?php echo htmlspecialchars($user['username']); ?></h5>
                                                <button type="button" class="close text-white" data-dismiss="modal"
                                                    aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form id="addTicketsForm<?php echo $user['id']; ?>" method="POST">
                                                <div class="modal-body">
                                                    <div class="alert alert-danger mt-3 add-tickets-error-message"
                                                        style="display: none;"></div>

                                                    <input type="hidden" name="user_id"
                                                        value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="add_tickets" value="1">
                                                    <input type="hidden" name="ajax" value="1">

                                                    <div class="form-group">
                                                        <label for="ticket_type<?php echo $user['id']; ?>">Ticket
                                                            Type</label>
                                                        <select class="form-control"
                                                            id="ticket_type<?php echo $user['id']; ?>"
                                                            name="ticket_type" required>
                                                            <option value="starter_tickets">Starter</option>
                                                            <option value="premium_tickets">Business</option>
                                                            <option value="ultimate_tickets">Ultimate</option>
                                                        </select>
                                                    </div>

                                                    <div class="form-group">
                                                        <label
                                                            for="ticket_amount<?php echo $user['id']; ?>">Amount</label>
                                                        <input type="number" class="form-control"
                                                            id="ticket_amount<?php echo $user['id']; ?>"
                                                            name="ticket_amount" min="1" value="1" required>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn modal-btn-cancel"
                                                        data-dismiss="modal">Cancel</button>
                                                    <button type="button" class="btn modal-btn-primary add-tickets-btn"
                                                        data-user-id="<?php echo $user['id']; ?>">
                                                        Add Tickets
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete User Modal -->
                                <div class="modal fade" id="deleteUserModal<?php echo $user['id']; ?>" tabindex="-1"
                                    role="dialog" aria-labelledby="deleteUserModalLabel<?php echo $user['id']; ?>"
                                    aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger text-white">
                                                <h5 class="modal-title"
                                                    id="deleteUserModalLabel<?php echo $user['id']; ?>">Confirm User
                                                    Deletion</h5>
                                                <button type="button" class="close text-white" data-dismiss="modal"
                                                    aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form id="deleteUserForm<?php echo $user['id']; ?>" method="POST">
                                                <div class="modal-body">
                                                    <input type="hidden" name="user_id"
                                                        value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="delete_user" value="1">
                                                    <input type="hidden" name="ajax" value="1">
                                                    <p class="text-center mb-3">
                                                        <i class="fas fa-exclamation-triangle text-danger"
                                                            style="font-size: 48px;"></i>
                                                    </p>
                                                    <p>Are you sure you want to delete the user
                                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>?
                                                    </p>
                                                    <p class="text-danger"><strong>Warning:</strong> This action cannot
                                                        be undone. All user data, tickets, and history will be
                                                        permanently deleted.</p>
                                                    <div class="alert alert-danger mt-3 delete-error-message"
                                                        style="display: none;"></div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn modal-btn-cancel"
                                                        data-dismiss="modal">Cancel</button>
                                                    <button type="button" class="btn modal-btn-primary delete-user-btn"
                                                        data-user-id="<?php echo $user['id']; ?>">
                                                        <i class="fas fa-trash-alt mr-1"></i> Delete User
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center">No users found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <div class="pagination-container mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>"
                                        aria-label="Previous">
                                        <span aria-hidden="true">&laquo; Previous</span>
                                    </a>
                                </li>

                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>"
                                        aria-label="Next">
                                        <span aria-hidden="true">Next &raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle modal hidden event to reset button state
        $(document).on('hidden.bs.modal', '.modal', function() {
            // Find any add tickets buttons in this modal and reset them
            const addTicketsBtn = $(this).find('.add-tickets-btn');
            if (addTicketsBtn.length) {
                addTicketsBtn.html('Add Tickets').prop('disabled', false);
            }

            // Find any delete user buttons in this modal and reset them
            const deleteUserBtn = $(this).find('.delete-user-btn');
            if (deleteUserBtn.length) {
                deleteUserBtn.html('<i class="fas fa-trash-alt mr-1"></i> Delete User').prop('disabled',
                    false);
            }

            // Hide any error messages
            $(this).find('.alert').hide();
        });

        // Handle add tickets via AJAX
        const addTicketsButtons = document.querySelectorAll('.add-tickets-btn');
        addTicketsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-user-id');
                const form = document.getElementById('addTicketsForm' + userId);
                const errorMessage = form.querySelector('.add-tickets-error-message');
                const modal = jQuery('#addTicketsModal' + userId);

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Adding...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch('admin-users', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - update the user row in the table
                            const userRow = document.querySelector('tr[data-user-id="' +
                                userId + '"]');
                            if (userRow) {
                                // Get the ticket type and update the corresponding cell
                                const ticketType = document.getElementById('ticket_type' +
                                    userId).value;
                                const ticketAmount = parseInt(document.getElementById(
                                    'ticket_amount' + userId).value);

                                // Determine which cell to update based on ticket type
                                let cellIndex;
                                if (ticketType === 'starter_tickets') {
                                    cellIndex = 5; // Starter tickets column
                                } else if (ticketType === 'premium_tickets') {
                                    cellIndex = 6; // Premium tickets column
                                } else if (ticketType === 'ultimate_tickets') {
                                    cellIndex = 7; // Ultimate tickets column
                                }

                                // Update the cell with the new ticket count
                                if (cellIndex !== undefined) {
                                    const cell = userRow.cells[cellIndex];
                                    if (cell) {
                                        cell.textContent = data.user[ticketType];
                                    }
                                }
                            }

                            // Reset form
                            form.reset();

                            // Reset button state
                            this.innerHTML = 'Add Tickets';
                            this.disabled = false;

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Add Tickets';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Add Tickets';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        });

        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });

        // Handle user deletion via AJAX
        const deleteUserButtons = document.querySelectorAll('.delete-user-btn');
        deleteUserButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-user-id');
                const form = document.getElementById('deleteUserForm' + userId);
                const errorMessage = form.querySelector('.delete-error-message');
                const modal = jQuery('#deleteUserModal' + userId);

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch('admin-users', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - remove the user row from the table
                            const userRow = document.querySelector('tr[data-user-id="' +
                                userId + '"]');
                            if (userRow) {
                                userRow.remove();
                            }

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');

                            // If no users left, show the "No users found" message
                            const tableBody = document.querySelector('tbody');
                            if (tableBody.querySelectorAll('tr').length === 0) {
                                const noUsersRow = document.createElement('tr');
                                noUsersRow.innerHTML =
                                    '<td colspan="10" class="text-center">No users found</td>';
                                tableBody.appendChild(noUsersRow);
                            }
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML =
                                '<i class="fas fa-trash-alt mr-1"></i> Delete User';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML =
                            '<i class="fas fa-trash-alt mr-1"></i> Delete User';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        });

        // Function to show notification
        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'alert alert-' + type + ' alert-dismissible fade show notification-toast';
            notification.innerHTML = message +
                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                '<span aria-hidden="true">&times;</span></button>';

            // Style the notification
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';

            // Add to document
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 150);
            }, 5000);
        }

        // Handle create user via AJAX
        const createUserBtn = document.getElementById('createUserBtn');
        if (createUserBtn) {
            createUserBtn.addEventListener('click', function() {
                const form = document.getElementById('createUserForm');
                const errorMessage = form.querySelector('.create-error-message');
                const modal = jQuery('#createUserModal');

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Check if passwords match
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                if (password !== confirmPassword) {
                    errorMessage.textContent = 'Passwords do not match';
                    errorMessage.style.display = 'block';
                    return;
                }

                // Custom validation for required fields
                const requiredFields = [{
                        id: 'first_name',
                        name: 'First Name'
                    },
                    {
                        id: 'last_name',
                        name: 'Last Name'
                    },
                    {
                        id: 'username',
                        name: 'Username'
                    },
                    {
                        id: 'email',
                        name: 'Email'
                    },
                    {
                        id: 'password',
                        name: 'Password'
                    },
                    {
                        id: 'confirm_password',
                        name: 'Confirm Password'
                    },
                    {
                        id: 'phone',
                        name: 'Phone'
                    },
                    {
                        id: 'address',
                        name: 'Address'
                    },
                    {
                        id: 'district',
                        name: 'District'
                    },
                    {
                        id: 'city',
                        name: 'City'
                    },
                    {
                        id: 'postal_code',
                        name: 'Postal Code'
                    },
                    {
                        id: 'country',
                        name: 'Country'
                    }
                ];

                // Check each required field
                for (const field of requiredFields) {
                    const input = document.getElementById(field.id);
                    if (!input.value.trim()) {
                        errorMessage.textContent = `${field.name} is required`;
                        errorMessage.style.display = 'block';
                        input.focus();
                        return;
                    }
                }

                // Email validation
                const email = document.getElementById('email').value;
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    errorMessage.textContent = 'Please enter a valid email address';
                    errorMessage.style.display = 'block';
                    document.getElementById('email').focus();
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Creating...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Add AJAX parameter if not already present
                if (!formData.has('ajax')) {
                    formData.append('ajax', '1');
                }

                // Send AJAX request
                fetch('admin-users', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - add the new user to the table
                            const tableBody = document.querySelector('tbody');
                            const noUsersRow = tableBody.querySelector('tr td[colspan="10"]');

                            if (noUsersRow) {
                                // Remove the "No users found" row
                                noUsersRow.parentElement.remove();
                            }

                            // Create a new row for the user
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-user-id', data.user.id);

                            // Format the date and time
                            const registrationDate = new Date(data.user.registration_time);
                            const formattedDate = registrationDate.toISOString().slice(0, 16).replace('T', ' ');

                            // Create the row HTML
                            newRow.innerHTML = `
                            <td>${data.user.id}</td>
                            <td><a href="admin-user-detail.php?id=${data.user.id}">${data.user.username}</a></td>
                            <td>${data.user.email}</td>
                            <td>${data.user.appika_id || '-'}</td>
                            <td>${data.user.starter_tickets || 0}</td>
                            <td>${data.user.premium_tickets || 0}</td>
                            <td>${data.user.ultimate_tickets || 0}</td>
                            <td>${data.user.company_name || 'N/A'}</td>
                            <td>${data.user.tell || 'N/A'}</td>
                            <td>${formattedDate}</td>
                            <td>
                                <div class="d-flex">
                                    <button type="button" class="btn btn-primary btn-action" data-toggle="modal" data-target="#addTicketsModal${data.user.id}" title="Add Tickets">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <a href="admin-user-detail.php?id=${data.user.id}" class="btn btn-info btn-action" title="View User Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-danger btn-action" data-toggle="modal" data-target="#deleteUserModal${data.user.id}" title="Delete User">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        `;

                            // Add the row to the table
                            tableBody.appendChild(newRow);

                            // Create the add tickets modal for the new user
                            const addTicketsModal = document.createElement('div');
                            addTicketsModal.innerHTML = `
                            <div class="modal fade" id="addTicketsModal${data.user.id}" tabindex="-1" role="dialog" aria-labelledby="addTicketsModalLabel${data.user.id}" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" style="color: white;" id="addTicketsModalLabel${data.user.id}">Add Tickets for ${data.user.username}</h5>
                                            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form id="addTicketsForm${data.user.id}" method="POST">
                                            <div class="modal-body">
                                                <div class="alert alert-danger mt-3 add-tickets-error-message" style="display: none;"></div>

                                                <input type="hidden" name="user_id" value="${data.user.id}">
                                                <input type="hidden" name="add_tickets" value="1">
                                                <input type="hidden" name="ajax" value="1">

                                                <div class="form-group">
                                                    <label for="ticket_type${data.user.id}">Ticket Type</label>
                                                    <select class="form-control" id="ticket_type${data.user.id}" name="ticket_type" required>
                                                        <option value="starter_tickets">Starter</option>
                                                        <option value="premium_tickets">Business</option>
                                                        <option value="ultimate_tickets">Ultimate</option>
                                                    </select>
                                                </div>

                                                <div class="form-group">
                                                    <label for="ticket_amount${data.user.id}">Amount</label>
                                                    <input type="number" class="form-control" id="ticket_amount${data.user.id}" name="ticket_amount" min="1" value="1" required>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn modal-btn-primary add-tickets-btn" data-user-id="${data.user.id}">Add Tickets</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>`;
                            document.body.appendChild(addTicketsModal);

                            // Create the delete user modal for the new user
                            const deleteUserModal = document.createElement('div');
                            deleteUserModal.innerHTML = `
                            <div class="modal fade" id="deleteUserModal${data.user.id}" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel${data.user.id}" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title" id="deleteUserModalLabel${data.user.id}">Confirm User Deletion</h5>
                                            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form id="deleteUserForm${data.user.id}" method="POST">
                                            <div class="modal-body">
                                                <input type="hidden" name="user_id" value="${data.user.id}">
                                                <input type="hidden" name="delete_user" value="1">
                                                <input type="hidden" name="ajax" value="1">
                                                <p class="text-center mb-3">
                                                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                                                </p>
                                                <p>Are you sure you want to delete the user <strong>${data.user.username}</strong>?</p>
                                                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. All user data, tickets, and history will be permanently deleted.</p>
                                                <div class="alert alert-danger mt-3 delete-error-message" style="display: none;"></div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn modal-btn-primary delete-user-btn" data-user-id="${data.user.id}">
                                                    <i class="fas fa-trash-alt mr-1"></i> Delete User
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>`;
                            document.body.appendChild(deleteUserModal);

                            // Initialize the event listeners for the new modals
                            const newAddTicketsBtn = addTicketsModal.querySelector(
                                '.add-tickets-btn');
                            if (newAddTicketsBtn) {
                                newAddTicketsBtn.addEventListener('click', function() {
                                    const userId = this.getAttribute('data-user-id');
                                    const form = document.getElementById('addTicketsForm' +
                                        userId);
                                    const errorMessage = form.querySelector(
                                        '.add-tickets-error-message');
                                    const modal = jQuery('#addTicketsModal' + userId);

                                    // Validate form
                                    if (!form.checkValidity()) {
                                        form.reportValidity();
                                        return;
                                    }

                                    // Show loading state
                                    this.innerHTML =
                                        '<i class="fas fa-spinner fa-spin mr-1"></i> Adding...';
                                    this.disabled = true;

                                    // Create form data
                                    const formData = new FormData(form);

                                    // Send AJAX request
                                    fetch('admin-users', {
                                            method: 'POST',
                                            body: formData
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success) {
                                                // Success - update the user row in the table
                                                const userRow = document.querySelector(
                                                    'tr[data-user-id="' + userId +
                                                    '"]');
                                                if (userRow) {
                                                    // Get the ticket type and update the corresponding cell
                                                    const ticketType = document
                                                        .getElementById('ticket_type' +
                                                            userId).value;
                                                    const ticketAmount = parseInt(
                                                        document.getElementById(
                                                            'ticket_amount' + userId
                                                        ).value);

                                                    // Determine which cell to update based on ticket type
                                                    let cellIndex;
                                                    if (ticketType ===
                                                        'starter_tickets') {
                                                        cellIndex =
                                                            5; // starter tickets column
                                                    } else if (ticketType ===
                                                        'premium_tickets') {
                                                        cellIndex =
                                                            6; // Premium tickets column
                                                    } else if (ticketType ===
                                                        'ultimate_tickets') {
                                                        cellIndex =
                                                            7; // Ultimate tickets column
                                                    }

                                                    // Update the cell with the new ticket count
                                                    if (cellIndex !== undefined) {
                                                        const cell = userRow.cells[
                                                            cellIndex];
                                                        if (cell) {
                                                            cell.textContent = data
                                                                .user[ticketType];
                                                        }
                                                    }
                                                }

                                                // Reset form
                                                form.reset();

                                                // Reset button state
                                                this.innerHTML = 'Add Tickets';
                                                this.disabled = false;

                                                // Close the modal
                                                modal.modal('hide');

                                                // Show success message
                                                showNotification(data.message,
                                                    'success');
                                            } else {
                                                // Error
                                                errorMessage.textContent = data.message;
                                                errorMessage.style.display = 'block';

                                                // Reset button
                                                this.innerHTML = 'Add Tickets';
                                                this.disabled = false;
                                            }
                                        })
                                        .catch(error => {
                                            // Network or other error
                                            errorMessage.textContent =
                                                'An error occurred while processing your request. Please try again.';
                                            errorMessage.style.display = 'block';

                                            // Reset button
                                            this.innerHTML = 'Add Tickets';
                                            this.disabled = false;

                                            console.error('Error:', error);
                                        });
                                });
                            }

                            const newDeleteUserBtn = deleteUserModal.querySelector(
                                '.delete-user-btn');
                            if (newDeleteUserBtn) {
                                newDeleteUserBtn.addEventListener('click', function() {
                                    const userId = this.getAttribute('data-user-id');
                                    const form = document.getElementById('deleteUserForm' +
                                        userId);
                                    const errorMessage = form.querySelector(
                                        '.delete-error-message');
                                    const modal = jQuery('#deleteUserModal' + userId);

                                    // Show loading state
                                    this.innerHTML =
                                        '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
                                    this.disabled = true;

                                    // Create form data
                                    const formData = new FormData(form);

                                    // Send AJAX request
                                    fetch('admin-users', {
                                            method: 'POST',
                                            body: formData
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success) {
                                                // Success - remove the user row from the table
                                                const userRow = document.querySelector(
                                                    'tr[data-user-id="' + userId +
                                                    '"]');
                                                if (userRow) {
                                                    userRow.remove();
                                                }

                                                // Close the modal
                                                modal.modal('hide');

                                                // Show success message
                                                showNotification(data.message,
                                                    'success');

                                                // If no users left, show the "No users found" message
                                                const tableBody = document
                                                    .querySelector('tbody');
                                                if (tableBody.querySelectorAll('tr')
                                                    .length === 0) {
                                                    const noUsersRow = document
                                                        .createElement('tr');
                                                    noUsersRow.innerHTML =
                                                        '<td colspan="10" class="text-center">No users found</td>';
                                                    tableBody.appendChild(noUsersRow);
                                                }
                                            } else {
                                                // Error
                                                errorMessage.textContent = data.message;
                                                errorMessage.style.display = 'block';

                                                // Reset button
                                                this.innerHTML =
                                                    '<i class="fas fa-trash-alt mr-1"></i> Delete User';
                                                this.disabled = false;
                                            }
                                        })
                                        .catch(error => {
                                            // Network or other error
                                            errorMessage.textContent =
                                                'An error occurred while processing your request. Please try again.';
                                            errorMessage.style.display = 'block';

                                            // Reset button
                                            this.innerHTML =
                                                '<i class="fas fa-trash-alt mr-1"></i> Delete User';
                                            this.disabled = false;

                                            console.error('Error:', error);
                                        });
                                });
                            }

                            // Reset the form
                            form.reset();

                            // Reset button state
                            this.innerHTML = 'Create User';
                            this.disabled = false;

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Create User';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Create User';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        }

        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            // Toggle the eye icon
            $(this).find('i').toggleClass('fa-eye-slash fa-eye');

            // Toggle the password field type
            var input = $($(this).attr('toggle'));
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
            } else {
                input.attr('type', 'password');
            }
        });

        // Add hover effect to the eye icon
        $('.toggle-password').hover(
            function() {
                $(this).css('cursor', 'pointer');
            }
        );

        // Function to show notification
        function showNotification(message, type, duration = 5000) {
            // Remove any existing notifications
            $('.notification').remove();

            // Create notification element
            const notification = $('<div class="notification"></div>');
            notification.addClass(type);
            notification.text(message);

            // Add to body
            $('body').append(notification);

            // Show with animation
            setTimeout(() => {
                notification.addClass('show');
            }, 10);

            // Hide after duration
            setTimeout(() => {
                notification.removeClass('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
        }
    });
    </script>

    <style>
    /* Notification styling */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        max-width: 350px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .notification.show {
        opacity: 1;
        transform: translateY(0);
    }

    .notification.success {
        background-color: #28a745;
    }

    .notification.danger {
        background-color: #dc3545;
    }

    .notification.info {
        background-color: #17a2b8;
    }

    .notification.warning {
        background-color: #ffc107;
        color: #212529;
    }

    @media (max-width: 767px) {
        .notification {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }
    }
    </style>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1" role="dialog" aria-labelledby="createUserModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" style="color: white;" id="createUserModalLabel">Create New User</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="createUserForm" method="POST">
                    <div class="modal-body">
                        <div class="alert alert-danger mt-3 create-error-message" style="display: none;"></div>

                        <input type="hidden" name="create_user" value="1">
                        <input type="hidden" name="ajax" value="1">

                        <!-- Account Information -->
                        <h6 class="mb-3">Account Information</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="username">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="password">Password <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="input-group-append">
                                    <span class="input-group-text toggle-password" toggle="#password">
                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                    </span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Password must be at least 6 characters</small>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">Confirm Password <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password"
                                    name="confirm_password" required>
                                <div class="input-group-append">
                                    <span class="input-group-text toggle-password" toggle="#confirm_password">
                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Personal Information -->
                        <h6 class="mb-3">Personal Information</h6>

                        <div class="form-group">
                            <label for="phone">Phone <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>

                        <div class="form-group">
                            <label for="company_name">Company Name</label>
                            <input type="text" class="form-control" id="company_name" name="company_name">
                            <small class="form-text text-muted">Optional field</small>
                        </div>

                        <div class="form-group">
                            <label for="tax_id">Tax ID</label>
                            <input type="text" class="form-control" id="tax_id" name="tax_id">
                            <small class="form-text text-muted">Optional field</small>
                        </div>

                        <hr>

                        <!-- Address Information -->
                        <h6 class="mb-3">Address Information</h6>

                        <div class="form-group">
                            <label for="address">Address <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="2"
                                placeholder="Enter the primary address" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="address2">Address Notes</label>
                            <textarea class="form-control" id="address2" name="address2" rows="3"
                                placeholder="Enter any additional notes about the address"></textarea>
                            <small class="form-text text-muted">Optional field</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="district">District <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="district" name="district" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="city" name="city" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="state">State/Province</label>
                                    <input type="text" class="form-control" id="state" name="state">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="postal_code">Postal Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code"
                                        required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country">Country <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="country" name="country" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Empty div for layout balance -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                        <button type="button" id="createUserBtn" class="btn modal-btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

</html>