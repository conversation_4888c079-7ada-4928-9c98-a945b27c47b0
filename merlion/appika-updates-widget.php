<?php
/**
 * A<PERSON>ika Updates Widget
 * Shows recent tickets updated from Appika
 * Include this in admin dashboard or any admin page
 */

// Check if required columns exist
function checkAppikaColumnsExist() {
    global $conn;

    $result = mysqli_query($conn, "SHOW COLUMNS FROM support_tickets LIKE 'appika_updated_at'");
    return mysqli_num_rows($result) > 0;
}

// Function to get recent Appika updates
function getRecentAppikaUpdates($limit = 10) {
    global $conn;

    // Check if columns exist first
    if (!checkAppikaColumnsExist()) {
        return [];
    }
    
    $query = "SELECT st.id, st.subject, st.status, st.priority, st.appika_id, 
                     st.appika_updated_at, st.appika_update_source,
                     u.username, u.email
              FROM support_tickets st
              JOIN user u ON st.user_id = u.id
              WHERE st.appika_updated_at IS NOT NULL
              ORDER BY st.appika_updated_at DESC
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $updates = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $updates[] = $row;
    }
    
    return $updates;
}

// Function to count updates in last 24 hours
function countRecentAppikaUpdates() {
    global $conn;

    // Check if columns exist first
    if (!checkAppikaColumnsExist()) {
        return 0;
    }

    $query = "SELECT COUNT(*) as count
              FROM support_tickets
              WHERE appika_updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";

    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);

    return $row['count'];
}

// Function to format time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' min ago';
    if ($time < 86400) return floor($time/3600) . ' hr ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}

// Get recent updates
$recentUpdates = getRecentAppikaUpdates(10);
$updateCount24h = countRecentAppikaUpdates();
$columnsExist = checkAppikaColumnsExist();
?>

<div class="appika-updates-widget">
    <div class="widget-header">
        <h5>
            <i class="fas fa-sync-alt text-primary"></i>
            Recent Appika Updates
            <?php if ($updateCount24h > 0): ?>
                <span class="badge badge-primary"><?php echo $updateCount24h; ?></span>
            <?php endif; ?>
        </h5>
        <small class="text-muted">Last 24 hours</small>
    </div>

    <div class="widget-content">
        <?php if (!$columnsExist): ?>
            <div class="setup-notice">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <p class="mb-2"><strong>Setup Required</strong></p>
                <p class="mb-2">Appika reverse sync is not set up yet. Run the setup script to enable automatic updates from Appika.</p>
                <a href="../setup/setup-reverse-sync.php" class="btn btn-sm btn-warning">
                    <i class="fas fa-cog"></i> Run Setup
                </a>
            </div>
        <?php elseif (empty($recentUpdates)): ?>
            <div class="no-updates">
                <i class="fas fa-check-circle text-success"></i>
                <p class="mb-0">No recent updates from Appika</p>
            </div>
        <?php else: ?>
            <div class="updates-list">
                <?php foreach ($recentUpdates as $update): ?>
                    <div class="update-item">
                        <div class="update-info">
                            <div class="update-header">
                                <a href="admin-ticket-detail.php?id=<?php echo $update['id']; ?>" class="ticket-link">
                                    <strong>#<?php echo $update['id']; ?></strong>
                                    <?php if (!empty($update['appika_id'])): ?>
                                        <span class="badge badge-info badge-sm"><?php echo htmlspecialchars($update['appika_id']); ?></span>
                                    <?php endif; ?>
                                </a>
                                <span class="update-time"><?php echo timeAgo($update['appika_updated_at']); ?></span>
                            </div>
                            
                            <div class="update-details">
                                <div class="ticket-subject">
                                    <?php echo htmlspecialchars(substr($update['subject'], 0, 50)) . (strlen($update['subject']) > 50 ? '...' : ''); ?>
                                </div>
                                <div class="ticket-meta">
                                    <span class="badge badge-<?php echo $update['status']; ?>"><?php echo ucfirst($update['status']); ?></span>
                                    <span class="badge badge-<?php echo $update['priority']; ?>"><?php echo ucfirst($update['priority']); ?></span>
                                    <small class="text-muted">by <?php echo htmlspecialchars($update['username']); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="update-source">
                            <i class="fas fa-external-link-alt text-muted"></i>
                            <small class="text-muted">from Appika</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="widget-footer">
                <a href="appika-updates-log.php" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list"></i> View All Updates
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.appika-updates-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.widget-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h5 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.widget-content {
    padding: 15px 20px;
}

.no-updates {
    text-align: center;
    padding: 20px;
    color: #666;
}

.setup-notice {
    text-align: center;
    padding: 20px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
}

.update-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
}

.update-item:last-child {
    border-bottom: none;
}

.update-info {
    flex: 1;
}

.update-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.ticket-link {
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
}

.ticket-link:hover {
    text-decoration: underline;
}

.update-time {
    font-size: 12px;
    color: #666;
}

.ticket-subject {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.ticket-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.badge-sm {
    font-size: 10px;
    padding: 2px 6px;
}

.update-source {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 15px;
}

.widget-footer {
    padding: 10px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

/* Status badge colors */
.badge-open { background-color: #4CAF50; }
.badge-in_progress { background-color: #2196F3; }
.badge-resolved { background-color: #9C27B0; }
.badge-closed { background-color: #757575; }

/* Priority badge colors */
.badge-low { background-color: #28a745; }
.badge-medium { background-color: #ffc107; color: #000; }
.badge-high { background-color: #fd7e14; }
.badge-critical { background-color: #dc3545; }

/* Responsive */
@media (max-width: 768px) {
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .update-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .update-time {
        margin-top: 5px;
    }
    
    .ticket-meta {
        flex-wrap: wrap;
    }
}
</style>
