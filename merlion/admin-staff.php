<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Check if user has permission to access this page
if (!in_array($admin_role, ['admin', 'manager', 'superadmin'])) {
    header('location: index.php');
    exit();
}

// Get search parameter
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$role_filter = isset($_GET['role']) ? trim($_GET['role']) : "";

// Build search and filter conditions
$where_conditions = [];

if (!empty($search)) {
    $search_safe = mysqli_real_escape_string($conn, $search);
    $where_conditions[] = "(username LIKE '%$search_safe%' OR
                           email LIKE '%$search_safe%')";
}

if (!empty($role_filter)) {
    $role_safe = mysqli_real_escape_string($conn, $role_filter);
    $where_conditions[] = "role = '$role_safe'";
}

// Combine conditions
$where_clause = "";
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $items_per_page;

// Count total admins for pagination
$count_sql = "SELECT COUNT(*) as total FROM admin_users $where_clause";
$count_result = mysqli_query($conn, $count_sql);
$total_items = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get admins with pagination
$admins_sql = "SELECT id, username, email, role, created_at, last_login
              FROM admin_users $where_clause
              ORDER BY created_at DESC
              LIMIT $items_per_page OFFSET $offset";
$admins_result = mysqli_query($conn, $admins_sql);

// Process add/edit admin form
$message = '';
$message_type = '';

// Add new admin
if (isset($_POST['add_admin'])) {
    $new_username = mysqli_real_escape_string($conn, $_POST['username']);
    $new_email = mysqli_real_escape_string($conn, $_POST['email']);
    $new_password = mysqli_real_escape_string($conn, $_POST['password']);
    $new_role = mysqli_real_escape_string($conn, $_POST['role']);
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Validate input
    $errors = [];

    if (empty($new_username)) {
        $errors[] = "Username is required";
    } else {
        // Check if username already exists
        $check_username = "SELECT id FROM admin_users WHERE username = '$new_username'";
        $result_username = mysqli_query($conn, $check_username);
        if (mysqli_num_rows($result_username) > 0) {
            $errors[] = "Username already exists";
        }
    }

    if (empty($new_email)) {
        $errors[] = "Email is required";
    } else if (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    } else {
        // Check if email already exists
        $check_email = "SELECT id FROM admin_users WHERE email = '$new_email'";
        $result_email = mysqli_query($conn, $check_email);
        if (mysqli_num_rows($result_email) > 0) {
            $errors[] = "Email already exists";
        }
    }

    if (empty($new_password)) {
        $errors[] = "Password is required";
    } else if (strlen($new_password) < 6) {
        $errors[] = "Password must be at least 6 characters";
    }

    if (empty($new_role)) {
        $errors[] = "Role is required";
    }

    // If no errors, add new admin
    if (empty($errors)) {
        $hashed_password = md5($new_password); // Use MD5 for password hashing to match existing system

        $insert_sql = "INSERT INTO admin_users (username, email, password, role, created_at)
                      VALUES ('$new_username', '$new_email', '$hashed_password', '$new_role', NOW())";

        if (mysqli_query($conn, $insert_sql)) {
            $new_admin_id = mysqli_insert_id($conn);
            $message = "Admin user added successfully";
            $message_type = 'success';

            // If AJAX request, return JSON with the new staff member data
            if ($is_ajax) {
                // Get the newly created admin user data
                $new_admin_sql = "SELECT id, username, email, role, created_at, last_login
                                  FROM admin_users WHERE id = $new_admin_id";
                $new_admin_result = mysqli_query($conn, $new_admin_sql);
                $new_admin = mysqli_fetch_assoc($new_admin_result);

                $response = [
                    'success' => true,
                    'message' => $message,
                    'admin' => [
                        'id' => $new_admin['id'],
                        'username' => $new_admin['username'],
                        'email' => $new_admin['email'],
                        'role' => $new_admin['role'],
                        'created_at' => date('Y-m-d H:i', strtotime($new_admin['created_at'])),
                        'last_login' => $new_admin['last_login'] ? date('Y-m-d H:i', strtotime($new_admin['last_login'])) : 'Never'
                    ]
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error adding admin user: " . mysqli_error($conn);
            $message_type = 'danger';

            // If AJAX request, return JSON error
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'danger';

        // If AJAX request, return JSON error
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}

// Edit admin
if (isset($_POST['edit_admin'])) {
    $edit_id = (int)$_POST['edit_id'];
    $edit_email = mysqli_real_escape_string($conn, $_POST['edit_email']);
    $edit_role = mysqli_real_escape_string($conn, $_POST['edit_role']);
    $edit_password = $_POST['edit_password'];
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Get admin info before update (for AJAX response)
    $admin_info = null;
    if ($is_ajax) {
        $admin_query = "SELECT username FROM admin_users WHERE id = $edit_id";
        $admin_result = mysqli_query($conn, $admin_query);
        if (mysqli_num_rows($admin_result) > 0) {
            $admin_info = mysqli_fetch_assoc($admin_result);
        }
    }

    // Validate input
    $errors = [];

    if (empty($edit_email)) {
        $errors[] = "Email is required";
    } else if (!filter_var($edit_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    } else {
        // Check if email already exists for other users
        $check_email = "SELECT id FROM admin_users WHERE email = '$edit_email' AND id != $edit_id";
        $result_email = mysqli_query($conn, $check_email);
        if (mysqli_num_rows($result_email) > 0) {
            $errors[] = "Email already exists";
        }
    }

    if (empty($edit_role)) {
        $errors[] = "Role is required";
    }

    // If no errors, update admin
    if (empty($errors)) {
        if (!empty($edit_password)) {
            // Update with new password
            $hashed_password = md5($edit_password);
            $update_sql = "UPDATE admin_users SET
                          email = '$edit_email',
                          password = '$hashed_password',
                          role = '$edit_role'
                          WHERE id = $edit_id";
        } else {
            // Update without changing password
            $update_sql = "UPDATE admin_users SET
                          email = '$edit_email',
                          role = '$edit_role'
                          WHERE id = $edit_id";
        }

        if (mysqli_query($conn, $update_sql)) {
            $message = "Admin user updated successfully";
            $message_type = 'success';

            // If AJAX request, return JSON success with updated data
            if ($is_ajax) {
                // Get the updated admin user data
                $updated_admin_sql = "SELECT id, username, email, role, created_at, last_login
                                     FROM admin_users WHERE id = $edit_id";
                $updated_admin_result = mysqli_query($conn, $updated_admin_sql);
                $updated_admin = mysqli_fetch_assoc($updated_admin_result);

                $response = [
                    'success' => true,
                    'message' => $message,
                    'admin' => [
                        'id' => $updated_admin['id'],
                        'username' => $updated_admin['username'],
                        'email' => $updated_admin['email'],
                        'role' => $updated_admin['role'],
                        'created_at' => date('Y-m-d H:i', strtotime($updated_admin['created_at'])),
                        'last_login' => $updated_admin['last_login'] ? date('Y-m-d H:i', strtotime($updated_admin['last_login'])) : 'Never'
                    ]
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error updating admin user: " . mysqli_error($conn);
            $message_type = 'danger';

            // If AJAX request, return JSON error
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'danger';

        // If AJAX request, return JSON error
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}

// Delete admin
if (isset($_POST['delete_admin'])) {
    $delete_id = (int)$_POST['delete_id'];
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Get admin info before deletion (for AJAX response)
    $admin_info = null;
    if ($is_ajax) {
        $admin_query = "SELECT username FROM admin_users WHERE id = $delete_id";
        $admin_result = mysqli_query($conn, $admin_query);
        if (mysqli_num_rows($admin_result) > 0) {
            $admin_info = mysqli_fetch_assoc($admin_result);
        }
    }

    // Don't allow deleting yourself
    if ($delete_id == $admin_id) {
        $message = "You cannot delete your own account";
        $message_type = 'danger';

        // If AJAX request, return JSON error
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    } else {
        $delete_sql = "DELETE FROM admin_users WHERE id = $delete_id";

        if (mysqli_query($conn, $delete_sql)) {
            $message = "Admin user deleted successfully";
            $message_type = 'success';

            // If AJAX request, return JSON success
            if ($is_ajax) {
                $response = [
                    'success' => true,
                    'message' => $message,
                    'admin_id' => $delete_id,
                    'admin_username' => $admin_info ? $admin_info['username'] : ''
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error deleting admin user: " . mysqli_error($conn);
            $message_type = 'danger';

            // If AJAX request, return JSON error
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Staff Management</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
            height: calc(100vh - 90px);
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 15px;
        }

        .admin-sidebar ul {
            display: flex;
            flex-wrap: wrap;
        }

        .admin-sidebar ul li {
            margin-right: 5px;
            margin-bottom: 5px;
            width: calc(50% - 5px);
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
            text-align: center;
        }

        .admin-sidebar ul li a i {
            margin-right: 5px;
            width: auto;
        }
    }

    @media (max-width: 480px) {
        .admin-sidebar ul li {
            width: 100%;
            margin-right: 0;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
            height: calc(100vh - 90px);
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
            margin-bottom: 20px;
        }
    }

    @media (max-width: 480px) {
        .admin-content {
            padding: 10px;
        }
    }

    .filter-row {
        margin-bottom: 20px;
    }

    /* Table styles */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 20px;
    }

    /* Responsive table styles */
    @media (max-width: 991px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.6rem 0.4rem;
        }
    }

    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.3rem;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 12px;
        }

        .table th,
        .table td {
            padding: 0.3rem 0.2rem;
        }
    }

    /* Search box and filter styles */
    .search-box {
        width: 100%;
    }

    .search-box .input-group {
        width: 100%;
        max-width: 450px;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-select {
        font-size: 14px;
        height: 38px;
    }

    /* Responsive search and filters */
    @media (max-width: 991px) {
        .search-box .input-group {
            max-width: 100%;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 767px) {
        .search-input {
            font-size: 13px;
        }

        .filter-select {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .search-input::placeholder {
            font-size: 12px;
        }

        .filter-select {
            font-size: 12px;
        }
    }

    /* Pagination styles */
    .pagination-container {
        margin-top: 20px;
    }

    .pagination .page-link {
        color: #473BF0;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    @media (max-width: 767px) {
        .pagination {
            font-size: 14px;
        }

        .pagination .page-link {
            padding: 0.3rem 0.6rem;
        }
    }

    @media (max-width: 480px) {
        .pagination {
            font-size: 12px;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
        }
    }

    /* Badge styles */
    .badge {
        font-size: 14px;
        padding: 5px 8px;
        border-radius: 4px;
        font-weight: 500;
    }

    @media (max-width: 767px) {
        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }
    }

    @media (max-width: 480px) {
        .badge {
            font-size: 11px;
            padding: 3px 5px;
        }
    }

    .modal-header {
        background-color: #473BF0;
        color: white;
    }

    .modal-header .close {
        color: white;
    }

    .modal-title {
        font-size: 22px;
        color: white;
        font-weight: 500;
    }

    .form-group label {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
    }

    @media (max-width: 767px) {
        .form-group label {
            font-size: 14px;
        }
    }

    /* .badge-admin {
            background-color: #dc3545;
        }

        .badge-manager {
            background-color: #fd7e14;
        }

        .badge-support {
            background-color: #28a745;
        }

        .badge-superadmin {
            background-color: #6610f2;
        } */

    .badge-admin,
    .badge-manager,
    .badge-support,
    .badge-superadmin {
        font-size: 16px;
    }

    /* Create New Staff button style - matching my-ticket.php */
    .create-staff-btn {
        background-color: #473BF0 !important;
        color: white !important;
        padding: 0.5em 1em !important;
        font-size: 0.9em !important;
        border-radius: 5px !important;
        text-decoration: none !important;
        border: none !important;
        transition: all 0.3s ease !important;
    }

    .create-staff-btn:hover {
        background-color: #3a30c0 !important;
        color: white !important;
    }

    /* Modal button styles - matching admin-ticket-detail.php */
    .modal-btn-primary {
        background-color: #473BF0 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
    }

    .modal-btn-primary:hover {
        background-color: #3a30c0 !important;
    }

    .modal-btn-cancel {
        background-color: #dc3545 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
    }

    .modal-btn-cancel:hover {
        background-color: #c82333 !important;
    }

    /* Password toggle eye icon styling */
    .toggle-password {
        cursor: pointer;
        border-left: none;
    }

    .toggle-password:hover {
        color: #473BF0;
    }

    .input-group-text {
        background-color: #fff;
        border-left: 0;
        height: 50px;
    }

    .input-group .form-control:focus+.input-group-append .input-group-text {
        border-color: #80bdff;
    }

    /* Action button styles - 30x30 square buttons */
    .btn-action {
        width: 30px !important;
        height: 30px !important;
        min-width: 30px !important;
        max-width: 30px !important;
        min-height: 30px !important;
        max-height: 30px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        margin: 2px !important;
    }

    .btn-action i {
        margin: 0 !important;
    }

    .btn-action-primary {
        background-color: #473BF0 !important;
        color: white !important;
        border: none !important;
    }

    .btn-action-primary:hover {
        background-color: #3a30c0 !important;
    }

    .btn-action-danger {
        background-color: #dc3545 !important;
        color: white !important;
        border: none !important;
    }

    .btn-action-danger:hover {
        background-color: #c82333 !important;
    }

    /* Responsive styles for the button */
    @media (max-width: 767px) {
        .create-staff-btn {
            font-size: 0.85em !important;
            padding: 0.45em 0.9em !important;
        }

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 13px !important;
            padding: 7px 14px !important;
        }

        .btn-action {
            width: 28px !important;
            height: 28px !important;
            min-width: 28px !important;
            max-width: 28px !important;
            min-height: 28px !important;
            max-height: 28px !important;
            font-size: 12px !important;
        }
    }

    @media (max-width: 575px) {
        .create-staff-btn {
            display: block;
            width: 100%;
            margin-bottom: 20px;
            font-size: 0.8em !important;
            padding: 0.4em 0.8em !important;
        }

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 12px !important;
            padding: 6px 12px !important;
        }

        .btn-action {
            width: 26px !important;
            height: 26px !important;
            min-width: 26px !important;
            max-width: 26px !important;
            min-height: 26px !important;
            max-height: 26px !important;
            font-size: 11px !important;
            margin: 1px !important;
        }
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }

        .badge-admin,
        .badge-manager,
        .badge-support,
        .badge-superadmin {
            font-size: 12px;
        }
    }

    input#username,
    input#email,
    input#password,
    input#confirm_password {
        height: 50px;
    }

    input.form-control {
        height: 50px;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Staff Management</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET">
                                <div class="row">
                                    <div class="col-lg-5 col-md-12 mb-3">
                                        <div class="search-box">
                                            <div class="input-group">
                                                <input type="text" name="search" class="form-control search-input"
                                                    placeholder="Search by username or email"
                                                    value="<?php echo htmlspecialchars($search); ?>">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary search-button">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-7 col-md-12">
                                        <div class="row">
                                            <div class="col-sm-6 col-md-6 mb-2">
                                                <select name="role" class="form-control filter-select"
                                                    onchange="this.form.submit()">
                                                    <option value="">All Roles</option>
                                                    <option value="admin"
                                                        <?php echo $role_filter === 'admin' ? 'selected' : ''; ?>>
                                                        Admin</option>
                                                    <option value="manager"
                                                        <?php echo $role_filter === 'manager' ? 'selected' : ''; ?>>
                                                        Manager</option>
                                                    <option value="support"
                                                        <?php echo $role_filter === 'support' ? 'selected' : ''; ?>>
                                                        Support</option>
                                                    <?php if ($admin_role === 'superadmin'): ?>
                                                    <option value="superadmin"
                                                        <?php echo $role_filter === 'superadmin' ? 'selected' : ''; ?>>
                                                        Super Admin
                                                    </option>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4 text-right">
                            <button type="button" class="btn create-staff-btn" data-toggle="modal"
                                data-target="#addAdminModal">
                                <i class="fas fa-plus"></i>&nbsp; Create New Staff
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Created</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($admins_result) > 0): ?>
                                <?php while ($admin_user = mysqli_fetch_assoc($admins_result)): ?>
                                <tr data-admin-id="<?php echo $admin_user['id']; ?>">
                                    <td><?php echo $admin_user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($admin_user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($admin_user['email']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $admin_user['role']; ?>">
                                            <?php echo ucfirst($admin_user['role']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($admin_user['created_at'])); ?></td>
                                    <td>
                                        <?php echo $admin_user['last_login'] ? date('Y-m-d H:i', strtotime($admin_user['last_login'])) : 'Never'; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-action btn-action-primary"
                                                data-toggle="modal"
                                                data-target="#editAdminModal<?php echo $admin_user['id']; ?>"
                                                title="Edit Staff">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($admin_user['id'] != $admin_id): ?>
                                            <button type="button" class="btn btn-action btn-action-danger"
                                                data-toggle="modal"
                                                data-target="#deleteAdminModal<?php echo $admin_user['id']; ?>"
                                                title="Delete Staff">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Edit Admin Modal -->
                                <div class="modal fade" id="editAdminModal<?php echo $admin_user['id']; ?>"
                                    tabindex="-1" role="dialog"
                                    aria-labelledby="editAdminModalLabel<?php echo $admin_user['id']; ?>"
                                    aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title"
                                                    id="editAdminModalLabel<?php echo $admin_user['id']; ?>">Edit Staff:
                                                    <?php echo htmlspecialchars($admin_user['username']); ?></h5>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form id="editAdminForm<?php echo $admin_user['id']; ?>" method="POST">
                                                <div class="modal-body">
                                                    <div class="alert alert-danger mt-3 edit-error-message"
                                                        style="display: none;"></div>

                                                    <input type="hidden" name="edit_id"
                                                        value="<?php echo $admin_user['id']; ?>">
                                                    <input type="hidden" name="edit_admin" value="1">
                                                    <input type="hidden" name="ajax" value="1">

                                                    <div class="form-group">
                                                        <label>Username</label>
                                                        <input type="text" class="form-control"
                                                            value="<?php echo htmlspecialchars($admin_user['username']); ?>"
                                                            readonly>
                                                        <small class="form-text text-muted">Username cannot be
                                                            changed</small>
                                                    </div>

                                                    <div class="form-group">
                                                        <label
                                                            for="edit_email<?php echo $admin_user['id']; ?>">Email</label>
                                                        <input type="email" class="form-control"
                                                            id="edit_email<?php echo $admin_user['id']; ?>"
                                                            name="edit_email"
                                                            value="<?php echo htmlspecialchars($admin_user['email']); ?>"
                                                            required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label
                                                            for="edit_password<?php echo $admin_user['id']; ?>">Password</label>
                                                        <div class="input-group">
                                                            <input type="password" class="form-control"
                                                                id="edit_password<?php echo $admin_user['id']; ?>"
                                                                name="edit_password">
                                                            <div class="input-group-append">
                                                                <span class="input-group-text toggle-password"
                                                                    toggle="#edit_password<?php echo $admin_user['id']; ?>">
                                                                    <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <small class="form-text text-muted">Leave blank to keep current
                                                            password</small>
                                                    </div>

                                                    <div class="form-group">
                                                        <label
                                                            for="edit_role<?php echo $admin_user['id']; ?>">Role</label>
                                                        <select class="form-control"
                                                            id="edit_role<?php echo $admin_user['id']; ?>"
                                                            name="edit_role" required>
                                                            <option value="support"
                                                                <?php echo $admin_user['role'] === 'support' ? 'selected' : ''; ?>>
                                                                Support</option>
                                                            <option value="manager"
                                                                <?php echo $admin_user['role'] === 'manager' ? 'selected' : ''; ?>>
                                                                Manager</option>
                                                            <option value="admin"
                                                                <?php echo $admin_user['role'] === 'admin' ? 'selected' : ''; ?>>
                                                                Admin</option>
                                                            <?php if ($admin_role === 'superadmin'): ?>
                                                            <option value="superadmin"
                                                                <?php echo $admin_user['role'] === 'superadmin' ? 'selected' : ''; ?>>
                                                                Super Admin</option>
                                                            <?php endif; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn modal-btn-cancel"
                                                        data-dismiss="modal">Cancel</button>
                                                    <button type="button" class="btn modal-btn-primary edit-admin-btn"
                                                        data-admin-id="<?php echo $admin_user['id']; ?>">Save
                                                        Changes</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Admin Modal -->
                                <div class="modal fade" id="deleteAdminModal<?php echo $admin_user['id']; ?>"
                                    tabindex="-1" role="dialog"
                                    aria-labelledby="deleteAdminModalLabel<?php echo $admin_user['id']; ?>"
                                    aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger text-white">
                                                <h5 class="modal-title"
                                                    id="deleteAdminModalLabel<?php echo $admin_user['id']; ?>">Confirm
                                                    Delete</h5>
                                                <button type="button" class="close text-white" data-dismiss="modal"
                                                    aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form id="deleteAdminForm<?php echo $admin_user['id']; ?>" method="POST">
                                                <div class="modal-body">
                                                    <input type="hidden" name="delete_id"
                                                        value="<?php echo $admin_user['id']; ?>">
                                                    <input type="hidden" name="delete_admin" value="1">
                                                    <input type="hidden" name="ajax" value="1">
                                                    <p>Are you sure you want to delete the staff member
                                                        <strong><?php echo htmlspecialchars($admin_user['username']); ?></strong>?
                                                    </p>
                                                    <p class="text-danger">This action cannot be undone.</p>
                                                    <div class="alert alert-danger mt-3 delete-error-message"
                                                        style="display: none;"></div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn modal-btn-cancel"
                                                        data-dismiss="modal">Cancel</button>
                                                    <button type="button" class="btn modal-btn-primary delete-admin-btn"
                                                        data-admin-id="<?php echo $admin_user['id']; ?>">Delete</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No staff members found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <div class="pagination-container mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>"
                                        aria-label="Previous">
                                        <span aria-hidden="true">&laquo; Previous</span>
                                    </a>
                                </li>

                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link"
                                        href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>"
                                        aria-label="Next">
                                        <span aria-hidden="true">Next &raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Admin Modal -->
    <div class="modal fade" id="addAdminModal" tabindex="-1" role="dialog" aria-labelledby="addAdminModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAdminModalLabel">Add New Admin Member</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="addAdminForm" method="POST">
                    <div class="modal-body">
                        <div class="alert alert-danger mt-3 add-error-message" style="display: none;"></div>

                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="input-group-append">
                                    <span class="input-group-text toggle-password" toggle="#password">
                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                    </span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Password must be at least 6 characters</small>
                        </div>

                        <div class="form-group">
                            <label for="role">Role</label>
                            <select class="form-control" id="role" name="role" required>
                                <option value="support">Support</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Admin</option>
                                <?php if ($admin_role === 'superadmin'): ?>
                                <option value="superadmin">Super Admin</option>
                                <?php endif; ?>
                            </select>
                        </div>

                        <input type="hidden" name="add_admin" value="1">
                        <input type="hidden" name="ajax" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                        <button type="button" id="addAdminBtn" class="btn modal-btn-primary">Add Staff</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });

        // Handle admin edit via AJAX
        const editAdminButtons = document.querySelectorAll('.edit-admin-btn');
        editAdminButtons.forEach(button => {
            button.addEventListener('click', function() {
                const adminId = this.getAttribute('data-admin-id');
                const form = document.getElementById('editAdminForm' + adminId);
                const errorMessage = form.querySelector('.edit-error-message');
                const modal = jQuery('#editAdminModal' + adminId);

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Saving...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(window.location.pathname, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - update the admin row in the table
                            const adminRow = document.querySelector('tr[data-admin-id="' +
                                adminId + '"]');
                            if (adminRow) {
                                // Update email cell
                                const emailCell = adminRow.cells[2];
                                if (emailCell) {
                                    emailCell.textContent = data.admin.email;
                                }

                                // Update role cell
                                const roleCell = adminRow.cells[3];
                                if (roleCell) {
                                    const roleBadge = document.createElement('span');
                                    roleBadge.className = 'badge badge-' + data.admin.role;
                                    roleBadge.textContent = data.admin.role.charAt(0)
                                        .toUpperCase() + data.admin.role.slice(1);
                                    roleCell.innerHTML = '';
                                    roleCell.appendChild(roleBadge);
                                }
                            }

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Save Changes';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Save Changes';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        });

        // Handle admin deletion via AJAX
        const deleteAdminButtons = document.querySelectorAll('.delete-admin-btn');
        deleteAdminButtons.forEach(button => {
            button.addEventListener('click', function() {
                const adminId = this.getAttribute('data-admin-id');
                const form = document.getElementById('deleteAdminForm' + adminId);
                const errorMessage = form.querySelector('.delete-error-message');
                const modal = jQuery('#deleteAdminModal' + adminId);

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(window.location.pathname, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - remove the admin row from the table
                            const adminRow = document.querySelector('tr[data-admin-id="' +
                                adminId + '"]');
                            if (adminRow) {
                                adminRow.remove();
                            }

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');

                            // If no admins left, show the "No staff members found" message
                            const tableBody = document.querySelector('tbody');
                            if (tableBody.querySelectorAll('tr').length === 0) {
                                const noAdminsRow = document.createElement('tr');
                                noAdminsRow.innerHTML =
                                    '<td colspan="7" class="text-center">No staff members found</td>';
                                tableBody.appendChild(noAdminsRow);
                            }
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Delete';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Delete';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        });

        // Handle adding new admin via AJAX
        const addAdminBtn = document.getElementById('addAdminBtn');
        if (addAdminBtn) {
            addAdminBtn.addEventListener('click', function() {
                const form = document.getElementById('addAdminForm');
                const errorMessage = form.querySelector('.add-error-message');
                const modal = jQuery('#addAdminModal');

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Adding...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(window.location.pathname, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - add the new admin row to the table
                            const tableBody = document.querySelector('tbody');

                            // Remove "No staff members found" row if it exists
                            const noAdminsRow = tableBody.querySelector('tr td[colspan="7"]');
                            if (noAdminsRow) {
                                noAdminsRow.parentElement.remove();
                            }

                            // Create new row
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-admin-id', data.admin.id);

                            // Format the role for badge
                            const roleBadge =
                                `<span class="badge badge-${data.admin.role}">${data.admin.role.charAt(0).toUpperCase() + data.admin.role.slice(1)}</span>`;

                            // Create row HTML
                            newRow.innerHTML = `
                            <td>${data.admin.id}</td>
                            <td>${data.admin.username}</td>
                            <td>${data.admin.email}</td>
                            <td>${roleBadge}</td>
                            <td>${data.admin.created_at}</td>
                            <td>${data.admin.last_login}</td>
                            <td>
                                <div class="d-flex">
                                    <button type="button" class="btn btn-action btn-action-primary" data-toggle="modal" data-target="#editAdminModal${data.admin.id}" title="Edit Staff">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-action btn-action-danger" data-toggle="modal" data-target="#deleteAdminModal${data.admin.id}" title="Delete Staff">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        `;

                            // Add row to table
                            tableBody.insertBefore(newRow, tableBody.firstChild);

                            // Reset form
                            form.reset();

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');

                            // Create the edit and delete modals for the new staff member
                            createStaffModals(data.admin);
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Add Staff';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Add Staff';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        }

        // Function to create edit and delete modals for a new staff member
        function createStaffModals(admin) {
            // 1. Create Edit Modal
            const editModalId = `editAdminModal${admin.id}`;
            const editModal = document.createElement('div');
            editModal.className = 'modal fade';
            editModal.id = editModalId;
            editModal.setAttribute('tabindex', '-1');
            editModal.setAttribute('role', 'dialog');
            editModal.setAttribute('aria-labelledby', `editAdminModalLabel${admin.id}`);
            editModal.setAttribute('aria-hidden', 'true');

            // Get current admin role for checking superadmin option
            const currentAdminRole = '<?php echo $admin_role; ?>';

            // Create the edit modal HTML
            editModal.innerHTML = `
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editAdminModalLabel${admin.id}">Edit Staff: ${admin.username}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form id="editAdminForm${admin.id}" method="POST">
                            <div class="modal-body">
                                <div class="alert alert-danger mt-3 edit-error-message" style="display: none;"></div>

                                <input type="hidden" name="edit_id" value="${admin.id}">
                                <input type="hidden" name="edit_admin" value="1">
                                <input type="hidden" name="ajax" value="1">

                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" class="form-control" value="${admin.username}" readonly>
                                    <small class="form-text text-muted">Username cannot be changed</small>
                                </div>

                                <div class="form-group">
                                    <label for="edit_email${admin.id}">Email</label>
                                    <input type="email" class="form-control" id="edit_email${admin.id}" name="edit_email" value="${admin.email}" required>
                                </div>

                                <div class="form-group">
                                    <label for="edit_password${admin.id}">Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="edit_password${admin.id}" name="edit_password">
                                        <div class="input-group-append">
                                            <span class="input-group-text toggle-password" toggle="#edit_password${admin.id}">
                                                <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Leave blank to keep current password</small>
                                </div>

                                <div class="form-group">
                                    <label for="edit_role${admin.id}">Role</label>
                                    <select class="form-control" id="edit_role${admin.id}" name="edit_role" required>
                                        <option value="support" ${admin.role === 'support' ? 'selected' : ''}>Support</option>
                                        <option value="manager" ${admin.role === 'manager' ? 'selected' : ''}>Manager</option>
                                        <option value="admin" ${admin.role === 'admin' ? 'selected' : ''}>Admin</option>
                                        ${currentAdminRole === 'superadmin' ?
                                            `<option value="superadmin" ${admin.role === 'superadmin' ? 'selected' : ''}>Super Admin</option>` : ''}
                                    </select>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn modal-btn-primary edit-admin-btn" data-admin-id="${admin.id}">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // 2. Create Delete Modal
            const deleteModalId = `deleteAdminModal${admin.id}`;
            const deleteModal = document.createElement('div');
            deleteModal.className = 'modal fade';
            deleteModal.id = deleteModalId;
            deleteModal.setAttribute('tabindex', '-1');
            deleteModal.setAttribute('role', 'dialog');
            deleteModal.setAttribute('aria-labelledby', `deleteAdminModalLabel${admin.id}`);
            deleteModal.setAttribute('aria-hidden', 'true');

            // Create the delete modal HTML
            deleteModal.innerHTML = `
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="deleteAdminModalLabel${admin.id}">Confirm Delete</h5>
                            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <form id="deleteAdminForm${admin.id}" method="POST">
                            <div class="modal-body">
                                <input type="hidden" name="delete_id" value="${admin.id}">
                                <input type="hidden" name="delete_admin" value="1">
                                <input type="hidden" name="ajax" value="1">
                                <p>Are you sure you want to delete the staff member <strong>${admin.username}</strong>?</p>
                                <p class="text-danger">This action cannot be undone.</p>
                                <div class="alert alert-danger mt-3 delete-error-message" style="display: none;"></div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn modal-btn-primary delete-admin-btn" data-admin-id="${admin.id}">Delete</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // 3. Add the modals to the document
            document.body.appendChild(editModal);
            document.body.appendChild(deleteModal);

            // 4. Initialize the edit button event listener for the new modal
            const editBtn = editModal.querySelector('.edit-admin-btn');
            if (editBtn) {
                editBtn.addEventListener('click', function() {
                    const adminId = this.getAttribute('data-admin-id');
                    const form = document.getElementById('editAdminForm' + adminId);
                    const errorMessage = form.querySelector('.edit-error-message');
                    const modal = jQuery('#editAdminModal' + adminId);

                    // Validate form
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }

                    // Show loading state
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Saving...';
                    this.disabled = true;

                    // Create form data
                    const formData = new FormData(form);

                    // Send AJAX request
                    fetch(window.location.pathname, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Success - update the admin row in the table
                                const adminRow = document.querySelector('tr[data-admin-id="' +
                                    adminId + '"]');
                                if (adminRow) {
                                    // Update email cell
                                    const emailCell = adminRow.cells[2];
                                    if (emailCell) {
                                        emailCell.textContent = data.admin.email;
                                    }

                                    // Update role cell
                                    const roleCell = adminRow.cells[3];
                                    if (roleCell) {
                                        const roleBadge = document.createElement('span');
                                        roleBadge.className = 'badge badge-' + data.admin.role;
                                        roleBadge.textContent = data.admin.role.charAt(0)
                                            .toUpperCase() + data.admin.role.slice(1);
                                        roleCell.innerHTML = '';
                                        roleCell.appendChild(roleBadge);
                                    }
                                }

                                // Close the modal
                                modal.modal('hide');

                                // Show success message
                                showNotification(data.message, 'success');
                            } else {
                                // Error
                                errorMessage.textContent = data.message;
                                errorMessage.style.display = 'block';

                                // Reset button
                                this.innerHTML = 'Save Changes';
                                this.disabled = false;
                            }
                        })
                        .catch(error => {
                            // Network or other error
                            errorMessage.textContent =
                                'An error occurred while processing your request. Please try again.';
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Save Changes';
                            this.disabled = false;

                            console.error('Error:', error);
                        });
                });
            }

            // 5. Initialize the delete button event listener for the new modal
            const deleteBtn = deleteModal.querySelector('.delete-admin-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    const adminId = this.getAttribute('data-admin-id');
                    const form = document.getElementById('deleteAdminForm' + adminId);
                    const errorMessage = form.querySelector('.delete-error-message');
                    const modal = jQuery('#deleteAdminModal' + adminId);

                    // Show loading state
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
                    this.disabled = true;

                    // Create form data
                    const formData = new FormData(form);

                    // Send AJAX request
                    fetch(window.location.pathname, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Success - remove the admin row from the table
                                const adminRow = document.querySelector('tr[data-admin-id="' +
                                    adminId + '"]');
                                if (adminRow) {
                                    adminRow.remove();
                                }

                                // Close the modal
                                modal.modal('hide');

                                // Show success message
                                showNotification(data.message, 'success');

                                // If no admins left, show the "No staff members found" message
                                const tableBody = document.querySelector('tbody');
                                if (tableBody.querySelectorAll('tr').length === 0) {
                                    const noAdminsRow = document.createElement('tr');
                                    noAdminsRow.innerHTML =
                                        '<td colspan="7" class="text-center">No staff members found</td>';
                                    tableBody.appendChild(noAdminsRow);
                                }
                            } else {
                                // Error
                                errorMessage.textContent = data.message;
                                errorMessage.style.display = 'block';

                                // Reset button
                                this.innerHTML = 'Delete';
                                this.disabled = false;
                            }
                        })
                        .catch(error => {
                            // Network or other error
                            errorMessage.textContent =
                                'An error occurred while processing your request. Please try again.';
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Delete';
                            this.disabled = false;

                            console.error('Error:', error);
                        });
                });
            }
        }

        // Toggle password visibility (using event delegation for dynamically created elements)
        $(document).on('click', '.toggle-password', function() {
            // Toggle the eye icon
            $(this).find('i').toggleClass('fa-eye-slash fa-eye');

            // Toggle the password field type
            var input = $($(this).attr('toggle'));
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
            } else {
                input.attr('type', 'password');
            }
        });

        // Add hover effect to the eye icon (using event delegation)
        $(document).on('mouseenter', '.toggle-password', function() {
            $(this).css('cursor', 'pointer');
        });

        // Function to show notification
        function showNotification(message, type, duration = 5000) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'alert alert-' + type + ' alert-dismissible fade show notification-toast';
            notification.innerHTML = message +
                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                '<span aria-hidden="true">&times;</span></button>';

            // Style the notification
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';

            // Add to document
            document.body.appendChild(notification);

            // Auto remove after specified duration
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 150);
            }, duration);
        }
    });
    </script>
</body>

</html>