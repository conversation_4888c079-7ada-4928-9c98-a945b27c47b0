<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Handle API key updates
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_keys'])) {
        $customerApiKey = trim($_POST['customer_api_key']);
        $graphqlApiKey = trim($_POST['graphql_api_key']);

        if (!empty($customerApiKey) && !empty($graphqlApiKey)) {
            // Update API keys in the configuration file
            $configContent = "<?php
// Centralized API Key Configuration
// Last updated: " . date('Y-m-d H:i:s') . " by {$admin_username}

// Customer API Configuration (Appika Customer API)
define('CUSTOMER_API_ENDPOINT', 'https://dev-api-pooh-sgsg.appika.com');
define('CUSTOMER_API_PATH', '/contact/customers');
define('CUSTOMER_API_KEY', '{$customerApiKey}');

// GraphQL Ticket API Configuration
define('GRAPHQL_API_ENDPOINT', 'https://dev-sgsg-tktapi.appika.com/graphql');
define('GRAPHQL_API_KEY', '{$graphqlApiKey}');

// Function to get Customer API configuration
function getCustomerApiConfig() {
    return [
        'endpoint' => CUSTOMER_API_ENDPOINT,
        'path' => CUSTOMER_API_PATH,
        'key' => CUSTOMER_API_KEY
    ];
}

// Function to get GraphQL API configuration
function getGraphqlApiConfig() {
    return [
        'endpoint' => GRAPHQL_API_ENDPOINT,
        'key' => GRAPHQL_API_KEY
    ];
}
?>";

// Write to config file
$configFile = '../config/api-config.php';

// Create config directory if it doesn't exist
if (!file_exists('../config')) {
mkdir('../config', 0755, true);
}

if (file_put_contents($configFile, $configContent)) {
$message = 'API keys updated successfully!';
$messageType = 'success';

// Log the update
if (!file_exists('../logs')) {
mkdir('../logs', 0755, true);
}
$logEntry = date('Y-m-d H:i:s') . " - API keys updated by admin: {$admin_username}\n";
file_put_contents('../logs/api_key_updates.log', $logEntry, FILE_APPEND | LOCK_EX);
} else {
$message = 'Failed to update API keys. Please check file permissions.';
$messageType = 'danger';
}
} else {
$message = 'Please fill in both API keys.';
$messageType = 'warning';
}
}
}

// Load current API keys
$currentCustomerKey = '';
$currentGraphqlKey = '';

if (file_exists('../config/api-config.php')) {
include('../config/api-config.php');
$currentCustomerKey = defined('CUSTOMER_API_KEY') ? CUSTOMER_API_KEY : '';
$currentGraphqlKey = defined('GRAPHQL_API_KEY') ? GRAPHQL_API_KEY : '';
} else {
// Default keys if config file doesn't exist
$currentCustomerKey = 'NWViNWRhZmQ4ZWMxZmMzODA4MGIzZmQzNGE2ZmRhY2U4MDI2NWJjMWExMTFhNzFiMDM3M2I5OTQ1OTk5NDVlYg';
$currentGraphqlKey = 'NWViNWRhZmQ4ZWMxZmMzODA4MGIzZmQzNGE2ZmRhY2U4MDI2NWJjMWExMTFhNzFiMDM3M2I5OTQ1OTk5NDVlYg';
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - API Key Manager</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .api-key-input {
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }

    .key-preview {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        word-break: break-all;
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .admin-header h1 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }

        .admin-content {
            height: auto;
            padding: 12px;
            margin-bottom: 20px;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-key"></i> API Key Manager</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <h2><i class="fas fa-key"></i> Centralized API Key Management</h2>
                    <p class="text-muted">Manage all API keys used across the system from one location</p>

                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0" style="color: white;"><i class="fas fa-users"></i> Customer API
                                            Key</h5>
                                        <small>Used for Appika Customer API operations</small>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="customer_api_key">API Key:</label>
                                            <textarea class="form-control api-key-input" id="customer_api_key"
                                                name="customer_api_key" rows="3" placeholder="Enter Customer API Key"
                                                required><?php echo htmlspecialchars($currentCustomerKey); ?></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>Current Key Preview:</label>
                                            <div class="key-preview">
                                                <?php echo $currentCustomerKey ? substr($currentCustomerKey, 0, 20) . '...' . substr($currentCustomerKey, -10) : 'No key set'; ?>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Endpoint:</label>
                                            <div class="key-preview">
                                                https://dev-api-pooh-sgsg.appika.com/contact/customers
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0" style="color: white;"><i class="fas fa-ticket-alt"></i> GraphQL
                                            Ticket API Key</h5>
                                        <small>Used for GraphQL Ticket API operations</small>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="graphql_api_key">API Key:</label>
                                            <textarea class="form-control api-key-input" id="graphql_api_key"
                                                name="graphql_api_key" rows="3" placeholder="Enter GraphQL API Key"
                                                required><?php echo htmlspecialchars($currentGraphqlKey); ?></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>Current Key Preview:</label>
                                            <div class="key-preview">
                                                <?php echo $currentGraphqlKey ? substr($currentGraphqlKey, 0, 20) . '...' . substr($currentGraphqlKey, -10) : 'No key set'; ?>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Endpoint:</label>
                                            <div class="key-preview">
                                                https://dev-sgsg-tktapi.appika.com/graphql
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" name="update_keys" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i>&nbsp; Update API Keys
                            </button>
                            <!-- <a href="index.php" class="btn btn-secondary btn-lg ml-3">
                                <i class="fas fa-arrow-left"></i>&nbsp; Back to Dashboard
                            </a> -->
                        </div>
                    </form>

                    <!-- Usage Information -->
                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0" style="color:white"><i class="fas fa-info-circle"></i> Usage Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><strong>Files using Customer API Key:</strong></h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-file-code text-primary"></i> functions/sign-up-db.php</li>
                                        <li><i class="fas fa-file-code text-primary"></i> merlion/admin-users.php</li>
                                        <li><i class="fas fa-file-code text-primary"></i> merlion/admin-user-detail.php
                                        </li>
                                        <li><i class="fas fa-file-code text-primary"></i> merlion/appika-customers.php
                                        </li>
                                        <li><i class="fas fa-file-code text-primary"></i> api/core_api_example.php</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><strong>Files using GraphQL API Key:</strong></h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-file-code text-success"></i> front-end/create-ticket.php
                                        </li>
                                        <li><i class="fas fa-file-code text-success"></i>
                                            merlion/create-ticket-with-graphql.php</li>
                                        <li><i class="fas fa-file-code text-success"></i> api/graphql_ticket_test.php
                                        </li>
                                        <li><i class="fas fa-file-code text-success"></i> merlion/test-graphql.php</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Important:</strong> After updating API keys here, all files will automatically
                                use the new keys.
                                No manual editing of individual files is required.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (userInfo && mobileToggle) {
            userInfo.addEventListener('click', function() {
                userDropdown.classList.toggle('active');
                mobileToggle.classList.toggle('active');
            });
        }
    });
    </script>
</body>

</html>