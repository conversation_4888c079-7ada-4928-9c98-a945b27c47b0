<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: index');
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Home</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    /* Custom modal width */
    /* Edit profile field  */
    .custom-width-modal {
        max-width: 1200px;
        /* You can adjust this value as needed */
        width: 90%;
        /* This makes it responsive */
    }

    /* Make sure the modal content has enough space */
    .custom-width-modal .modal-content {
        padding: 20px;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .custom-width-modal {
            max-width: 90%;
        }
    }

    @media (max-width: 576px) {
        .custom-width-modal {
            max-width: 95%;
            margin: 0.5rem auto;
        }

        .custom-width-modal .modal-content {
            padding: 15px;
        }
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        padding: 8px 16px;
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .custom-alert-btn:hover {
        background-color: #3b31c8;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(71, 59, 240, 0.3);
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }

    /* Success Animation Styles */
    .success-animation {
        margin: 0 auto;
        width: 100px;
        height: 100px;
    }

    .checkmark {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #4bb71b;
        stroke-miterlimit: 10;
        box-shadow: inset 0px 0px 0px #4bb71b;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 1;
        stroke-miterlimit: 2;
        stroke: #4bb71b;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {

        0%,
        100% {
            transform: none;
        }

        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 1px #4bb71b;
        }
    }

    @keyframes resetStrokeWidth {
        0% {
            stroke-width: 1;
        }

        100% {
            stroke-width: 1;
        }
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];
            $sqlshow = "SELECT username, email, starter_tickets, premium_tickets, tell, company_name, tax_id, address, address2, district, city, state, postal_code, country, first_name, last_name FROM user WHERE username = '$username'";
            $resultshow = mysqli_query($conn, $sqlshow);
            ?>
        <?php if ($resultshow && mysqli_num_rows($resultshow) > 0) :
                $user = mysqli_fetch_assoc($resultshow);

                $sqlTickets = "SELECT SUM(remaining_tickets) AS total_remaining_tickets
                       FROM purchasetickets
                       WHERE username = '$username'
                       AND ticket_type = 'STARTER'
                       AND purchase_time >= CURDATE() - INTERVAL 1 YEAR;";

                $resultTickets = mysqli_query($conn, $sqlTickets);
                if ($resultTickets && mysqli_num_rows($resultTickets) > 0) :
                    $ticketStarterData = mysqli_fetch_assoc($resultTickets);
                    $total_starter_tickets = $ticketStarterData['total_remaining_tickets'] ?? 0;
                else :
                    $total_starter_tickets = 0;
                endif;

            ?>
        <!-- Display user data here -->
        <!-- Page Banner Area -->
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container"></div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">

                <div class="row justify-content-md-between pt-9">
                    <!-- <div class="col-sm-6 col-md-5 col-lg-4 col-xl-3">
                                <div class="btn btn-white border">PROFILE</div>
                            </div> -->
                    <div style="display: flex; flex-direction: row;" ;>
                        <div>
                            <!-- <div class="btn btn-white border"><strong>Tickets : </strong> <?php echo htmlspecialchars($total_starter_tickets); ?></div> -->
                        </div>
                        <!--
                                <div style="margin-left: 10px;">
                                    <div class="btn btn-white border"><strong>Premium Tickets:</strong></div>
                                </div>
                                -->
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="cart-details-main-block" id="dynamic-cart">

                            <!-- .cart_single-product-block starts -->
                            <!-- style="margin-top: 20px;" -->
                            <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative card mb-4">
                                <?php if (isset($user)) : ?>
                                <div class="card-body">
                                    <div class="row justify-content-center pt-1 mb-4">
                                        <div class="col-12 text-center">
                                            <h2>Profile</h2>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">First Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['first_name']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Last Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['last_name']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Username</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['username']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Email</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['email']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Phone</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['tell']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Company Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['company_name']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Tax ID</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['tax_id']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Address</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['address']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Address 2</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['address2']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">District</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['district']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">City</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['city']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">State/Province</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['state'] ?? 'N/A'); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Country</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($user['country']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Postal Code</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['postal_code']); ?></p>
                                        </div>
                                    </div>
                                    <hr>
                                </div>
                                <!-- .profile-block ends -->
                                <!-- Add this inside the card-body div -->
                                <button type="button" style="margin-top: -15px; width: 25px;" class="btn btn-primary"
                                    data-bs-toggle="modal" data-bs-target="#editProfileModal">Edit Profile</button>




                                <!-- Modal for Edit Profile -->
                                <div class="modal fade" id="editProfileModal" tabindex="-1"
                                    aria-labelledby="editProfileModalLabel" aria-hidden="true">
                                    <div class="modal-dialog custom-width-modal">
                                        <div class="modal-content">
                                            <form method="post" action="../functions/edit_profile.php"
                                                id="editProfileForm" onsubmit="return validateForm()">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="editProfileModalLabel">Edit Profile</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                        aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <!-- First Name and Last Name on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="first_name" class="form-label">First
                                                                Name</label>
                                                            <input type="text" class="form-control" id="first_name"
                                                                name="first_name"
                                                                value="<?php echo htmlspecialchars($user['first_name']); ?>"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="last_name" class="form-label">Last Name</label>
                                                            <input type="text" class="form-control" id="last_name"
                                                                name="last_name"
                                                                value="<?php echo htmlspecialchars($user['last_name']); ?>"
                                                                required>
                                                        </div>
                                                    </div>

                                                    <!-- Email and Phone on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="email" class="form-label">Email</label>
                                                            <input type="email" class="form-control" id="email"
                                                                name="email"
                                                                value="<?php echo htmlspecialchars($user['email']); ?>"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="tell" class="form-label">Phone</label>
                                                            <input type="text" class="form-control" id="tell"
                                                                name="tell"
                                                                value="<?php echo htmlspecialchars($user['tell']); ?>"
                                                                required>
                                                        </div>
                                                    </div>

                                                    <!-- Company Name and Tax ID on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="company_name" class="form-label">Company Name
                                                                (Optional)</label>
                                                            <input type="text" class="form-control" id="company_name"
                                                                name="company_name"
                                                                value="<?php echo htmlspecialchars($user['company_name']); ?>">
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="tax_id" class="form-label">Tax ID
                                                                (Optional)</label>
                                                            <input type="text" class="form-control" id="tax_id"
                                                                name="tax_id"
                                                                value="<?php echo htmlspecialchars($user['tax_id']); ?>">
                                                        </div>
                                                    </div>

                                                    <!-- Address field -->
                                                    <div class="mb-3">
                                                        <label for="address" class="form-label">Address</label>
                                                        <textarea class="form-control" id="address" name="address"
                                                            rows="2" placeholder="Enter your primary address"
                                                            required><?php echo htmlspecialchars($user['address']); ?></textarea>
                                                    </div>

                                                    <!-- Address Notes field -->
                                                    <div class="mb-3">
                                                        <label for="address2" class="form-label">Address Notes
                                                            (Optional)</label>
                                                        <textarea class="form-control" id="address2" name="address2"
                                                            rows="3"
                                                            placeholder="Enter any additional notes about your address"><?php echo htmlspecialchars($user['address2']); ?></textarea>
                                                    </div>

                                                    <!-- District and City on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="district" class="form-label">District</label>
                                                            <input type="text" class="form-control" id="district"
                                                                name="district"
                                                                value="<?php echo htmlspecialchars($user['district']); ?>"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="city" class="form-label">City</label>
                                                            <input type="text" class="form-control" id="city"
                                                                name="city"
                                                                value="<?php echo htmlspecialchars($user['city']); ?>"
                                                                required>
                                                        </div>
                                                    </div>

                                                    <!-- State/Province field -->
                                                    <div class="mb-3">
                                                        <label for="state" class="form-label">State/Province</label>
                                                        <input type="text" class="form-control" id="state" name="state"
                                                            value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                                                    </div>

                                                    <!-- Country and Postal Code on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="country" class="form-label">Country</label>
                                                            <input type="text" class="form-control" id="country"
                                                                name="country"
                                                                value="<?php echo htmlspecialchars($user['country']); ?>"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="postal_code" class="form-label">Postal
                                                                Code</label>
                                                            <input type="text" class="form-control" id="postal_code"
                                                                name="postal_code"
                                                                value="<?php echo htmlspecialchars($user['postal_code']); ?>"
                                                                required>
                                                        </div>
                                                    </div>
                                                    <!-- Add other fields as needed -->
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">Close</button>
                                                    <button type="submit" class="btn btn-primary">Save changes</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php else : ?>
                                <p><?php echo $error_message; ?></p>
                                <?php endif; ?>

                            </div>
                            <!-- .cart_single-product-block ends -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "No user found."; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "You are not logged in."; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <!-- Form Validation Script -->
    <script>
    function validateForm() {
        // Get form values
        const phoneNumber = document.getElementById('tell').value.trim();
        const taxId = document.getElementById('tax_id').value.trim();
        const postalCode = document.getElementById('postal_code').value.trim();

        // Validate phone number (must be 10 digits)
        if (phoneNumber.length !== 0) {
            // Remove any non-digit characters for validation
            const digitsOnly = phoneNumber.replace(/\D/g, '');

            if (digitsOnly.length !== 10) {
                showAlert('Phone number must be exactly 10 digits.');
                return false;
            }
        }

        // Validate Tax ID (must contain only numbers if provided)
        if (taxId.length !== 0 && !/^\d+$/.test(taxId)) {
            showAlert('Tax ID must contain only numbers.');
            return false;
        }

        // Validate Postal Code (must contain only numbers)
        if (!/^\d+$/.test(postalCode)) {
            showAlert('Postal Code must contain only numbers.');
            return false;
        }

        // If all validations pass
        return true;
    }

    // Function to show custom alert
    function showAlert(message, title = 'Warning') {
        document.getElementById('alertTitle').textContent = title;
        document.getElementById('alertMessage').textContent = message;
        document.getElementById('alertOverlay').classList.add('show');
        document.getElementById('customAlert').classList.add('show');
    }

    // Function to close custom alert
    function closeAlert() {
        document.getElementById('alertOverlay').classList.remove('show');
        document.getElementById('customAlert').classList.remove('show');
    }

    // Show success modal on page load if needed
    <?php if (isset($_GET['success']) && $_GET['success'] == 1) : ?>
    $(document).ready(function() {
        $('#successModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href.split('?')[0]);
        }

        // Start countdown for automatic redirect
        var countdownElement = document.getElementById('countdown');
        var secondsLeft = 3;

        // Update countdown every second
        var countdownInterval = setInterval(function() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            if (secondsLeft <= 0) {
                clearInterval(countdownInterval);
                window.location.href = 'profile';
            }
        }, 1000);
    });
    <?php endif; ?>

    <?php if (isset($_GET['error']) && $_GET['error'] == 1) : ?>
    $(document).ready(function() {
        $('#errorModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href.split('?')[0]);
        }
    });
    <?php endif; ?>
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="success-animation">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h3 class="mt-4">Profile Updated Successfully!</h3>
                    <p class="mb-4">Your profile information has been updated.</p>
                    <p class="text-muted">This window will close automatically in <span id="countdown">3</span> seconds.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="text-center text-danger mb-4">
                        <i class="fas fa-exclamation-circle" style="font-size: 60px;"></i>
                    </div>
                    <h3 class="mt-2">Error!</h3>
                    <p class="mb-4">There was an error updating your profile. Please try again.</p>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>