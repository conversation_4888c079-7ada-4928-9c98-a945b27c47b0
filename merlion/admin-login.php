<?php
session_start();
include('../functions/server.php');

// Check if already logged in
if (isset($_SESSION['admin_username'])) {
    header('location: index.php');
    exit();
}

$errors = array();

// Process login form
if (isset($_POST['login_admin'])) {
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $password = mysqli_real_escape_string($conn, $_POST['password']);

    // Validate form
    if (empty($username)) {
        array_push($errors, "Username is required");
    }
    if (empty($password)) {
        array_push($errors, "Password is required");
    }

    // If no errors, attempt login
    if (count($errors) == 0) {
        $password = md5($password); // Encrypt password
        $query = "SELECT * FROM admin_users WHERE username = '$username' AND password = '$password'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) == 1) {
            $admin = mysqli_fetch_assoc($result);

            // Set session variables
            $_SESSION['admin_username'] = $username;
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_role'] = $admin['role'];

            // Update last login time
            $admin_id = $admin['id'];
            $update_login = "UPDATE admin_users SET last_login = NOW() WHERE id = $admin_id";
            mysqli_query($conn, $update_login);

            // Redirect to dashboard
            header('location: index.php');
            exit();
        } else {
            array_push($errors, "Wrong username or password");
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo img {
            max-width: 150px;
        }

        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-control {
            height: 50px;
            border-radius: 5px;
        }

        .btn-login {
            height: 50px;
            border-radius: 5px;
            background-color: #473BF0;
            color: #fff;
            font-weight: 600;
            width: 100%;
        }

        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Password toggle eye icon styling */
        .toggle-password {
            cursor: pointer;
            border-left: none;
        }
        .toggle-password:hover {
            color: #473BF0;
        }
        .input-group-text {
            background-color: #fff;
            border-left: 0;
            height: 50px;
        }
        .input-group .form-control:focus + .input-group-append .input-group-text {
            border-color: #80bdff;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-logo">
            <img src="../image/wp/HelloIT-new.png" alt="HelloIT Logo">
        </div>
        <h2 class="login-title">Admin Login</h2>

        <?php if (count($errors) > 0) : ?>
            <div class="error-message">
                <?php foreach ($errors as $error) : ?>
                    <p><?php echo $error; ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="admin-login">
            <div class="form-group">
                <input type="text" class="form-control" name="username" placeholder="Username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
            </div>
            <div class="form-group">
                <div class="input-group">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password">
                    <div class="input-group-append">
                        <span class="input-group-text toggle-password" toggle="#password">
                            <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        </span>
                    </div>
                </div>
            </div>
            <button type="submit" name="login_admin" class="btn btn-login">Login</button>
        </form>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('.toggle-password').on('click', function() {
                // Toggle the eye icon
                $(this).find('i').toggleClass('fa-eye-slash fa-eye');

                // Toggle the password field type
                var input = $($(this).attr('toggle'));
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                } else {
                    input.attr('type', 'password');
                }
            });

            // Add hover effect to the eye icon
            $('.toggle-password').hover(
                function() {
                    $(this).css('cursor', 'pointer');
                }
            );
        });
    </script>
</body>

</html>