<?php
/**
 * Simulate Appika Update - For Testing Purposes
 * This script simulates an update from Appika to test the notification system
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$message = '';
$messageType = '';

// Check if columns exist
function checkColumnsExist() {
    global $conn;
    $result = mysqli_query($conn, "SHOW COLUMNS FROM support_tickets LIKE 'appika_updated_at'");
    return mysqli_num_rows($result) > 0;
}

// Handle simulation request
if (isset($_POST['simulate_update'])) {
    if (!checkColumnsExist()) {
        $message = 'Error: Appika sync columns not found. Please run the setup script first.';
        $messageType = 'danger';
    } else {
        $ticketId = intval($_POST['ticket_id']);
        $updateType = $_POST['update_type'];
        
        // Get current ticket data
        $query = "SELECT * FROM support_tickets WHERE id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $ticketId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $ticket = mysqli_fetch_assoc($result);
        
        if (!$ticket) {
            $message = 'Error: Ticket not found.';
            $messageType = 'danger';
        } else {
            // Simulate different types of updates
            $updates = [];
            $changes = [];
            
            switch ($updateType) {
                case 'status_change':
                    $newStatus = ($ticket['status'] === 'open') ? 'closed' : 'open';
                    $updates['status'] = $newStatus;
                    $changes[] = "status: {$ticket['status']} → $newStatus";
                    break;
                    
                case 'priority_change':
                    $priorities = ['low', 'medium', 'high', 'critical'];
                    $currentIndex = array_search($ticket['priority'], $priorities);
                    $newIndex = ($currentIndex + 1) % count($priorities);
                    $newPriority = $priorities[$newIndex];
                    $updates['priority'] = $newPriority;
                    $changes[] = "priority: {$ticket['priority']} → $newPriority";
                    break;
                    
                case 'both_change':
                    $newStatus = ($ticket['status'] === 'open') ? 'in_progress' : 'open';
                    $newPriority = ($ticket['priority'] === 'low') ? 'high' : 'low';
                    $updates['status'] = $newStatus;
                    $updates['priority'] = $newPriority;
                    $changes[] = "status: {$ticket['status']} → $newStatus";
                    $changes[] = "priority: {$ticket['priority']} → $newPriority";
                    break;
            }
            
            if (!empty($updates)) {
                // Build update query
                $setClause = [];
                $values = [];
                $types = '';
                
                foreach ($updates as $field => $value) {
                    $setClause[] = "$field = ?";
                    $values[] = $value;
                    $types .= 's';
                }
                
                // Add Appika update tracking
                $setClause[] = "appika_updated_at = NOW()";
                $setClause[] = "appika_update_source = ?";
                $values[] = 'simulation_test';
                $types .= 's';
                
                $values[] = $ticketId;
                $types .= 'i';
                
                $updateQuery = "UPDATE support_tickets SET " . implode(', ', $setClause) . " WHERE id = ?";
                $stmt = mysqli_prepare($conn, $updateQuery);
                mysqli_stmt_bind_param($stmt, $types, ...$values);
                
                if (mysqli_stmt_execute($stmt)) {
                    $message = 'Simulation successful! Ticket updated with: ' . implode(', ', $changes);
                    $messageType = 'success';
                } else {
                    $message = 'Error updating ticket: ' . mysqli_error($conn);
                    $messageType = 'danger';
                }
            }
        }
    }
}

// Get tickets with Appika IDs for simulation
$ticketsQuery = "SELECT id, subject, status, priority, appika_id FROM support_tickets WHERE appika_id IS NOT NULL AND appika_id != '' ORDER BY id DESC LIMIT 10";
$ticketsResult = mysqli_query($conn, $ticketsQuery);
$availableTickets = [];
while ($row = mysqli_fetch_assoc($ticketsResult)) {
    $availableTickets[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulate Appika Update - Testing</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    
    <style>
    body { background-color: #f8f9fa; }
    .container { max-width: 800px; margin: 50px auto; }
    .card { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
    .simulation-card { border-left: 4px solid #007bff; }
    .ticket-info { background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>

<body>
    <div class="container">
        <div class="card simulation-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-flask"></i> Simulate Appika Update
                </h4>
                <small>Test how your system handles updates from Appika</small>
            </div>
            
            <div class="card-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!checkColumnsExist()): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Setup Required:</strong> Appika sync columns not found. 
                        <a href="../setup/setup-reverse-sync.php" class="btn btn-sm btn-warning ml-2">Run Setup</a>
                    </div>
                <?php elseif (empty($availableTickets)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>No tickets available:</strong> No tickets with Appika IDs found for simulation.
                        Create some tickets with Appika IDs first.
                    </div>
                <?php else: ?>
                    <form method="POST">
                        <div class="form-group">
                            <label for="ticket_id"><strong>Select Ticket to Simulate Update:</strong></label>
                            <select name="ticket_id" id="ticket_id" class="form-control" required>
                                <option value="">Choose a ticket...</option>
                                <?php foreach ($availableTickets as $ticket): ?>
                                    <option value="<?php echo $ticket['id']; ?>">
                                        #<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['appika_id']); ?> - 
                                        <?php echo htmlspecialchars(substr($ticket['subject'], 0, 50)); ?>
                                        (<?php echo ucfirst($ticket['status']); ?>, <?php echo ucfirst($ticket['priority']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="update_type"><strong>Type of Update to Simulate:</strong></label>
                            <select name="update_type" id="update_type" class="form-control" required>
                                <option value="">Choose update type...</option>
                                <option value="status_change">Status Change (Open ↔ Closed)</option>
                                <option value="priority_change">Priority Change (Low → Medium → High → Critical)</option>
                                <option value="both_change">Both Status & Priority Change</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="submit" name="simulate_update" class="btn btn-primary">
                                <i class="fas fa-play"></i> Simulate Appika Update
                            </button>
                            <a href="admin-tickets.php" class="btn btn-secondary ml-2">
                                <i class="fas fa-arrow-left"></i> Back to Admin
                            </a>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h5><i class="fas fa-info-circle text-info"></i> What This Simulation Does:</h5>
                        <ul class="list-unstyled">
                            <li>✅ Updates the selected ticket in your database</li>
                            <li>✅ Sets <code>appika_updated_at</code> to current timestamp</li>
                            <li>✅ Sets <code>appika_update_source</code> to 'simulation_test'</li>
                            <li>✅ Triggers the same notifications as real Appika updates</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h5><i class="fas fa-eye text-success"></i> After Simulation, Check:</h5>
                        <ul class="list-unstyled">
                            <li>📊 <a href="admin-tickets.php">Admin Dashboard</a> - Look for the widget update</li>
                            <li>🎫 <a href="admin-ticket-detail.php?id=<?php echo !empty($availableTickets) ? $availableTickets[0]['id'] : ''; ?>">Ticket Detail</a> - Look for sync notification</li>
                            <li>📋 <a href="appika-updates-log.php">Updates Log</a> - See the complete history</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (!empty($availableTickets)): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Available Tickets for Simulation</h5>
            </div>
            <div class="card-body">
                <?php foreach ($availableTickets as $ticket): ?>
                    <div class="ticket-info">
                        <strong>#<?php echo $ticket['id']; ?></strong> - 
                        <span class="badge badge-info"><?php echo htmlspecialchars($ticket['appika_id']); ?></span>
                        <br>
                        <small><?php echo htmlspecialchars($ticket['subject']); ?></small>
                        <br>
                        <span class="badge badge-<?php echo $ticket['status']; ?>"><?php echo ucfirst($ticket['status']); ?></span>
                        <span class="badge badge-<?php echo $ticket['priority']; ?>"><?php echo ucfirst($ticket['priority']); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="../js/vendor.min.js"></script>
</body>
</html>
