<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    http_response_code(403);
    echo '<div class="alert alert-danger">Access denied. Please log in.</div>';
    exit();
}

// Include GraphQL functions
include('../functions/graphql_functions.php');

// Check if ticket ID is provided
if (!isset($_POST['ticket_id']) || empty($_POST['ticket_id'])) {
    echo '<div class="alert alert-warning">No ticket ID provided.</div>';
    exit();
}

$ticketId = intval($_POST['ticket_id']);

// Function to format ticket type
function formatTicketType($type, $type_name) {
    if (!empty($type_name)) {
        $badge_class = strtolower($type_name);
        return '<span class="badge badge-' . $badge_class . '">' . ucfirst($type_name) . '</span>';
    }
    
    // Fallback based on type number
    $types = [1 => 'Starter', 2 => 'Premium', 3 => 'Ultimate'];
    $type_name = $types[$type] ?? 'Unknown';
    $badge_class = strtolower($type_name);
    return '<span class="badge badge-' . $badge_class . '">' . $type_name . '</span>';
}

// Function to format priority
function formatPriority($priority) {
    $priority_lower = strtolower($priority);
    $badge_class = $priority_lower;
    
    // Special handling for URGENT priority
    if ($priority_lower === 'urgent') {
        $badge_class = 'urgent';
    }
    
    return '<span class="badge badge-' . $badge_class . '">' . strtoupper($priority) . '</span>';
}

// Function to format status
function formatStatus($status) {
    $status_lower = strtolower($status);
    $badge_class = $status_lower;
    
    // Map WIP to in_progress for styling
    if ($status_lower === 'wip') {
        $badge_class = 'wip';
    }
    
    return '<span class="badge badge-' . $badge_class . '">' . strtoupper($status) . '</span>';
}

// Function to format date
function formatDate($dateString) {
    if (empty($dateString)) {
        return 'N/A';
    }
    
    try {
        $date = new DateTime($dateString);
        return $date->format('d M Y H:i');
    } catch (Exception $e) {
        return htmlspecialchars($dateString);
    }
}

// Fetch ticket details from Appika API
$query = '
query GetTicket($id: Int!) {
    getTicket(id: $id) {
        id
        ticket_no
        contact_id
        agent_id
        req_email
        subject
        type
        type_name
        priority
        status
        created
        updated
    }
}';

$variables = ['id' => $ticketId];
$result = makeGraphQLRequest($query, $variables);

if (!$result['success']) {
    echo '<div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Error fetching ticket details: ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '
    </div>';
    exit();
}

$ticketData = $result['data']['data']['getTicket'] ?? null;

if (!$ticketData) {
    echo '<div class="alert alert-warning">
        <i class="fas fa-search"></i>
        No ticket found with ID: ' . htmlspecialchars($ticketId) . '
    </div>';
    exit();
}
?>

<style>
.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #555;
    min-width: 120px;
}

.info-value {
    color: #333;
    text-align: right;
}

.badge {
    font-size: 12px;
    padding: 4px 8px;
}

.badge-urgent {
    background-color: #dc3545;
    color: #fff;
    font-weight: bold;
}

.badge-high {
    background-color: #fd7e14;
    color: #fff;
}

.badge-medium {
    background-color: #ffc107;
    color: #fff;
}

.badge-low {
    background-color: #28a745;
    color: #fff;
}

.badge-open {
    background-color: #4CAF50;
    color: #fff;
}

.badge-wip {
    background-color: #2196F3;
    color: #fff;
}

.badge-closed {
    background-color: #757575;
    color: #fff;
}

.badge-starter {
    background-color: #fbbf24;
    color: #fff;
}

.badge-premium {
    background-color: #01A7E1;
    color: #fff;
}

.badge-ultimate {
    background-color: #793BF0;
    color: #fff;
}
</style>

<div class="ticket-info-card">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="badge badge-primary">ID: <?php echo htmlspecialchars($ticketData['id']); ?></span>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Ticket Number:</span>
                <span class="info-value"><strong><?php echo htmlspecialchars($ticketData['ticket_no'] ?? 'N/A'); ?></strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Subject:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['subject'] ?? 'N/A'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Requester Email:</span>
                <span class="info-value">
                    <?php if (!empty($ticketData['req_email'])): ?>
                        <a href="mailto:<?php echo htmlspecialchars($ticketData['req_email']); ?>">
                            <?php echo htmlspecialchars($ticketData['req_email']); ?>
                        </a>
                    <?php else: ?>
                        N/A
                    <?php endif; ?>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Ticket Type:</span>
                <span class="info-value"><?php echo formatTicketType($ticketData['type'] ?? 0, $ticketData['type_name'] ?? ''); ?></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Priority:</span>
                <span class="info-value"><?php echo formatPriority($ticketData['priority'] ?? ''); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value"><?php echo formatStatus($ticketData['status'] ?? ''); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Contact ID:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['contact_id'] ?? 'N/A'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Agent ID:</span>
                <span class="info-value"><?php echo htmlspecialchars($ticketData['agent_id'] ?? 'Unassigned'); ?></span>
            </div>
        </div>
    </div>

    <!-- Timestamps -->
    <hr>
    <h6><i class="fas fa-clock"></i> Timeline</h6>
    <div class="row">
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Created:</span>
                <span class="info-value"><?php echo formatDate($ticketData['created'] ?? ''); ?></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-row">
                <span class="info-label">Last Updated:</span>
                <span class="info-value"><?php echo formatDate($ticketData['updated'] ?? ''); ?></span>
            </div>
        </div>
    </div>
</div>
