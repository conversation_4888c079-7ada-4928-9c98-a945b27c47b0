<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Include GraphQL functions
include('../functions/graphql_functions.php');

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : "";
$type_filter = isset($_GET['type']) ? trim($_GET['type']) : "";
$priority_filter = isset($_GET['priority']) ? trim($_GET['priority']) : "";

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;

// Fetch tickets from Appika API
function fetchAppikaTickets($search = "", $status_filter = "", $type_filter = "", $priority_filter = "", $page = 1, $items_per_page = 10) {
    // Try different GraphQL query structures based on the API
    $queries = [
        // First try: Simple getTickets query
        '
        query GetTickets {
            getTickets {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
            }
        }',
        // Second try: getTickets with data wrapper
        '
        query GetTickets {
            getTickets {
                data {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                }
            }
        }',
        // Third try: Individual ticket queries
        '
        query GetTickets {
            ticket1: getTicket(id: 1) {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
            }
            ticket2: getTicket(id: 2) {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
            }
            ticket3: getTicket(id: 3) {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
            }
        }'
    ];

    $tickets = [];
    $lastError = '';

    // Try each query structure
    foreach ($queries as $query) {
        $result = makeGraphQLRequest($query, []);

        if ($result['success'] && isset($result['data']['data'])) {
            $data = $result['data']['data'];

            // Handle different response structures
            if (isset($data['getTickets'])) {
                if (is_array($data['getTickets']) && isset($data['getTickets'][0])) {
                    // Direct array of tickets
                    $tickets = $data['getTickets'];
                    break;
                } elseif (isset($data['getTickets']['data'])) {
                    // Tickets wrapped in data property
                    $tickets = $data['getTickets']['data'];
                    break;
                }
            } else {
                // Individual ticket queries (ticket1, ticket2, etc.)
                $tickets = [];
                foreach ($data as $key => $ticket) {
                    if (strpos($key, 'ticket') === 0 && $ticket) {
                        $tickets[] = $ticket;
                    }
                }
                if (!empty($tickets)) {
                    break;
                }
            }
        } else {
            $lastError = $result['error'] ?? 'Unknown error';
        }
    }

    // If no tickets found, return error
    if (empty($tickets)) {
        return [
            'success' => false,
            'tickets' => [],
            'total_items' => 0,
            'total_pages' => 0,
            'error' => $lastError ?: 'No tickets found or API structure not recognized'
        ];
    }

    // Apply filters
    if (!empty($search)) {
        $tickets = array_filter($tickets, function($ticket) use ($search) {
            $search_lower = strtolower($search);
            return (
                strpos(strtolower($ticket['id']), $search_lower) !== false ||
                strpos(strtolower($ticket['ticket_no']), $search_lower) !== false ||
                strpos(strtolower($ticket['subject']), $search_lower) !== false ||
                strpos(strtolower($ticket['req_email']), $search_lower) !== false ||
                strpos(strtolower($ticket['created']), $search_lower) !== false
            );
        });
    }

    if (!empty($status_filter)) {
        $tickets = array_filter($tickets, function($ticket) use ($status_filter) {
            return strtolower($ticket['status']) === strtolower($status_filter);
        });
    }

    if (!empty($type_filter)) {
        $tickets = array_filter($tickets, function($ticket) use ($type_filter) {
            return strtolower($ticket['type_name']) === strtolower($type_filter);
        });
    }

    if (!empty($priority_filter)) {
        $tickets = array_filter($tickets, function($ticket) use ($priority_filter) {
            return strtolower($ticket['priority']) === strtolower($priority_filter);
        });
    }

    // Sort by created date (newest first)
    usort($tickets, function($a, $b) {
        return strtotime($b['created']) - strtotime($a['created']);
    });

    // Apply pagination
    $total_items = count($tickets);
    $offset = ($page - 1) * $items_per_page;
    $tickets = array_slice($tickets, $offset, $items_per_page);

    return [
        'success' => true,
        'tickets' => $tickets,
        'total_items' => $total_items,
        'total_pages' => ceil($total_items / $items_per_page)
    ];
}

// Fetch tickets
$ticket_data = fetchAppikaTickets($search, $status_filter, $type_filter, $priority_filter, $page, $items_per_page);
$tickets = $ticket_data['tickets'];
$total_items = $ticket_data['total_items'];
$total_pages = $ticket_data['total_pages'];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Appika Tickets</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
        }

        .admin-sidebar ul li {
            margin-bottom: 5px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .filter-row {
        margin-bottom: 20px;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
        }

        .filter-row {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
        }

        .filter-row {
            margin-bottom: 12px;
        }
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Ticket Status badges */
    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-wip {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Priority badges */
    .badge-low {
        background-color: #28a745;
        color: #fff
    }

    .badge-medium {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-high {
        background-color: #fd7e14;
        color: #fff
    }

    .badge-urgent {
        background-color: #dc3545;
        color: #fff;
        font-weight: bold;
    }

    /* Type badges */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    /* Search bar styles */
    .search-filter-row {
        padding: 0 40px;
    }

    .search-box {
        width: auto;
    }

    .search-box .input-group {
        width: 550px !important;
        max-width: 100% !important;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-row select {
        width: auto !important;
        padding: 4px 8px;
        height: 38px !important;
        margin-right: 5px !important;
        font-size: 14px;
    }

    .filter-container {
        padding-right: 20px;
        padding-left: 0;
    }

    .filter-container .d-flex {
        justify-content: flex-start !important;
    }

    @media (min-width: 992px) {
        .filter-container select {
            width: 120px !important;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }

    @media (max-width: 991px) {
        .filter-container {
            margin-top: 15px;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .search-box {
            width: 100%;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 500px !important;
            margin: 0 auto;
        }
    }

    @media (max-width: 767px) {
        .search-filter-row {
            padding: 0 15px;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 100% !important;
        }

        .search-input {
            font-size: 14px;
        }

        .filter-container {
            padding-left: 15px !important;
            padding-right: 15px !important;
            margin-top: 15px;
        }

        .filter-container select {
            width: 48% !important;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .filter-container .d-flex {
            justify-content: space-between !important;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .search-filter-row {
            padding: 0 10px;
        }

        .search-input::placeholder {
            font-size: 13px;
        }

        .filter-container select {
            font-size: 12px;
        }

        .filter-container {
            margin-top: 10px;
        }
    }

    /* Table styles for mobile */
    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }

        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.2rem;
        }
    }

    /* Error message styling */
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border: 1px solid #f5c6cb;
    }

    /* API ID styling */
    .api-id {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #473BF0;
        font-size: 12px;
    }

    /* Dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        margin-right: 10px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            right: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Appika API Tickets</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!$ticket_data['success']): ?>
                    <div class="error-message">
                        <strong>Error:</strong> <?php echo htmlspecialchars($ticket_data['error']); ?>
                    </div>
                    <?php endif; ?>

                    <div class="filter-row">
                        <form method="GET" class="row">
                            <div class="col-md-6">
                                <div class="search-filter-row">
                                    <div class="search-box">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by ID, ticket no, subject, email or date"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 filter-container">
                                <div class="d-flex flex-wrap">
                                    <select name="type" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Types</option>
                                        <option value="starter"
                                            <?php echo $type_filter === 'starter' ? 'selected' : ''; ?>>
                                            Starter</option>
                                        <option value="premium"
                                            <?php echo $type_filter === 'premium' ? 'selected' : ''; ?>>Premium
                                        </option>
                                        <option value="ultimate"
                                            <?php echo $type_filter === 'ultimate' ? 'selected' : ''; ?>>Ultimate
                                        </option>
                                    </select>
                                    <select name="status" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Statuses</option>
                                        <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>
                                            Open</option>
                                        <option value="wip" <?php echo $status_filter === 'wip' ? 'selected' : ''; ?>>
                                            WIP
                                        </option>
                                        <option value="closed"
                                            <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                    </select>

                                    <select name="priority" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Priorities</option>
                                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>
                                            Low</option>
                                        <option value="medium"
                                            <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Medium
                                        </option>
                                        <option value="high"
                                            <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>High
                                        </option>
                                        <option value="urgent"
                                            <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>Urgent
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>API ID</th>
                                    <th>Ticket No</th>
                                    <th>Subject</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($tickets)): ?>
                                <?php foreach ($tickets as $ticket): ?>
                                <tr>
                                    <td>
                                        <span class="api-id"><?php echo htmlspecialchars($ticket['id']); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($ticket['ticket_no'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($ticket['subject'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($ticket['req_email'] ?? '-'); ?></td>
                                    <td>
                                        <?php
                                        $type_name = strtolower($ticket['type_name'] ?? '');
                                        $badge_class = $type_name ?: 'secondary';
                                        $display_text = ucfirst($type_name) ?: '-';
                                        ?>
                                        <span class="badge badge-<?php echo $badge_class; ?>">
                                            <?php echo $display_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status = strtolower($ticket['status'] ?? '');
                                        $badge_class = $status ?: 'secondary';
                                        $display_text = strtoupper($status) ?: '-';
                                        ?>
                                        <span class="badge badge-<?php echo $badge_class; ?>">
                                            <?php echo $display_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $priority = strtolower($ticket['priority'] ?? '');
                                        $badge_class = $priority ?: 'secondary';
                                        $display_text = strtoupper($priority) ?: '-';
                                        ?>
                                        <span class="badge badge-<?php echo $badge_class; ?>">
                                            <?php echo $display_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        if (!empty($ticket['created'])) {
                                            echo date('Y-m-d H:i', strtotime($ticket['created']));
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        if (!empty($ticket['updated'])) {
                                            echo date('Y-m-d H:i', strtotime($ticket['updated']));
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="viewTicketDetails(<?php echo htmlspecialchars($ticket['id']); ?>)">
                                            <i class="fas fa-eye"></i> &nbsp; View Details
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center">
                                        <?php echo $ticket_data['success'] ? 'No tickets found' : 'Unable to load tickets from API'; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>">Previous</a>
                            </li>

                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                    <?php endif; ?>

                    <div class="mt-3">
                        <small class="text-muted">
                            Showing <?php echo count($tickets); ?> of <?php echo $total_items; ?> tickets from Appika
                            API
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ticket Details Modal -->
    <div class="modal fade" id="ticketDetailsModal" tabindex="-1" role="dialog" aria-labelledby="ticketDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ticketDetailsModalLabel">
                        <i class="fas fa-ticket-alt"></i> Ticket Information
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="ticketDetailsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Loading ticket details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" style="background-color: #473BF0; color: white; border-color: #473BF0;">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure jQuery is available -->
    <script>
    // Check if jQuery is loaded, if not load from CDN
    if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
        console.log('jQuery not found in vendor.min.js, loading from CDN...');
        var script = document.createElement('script');
        script.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
        script.onload = function() {
            console.log('jQuery loaded from CDN');
        };
        document.head.appendChild(script);
    } else {
        console.log('jQuery is available');
        // Make sure $ is available globally
        if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
            window.$ = jQuery;
        }
    }
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });

    // Function to view ticket details
    function viewTicketDetails(ticketId) {
        // Check if jQuery is loaded
        if (typeof $ === 'undefined') {
            alert('jQuery is not loaded. Please refresh the page.');
            return;
        }

        // Check if modal exists
        if ($('#ticketDetailsModal').length === 0) {
            alert('Modal not found. Please refresh the page.');
            return;
        }

        // Show modal
        $('#ticketDetailsModal').modal('show');

        // Reset content to loading state
        document.getElementById('ticketDetailsContent').innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading ticket details...</p>
            </div>
        `;

        // Make AJAX request to get ticket details
        fetch('appika-ticket-details.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'ticket_id=' + encodeURIComponent(ticketId)
        })
        .then(response => response.text())
        .then(data => {
            document.getElementById('ticketDetailsContent').innerHTML = data;
        })
        .catch(error => {
            console.error('Error loading ticket details:', error);
            document.getElementById('ticketDetailsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading ticket details. Please try again.
                </div>
            `;
        });
    }
    </script>
</body>

</html>