<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];



// Get ticket ID from URL
$ticket_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($ticket_id <= 0) {
    header('location: admin-tickets.php');
    exit();
}

// Mark ticket as seen by admin
mysqli_query($conn, "UPDATE support_tickets SET is_seen_by_admin=1 WHERE id=$ticket_id");

// Get ticket information
$ticket_query = "SELECT st.*, u.username, u.email, u.tell, au.username as assigned_admin_name
                FROM support_tickets st
                JOIN user u ON st.user_id = u.id
                LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
                WHERE st.id = $ticket_id";
$ticket_result = mysqli_query($conn, $ticket_query);

if (!$ticket_result || mysqli_num_rows($ticket_result) == 0) {
    header('location: admin-tickets.php');
    exit();
}

$ticket = mysqli_fetch_assoc($ticket_result);

// Include centralized Appika sync functions
require_once '../functions/appika_sync.php';

// Process update status form
$message = '';
$message_type = '';

if (isset($_POST['update_status'])) {
    $new_status = mysqli_real_escape_string($conn, $_POST['status']);
    $new_priority = mysqli_real_escape_string($conn, $_POST['priority']);
    $new_severity = mysqli_real_escape_string($conn, $_POST['severity']);

    // Update local database first
    $update_sql = "UPDATE support_tickets SET
                  status = '$new_status',
                  priority = '$new_priority',
                  severity = '$new_severity',
                  updated_at = NOW()
                  WHERE id = $ticket_id";

    if (mysqli_query($conn, $update_sql)) {
        // Use centralized sync function for Appika API update
        $syncResult = syncTicketToAppika($ticket_id, $new_status, $new_priority, $admin_username);

        // Log the sync operation
        logAppikaSync($ticket_id, 'admin_update_status_priority', $syncResult);

        // Set message based on sync result
        if ($syncResult['appika_updated']) {
            $message = "Ticket updated successfully in both local database and Appika API";
            $message_type = 'success';
        } else {
            $message = $syncResult['message'];
            $message_type = 'warning';
        }

        // Refresh ticket data
        $ticket_result = mysqli_query($conn, $ticket_query);
        $ticket = mysqli_fetch_assoc($ticket_result);
    } else {
        $message = "Error updating ticket status: " . mysqli_error($conn);
        $message_type = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ticket Detail</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin stylesheets -->
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 12px 15px;
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
            justify-content: space-between;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
        }

        .admin-sidebar ul li {
            margin-bottom: 5px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .ticket-card {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        width: 100%;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
        }

        .ticket-card {
            padding: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
        }

        .ticket-card {
            padding: 12px;
        }
    }

    .ticket-header {
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .ticket-header h3 {
        margin: 0;
        color: #333;
    }

    .ticket-meta {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .ticket-meta-item {
        margin-right: 20px;
        margin-bottom: 10px;
        flex: 0 0 auto;
    }

    .ticket-meta-label {
        font-weight: 600;
        color: #555;
        margin-right: 5px;
        display: inline-block;
    }

    /* Responsive meta items */
    @media (max-width: 991px) {
        .ticket-meta-item {
            margin-right: 15px;
            margin-bottom: 8px;
        }
    }

    @media (max-width: 767px) {
        .ticket-meta {
            flex-direction: column;
        }

        .ticket-meta-item {
            margin-right: 0;
            margin-bottom: 8px;
            width: 100%;
        }

        .ticket-meta-label {
            min-width: 80px;
        }
    }

    .ticket-content {
        margin-bottom: 20px;
    }

    .ticket-content p {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    .badge-open,
    .badge-in_progress,
    .badge-resolved,
    .badge-closed,
    .badge-starter,
    .badge-premium,
    .badge-ultimate,
    .badge-low,
    .badge-medium,
    .badge-high,
    .badge-critical,
    .badge-Low,
    .badge-Normal,
    .badge-High,
    .badge-Critical {
        font-size: 14px;
        padding: 5px 8px;
        display: inline-block;
        margin-bottom: 3px;
    }

    /* Responsive badges */
    @media (max-width: 991px) {

        .badge-open,
        .badge-in_progress,
        .badge-resolved,
        .badge-closed,
        .badge-starter,
        .badge-premium,
        .badge-ultimate,
        .badge-low,
        .badge-medium,
        .badge-high,
        .badge-critical,
        .badge-Low,
        .badge-Normal,
        .badge-High,
        .badge-Critical {
            font-size: 13px;
            padding: 4px 7px;
        }
    }

    @media (max-width: 767px) {

        .badge-open,
        .badge-in_progress,
        .badge-resolved,
        .badge-closed,
        .badge-starter,
        .badge-premium,
        .badge-ultimate,
        .badge-low,
        .badge-medium,
        .badge-high,
        .badge-critical,
        .badge-Low,
        .badge-Normal,
        .badge-High,
        .badge-Critical {
            font-size: 12px;
            padding: 3px 6px;
        }
    }

    /* Ticket Status Badge Colors */
    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket Type Badge Colors */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    /* Appika ID Badge */
    .badge-info {
        background-color: #17a2b8;
        color: #fff;
        font-family: 'Courier New', monospace;
        font-weight: bold;
        letter-spacing: 0.5px;
    }

    /* Modal styles */
    .modal-content {
        border-radius: 5px;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px;
    }

    /* Responsive modal */
    @media (max-width: 767px) {
        .modal-dialog {
            margin: 10px;
        }

        .modal-header {
            padding: 12px;
        }

        .modal-body {
            padding: 15px;
        }

        .modal-footer {
            padding: 12px;
        }

        .modal-title {
            font-size: 18px;
        }

        .form-control {
            font-size: 14px;
        }
    }

    /* Button styles */
    .btn {
        border-radius: 4px;
    }

    .action-buttons {
        margin-top: 15px;
    }

    .action-buttons .btn {
        margin-right: 10px;
    }

    @media (max-width: 767px) {
        .btn {
            padding: 0.375rem 0.75rem;
            font-size: 14px;
        }

        .action-buttons {
            margin-top: 10px;
            justify-content: space-between;
        }

        .action-buttons .btn {
            margin-right: 5px;
            flex: 1;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .btn {
            padding: 0.25rem 0.5rem;
            font-size: 13px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .btn {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
        }
    }

    h5#updateStatusModalLabel {
        color: white;
    }

    /* Photo Attachment Styles */
    .ticket-attachments {
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .ticket-attachments h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
        margin-bottom: 15px;
    }

    .attachment-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background: white;
        border: 1px solid #dee2e6;
    }

    .attachment-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .attachment-thumbnail {
        width: 100%;
        height: 140px;
        object-fit: cover;
        display: block;
        transition: opacity 0.3s ease;
    }

    .attachment-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(71, 59, 240, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        color: white;
        font-size: 28px;
    }

    .attachment-item:hover .attachment-overlay {
        opacity: 1;
    }

    .attachment-info {
        padding: 12px;
        text-align: center;
        background: white;
        border-top: 1px solid #eee;
    }

    .attachment-info small {
        color: #666;
        font-size: 12px;
        word-break: break-all;
        line-height: 1.4;
    }

    .attachment-item.missing {
        background: #f8d7da;
        border-color: #f5c6cb;
    }

    .attachment-missing {
        padding: 30px 15px;
        text-align: center;
        color: #721c24;
    }

    .attachment-missing i {
        font-size: 32px;
        margin-bottom: 10px;
        color: #dc3545;
    }

    .attachment-missing p {
        margin: 10px 0 5px 0;
        font-weight: 600;
    }

    .attachment-missing small {
        color: #856404;
        font-size: 11px;
    }

    /* Responsive attachment grid */
    @media (max-width: 991px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }

        .attachment-thumbnail {
            height: 120px;
        }

        .ticket-attachments {
            padding: 15px;
        }
    }

    @media (max-width: 767px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
            gap: 12px;
        }

        .attachment-thumbnail {
            height: 100px;
        }

        .attachment-info {
            padding: 8px;
        }

        .ticket-attachments {
            padding: 12px;
        }
    }

    @media (max-width: 575px) {
        .attachment-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .attachment-thumbnail {
            height: 90px;
        }

        .attachment-overlay {
            font-size: 20px;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Ticket #<?php echo $ticket_id; ?></h1>
            <div class="admin-user">
                <span>Welcome, <?php echo htmlspecialchars($admin_username); ?>
                    (<?php echo htmlspecialchars($admin_role); ?>)</span>
                <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <div class="ticket-card">
                        <div class="ticket-header">
                            <h3><?php echo htmlspecialchars($ticket['subject']); ?></h3>
                            <div class="ticket-meta">
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Status:</span>
                                    <span
                                        class="badge badge-<?php echo $ticket['status']; ?>"><?php echo ucfirst($ticket['status']); ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Type:</span>
                                    <?php
                                    // Display 'Business' for premium tickets
                                    $ticketType = $ticket['ticket_type'];
                                    $badgeClass = $ticketType;
                                    $displayText = ucfirst($ticketType);

                                    if (strtolower($ticketType) == 'premium') {
                                        $displayText = 'Business';
                                    }
                                    ?>
                                    <span
                                        class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Priority:</span>
                                    <span
                                        class="badge badge-<?php echo $ticket['priority']; ?>"><?php echo ucfirst($ticket['priority']); ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Severity:</span>
                                    <span
                                        class="badge badge-<?php echo $ticket['severity']; ?>"><?php echo $ticket['severity']; ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Created:</span>
                                    <span><?php echo date('Y-m-d H:i', strtotime($ticket['created_at'])); ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">User:</span>
                                    <a
                                        href="admin-user-detail.php?username=<?php echo urlencode($ticket['username']); ?>">
                                        <?php echo htmlspecialchars($ticket['username']); ?>
                                    </a>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Email:</span>
                                    <span><?php echo htmlspecialchars($ticket['email']); ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Phone:</span>
                                    <span><?php echo htmlspecialchars($ticket['tell'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Assigned Admin:</span>
                                    <span><?php echo !empty($ticket['assigned_admin_name']) ? htmlspecialchars($ticket['assigned_admin_name']) : '<span class="text-muted">Not assigned</span>'; ?></span>
                                </div>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Appika ID:</span>
                                    <?php if (!empty($ticket['appika_id'])): ?>
                                        <span class="badge badge-info"><?php echo htmlspecialchars($ticket['appika_id']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Not synced</span>
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($ticket['problem_type'])): ?>
                                <div class="ticket-meta-item">
                                    <span class="ticket-meta-label">Problem Type:</span>
                                    <span><?php echo htmlspecialchars($ticket['problem_type']); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="ticket-content">
                            <p><?php echo nl2br(htmlspecialchars($ticket['description'])); ?></p>
                        </div>

                        <?php if (!empty($ticket['attachments'])): ?>
                        <div class="ticket-attachments">
                            <h5><i class="fas fa-paperclip"></i> Attachments</h5>
                            <?php
                            $attachments = json_decode($ticket['attachments'], true);
                            if (is_array($attachments) && count($attachments) > 0):
                            ?>
                            <div class="attachment-grid">
                                <?php foreach ($attachments as $index => $attachment): ?>
                                <?php if (file_exists('../' . $attachment)): ?>
                                <div class="attachment-item">
                                    <a href="../<?php echo htmlspecialchars($attachment); ?>"
                                       data-fancybox="admin-ticket-gallery"
                                       data-caption="Attachment <?php echo $index + 1; ?> - <?php echo basename($attachment); ?>">
                                        <img src="../<?php echo htmlspecialchars($attachment); ?>"
                                             alt="Attachment <?php echo $index + 1; ?>"
                                             class="attachment-thumbnail">
                                        <div class="attachment-overlay">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                    </a>
                                    <div class="attachment-info">
                                        <small><?php echo basename($attachment); ?></small>
                                        <br>
                                        <small class="text-muted">
                                            <?php
                                            $fileSize = file_exists('../' . $attachment) ? filesize('../' . $attachment) : 0;
                                            echo $fileSize > 0 ? number_format($fileSize / 1024, 1) . ' KB' : 'Unknown size';
                                            ?>
                                        </small>
                                    </div>
                                </div>
                                <?php else: ?>
                                <div class="attachment-item missing">
                                    <div class="attachment-missing">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <p>File not found</p>
                                        <small><?php echo basename($attachment); ?></small>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Click on images to view full size. Total: <?php echo count($attachments); ?> attachment(s)
                            </small>
                            <?php else: ?>
                            <p class="text-muted">No valid attachments found</p>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-12">
                                <div class="action-buttons d-flex flex-wrap">
                                    <a href="admin-chat.php?user_id=<?php echo $ticket['user_id']; ?>&ticket_id=<?php echo $ticket_id; ?>"
                                        class="btn mb-2 mr-2" style="background-color: #28a745; color: white;">
                                        <i class="fas fa-comments"></i> &nbsp; Start Live Chat
                                    </a>
                                    <button type="button" class="btn btn-primary mb-2 mr-2" data-toggle="modal"
                                        data-target="#updateStatusModal">
                                        <i class="fas fa-edit"></i> &nbsp; Update Status
                                    </button>
                                    <a href="admin-tickets.php" class="btn btn-danger mb-2">
                                        <i class="fas fa-arrow-left"></i> &nbsp; Back
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Ticket Status</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="open" <?php echo $ticket['status'] === 'open' ? 'selected' : ''; ?>>Open
                                </option>
                                <option value="in_progress"
                                    <?php echo $ticket['status'] === 'in_progress' ? 'selected' : ''; ?>>In Progress
                                </option>
                                <option value="resolved"
                                    <?php echo $ticket['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                <option value="closed" <?php echo $ticket['status'] === 'closed' ? 'selected' : ''; ?>>
                                    Closed</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select class="form-control" id="priority" name="priority" required>
                                <option value="low" <?php echo $ticket['priority'] === 'low' ? 'selected' : ''; ?>>
                                    Low</option>
                                <option value="medium"
                                    <?php echo $ticket['priority'] === 'medium' ? 'selected' : ''; ?>>Normal (Medium in Appika)</option>
                                <option value="high" <?php echo $ticket['priority'] === 'high' ? 'selected' : ''; ?>>
                                    Important (High in Appika)</option>
                                <option value="critical"
                                    <?php echo $ticket['priority'] === 'critical' ? 'selected' : ''; ?>>Critical (Urgent in Appika)
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="severity">Severity</label>
                            <select class="form-control" id="severity" name="severity" required>
                                <option value="Low" <?php echo $ticket['severity'] === 'Low' ? 'selected' : ''; ?>>
                                    Information</option>
                                <option value="Normal"
                                    <?php echo $ticket['severity'] === 'Normal' ? 'selected' : ''; ?>>Minor</option>
                                <option value="High" <?php echo $ticket['severity'] === 'High' ? 'selected' : ''; ?>>
                                    Important</option>
                                <option value="Critical"
                                    <?php echo $ticket['severity'] === 'Critical' ? 'selected' : ''; ?>>Critical
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn" style="background-color: #dc3545; color: white;"
                            data-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize Fancybox for admin photo gallery
        $('[data-fancybox="admin-ticket-gallery"]').fancybox({
            buttons: [
                "zoom",
                "slideShow",
                "fullScreen",
                "download",
                "thumbs",
                "close"
            ],
            loop: true,
            protect: true,
            animationEffect: "fade",
            transitionEffect: "slide",
            thumbs: {
                autoStart: true,
                hideOnClose: true
            },
            caption: function(instance, item) {
                return $(this).data('caption') || '';
            },
            toolbar: true,
            smallBtn: true,
            iframe: {
                preload: false
            }
        });
    });
    </script>
</body>

</html>