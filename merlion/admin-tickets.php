<?php
session_start();
include('../functions/server.php');

// Add Appika integration dependencies at the top
require_once '../vendor/autoload.php';
require_once '../config/api-config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

// Anti-resubmit function: Generate CSRF token for forms
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Anti-resubmit function: Validate CSRF token
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Anti-resubmit function: Check if action was already processed
function isActionAlreadyProcessed($actionKey) {
    return isset($_SESSION['processed_actions'][$actionKey]);
}

// Anti-resubmit function: Mark action as processed
function markActionAsProcessed($actionKey) {
    if (!isset($_SESSION['processed_actions'])) {
        $_SESSION['processed_actions'] = [];
    }
    $_SESSION['processed_actions'][$actionKey] = time();

    // Clean up old processed actions (older than 1 hour)
    $oneHourAgo = time() - 3600;
    foreach ($_SESSION['processed_actions'] as $key => $timestamp) {
        if ($timestamp < $oneHourAgo) {
            unset($_SESSION['processed_actions'][$key]);
        }
    }
}

// Anti-resubmit function: Redirect after POST to prevent resubmission
function redirectAfterPost($message = '', $type = 'success') {
    $params = [];
    if (!empty($message)) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }

    // Preserve current GET parameters
    $currentParams = $_GET;
    if (!empty($currentParams)) {
        $params = array_merge($params, $currentParams);
    }

    $redirectUrl = $_SERVER['PHP_SELF'];
    if (!empty($params)) {
        $redirectUrl .= '?' . http_build_query($params);
    }

    header("Location: $redirectUrl");
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Handle flash messages from redirects
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'];
    unset($_SESSION['flash_message'], $_SESSION['flash_type']);
}

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : "";
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : "";
$type_filter = isset($_GET['type']) ? trim($_GET['type']) : "";
$priority_filter = isset($_GET['priority']) ? trim($_GET['priority']) : "";
$severity_filter = isset($_GET['severity']) ? trim($_GET['severity']) : "";

// Build search and filter conditions
$where_conditions = [];

if (!empty($search)) {
    $search_safe = mysqli_real_escape_string($conn, $search);
    $where_conditions[] = "(st.id LIKE '%$search_safe%' OR
                           st.appika_id LIKE '%$search_safe%' OR
                           st.subject LIKE '%$search_safe%' OR
                           u.username LIKE '%$search_safe%' OR
                           DATE(st.created_at) = '$search_safe')";
}

if (!empty($status_filter)) {
    $status_safe = mysqli_real_escape_string($conn, $status_filter);
    $where_conditions[] = "st.status = '$status_safe'";
}

if (!empty($type_filter)) {
    $type_safe = mysqli_real_escape_string($conn, $type_filter);
    $where_conditions[] = "st.ticket_type = '$type_safe'";
}

if (!empty($priority_filter)) {
    $priority_safe = mysqli_real_escape_string($conn, $priority_filter);
    $where_conditions[] = "st.priority = '$priority_safe'";
}

if (!empty($severity_filter)) {
    $severity_safe = mysqli_real_escape_string($conn, $severity_filter);
    $where_conditions[] = "st.severity = '$severity_safe'";
}

// Combine conditions
$where_clause = "";
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Pagination settings
$items_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $items_per_page;

// Count total tickets for pagination
$count_sql = "SELECT COUNT(*) as total
              FROM support_tickets st
              JOIN user u ON st.user_id = u.id
              $where_clause";
$count_result = mysqli_query($conn, $count_sql);
$total_items = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_items / $items_per_page);

// Get tickets with pagination
$tickets_sql = "SELECT st.*, u.username, au.username as assigned_admin_name
                FROM support_tickets st
                JOIN user u ON st.user_id = u.id
                LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
                $where_clause
                ORDER BY st.created_at DESC
                LIMIT $items_per_page OFFSET $offset";
$tickets_result = mysqli_query($conn, $tickets_sql);

// Function to make GraphQL requests (copied from create-ticket.php)
function makeGraphQLRequest($query, $variables = []) {
    $graphqlConfig = getGraphqlApiConfig();
    $graphqlEndpoint = $graphqlConfig['endpoint'];
    $apiKey = $graphqlConfig['key'];
    $client = new \GuzzleHttp\Client([
        'timeout' => 30,
        'http_errors' => false,
    ]);
    try {
        $response = $client->post($graphqlEndpoint, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                // 'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => [
                'query' => $query,
                'variables' => $variables
            ]
        ]);
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);
        return [
            'success' => ($statusCode >= 200 && $statusCode < 300 && !isset($data['errors'])),
            'status' => $statusCode,
            'data' => $data,
            'error' => isset($data['errors']) ? json_encode($data['errors']) : null
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Generate CSRF token for forms
$csrf_token = generateCSRFToken();

// Handle bulk add to Appika
if (isset($_POST['bulk_add_to_appika']) && !empty($_POST['selected_tickets'])) {
    // Anti-resubmit: Validate CSRF token
    if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
        redirectAfterPost('Security token validation failed. Please try again.', 'danger');
    }

    // Anti-resubmit: Create unique action key for this bulk operation
    $selected_tickets = $_POST['selected_tickets'];
    $actionKey = 'bulk_add_' . md5(implode(',', $selected_tickets) . time());

    // Anti-resubmit: Check if this action was already processed
    if (isActionAlreadyProcessed($actionKey)) {
        redirectAfterPost('This action has already been processed.', 'warning');
    }

    // Mark action as being processed
    markActionAsProcessed($actionKey);

    $success_count = 0;
    $error_count = 0;
    $error_messages = [];

    foreach ($selected_tickets as $ticket_id) {
        $ticket_id = intval($ticket_id);
        $ticket_query = "SELECT st.*, u.email, u.username FROM support_tickets st JOIN user u ON st.user_id = u.id WHERE st.id = $ticket_id";
        $ticket_result = mysqli_query($conn, $ticket_query);
        
        if ($ticket = mysqli_fetch_assoc($ticket_result)) {
            // Prepare variables for Appika
            $typeMapping = [ 'starter' => 1, 'premium' => 2, 'ultimate' => 3 ];
            $type = $typeMapping[$ticket['ticket_type']] ?? 1;
            $priorityMapping = [
                'Information' => 'LOW', 'Minor' => 'LOW', 'Important' => 'MEDIUM', 'Critical' => 'HIGH',
                'low' => 'LOW', 'medium' => 'MEDIUM', 'high' => 'HIGH', 'critical' => 'HIGH'
            ];
            $apiPriority = $priorityMapping[$ticket['priority']] ?? 'LOW';
            
            $mutation = 'mutation CreateTicketByAgent($subject: String!, $reply_msg: String!, $req_email: String, $type: Int!, $type_name: String, $priority: String!, $status: String!, $reply_type: String!, $tags: String) { createTicketByAgent(subject: $subject, reply_msg: $reply_msg, req_email: $req_email, type: $type, type_name: $type_name, priority: $priority, status: $status, reply_type: $reply_type, tags: $tags) { id ticket_no contact_id agent_id req_email subject type type_name priority status created updated } }';
            
            $variables = [
                'subject' => $ticket['subject'],
                'reply_msg' => $ticket['description'],
                'req_email' => $ticket['email'],
                'type' => $type,
                'type_name' => $ticket['ticket_type'],
                'priority' => $apiPriority,
                'status' => 'OPEN',
                'reply_type' => 'note',
                'tags' => ''
            ];
            
            $apiResult = makeGraphQLRequest($mutation, $variables);
            
            if ($apiResult['success'] && isset($apiResult['data']['data']['createTicketByAgent']['id'])) {
                $appikaApiId = $apiResult['data']['data']['createTicketByAgent']['id'];
                $appikaId = str_pad($appikaApiId, 3, '0', STR_PAD_LEFT);
                // Update local ticket
                mysqli_query($conn, "UPDATE support_tickets SET appika_id = '$appikaId' WHERE id = $ticket_id");
                $success_count++;
            } else {
                $error_count++;
                $error_messages[] = "Ticket ID $ticket_id: " . ($apiResult['error'] ?? 'Unknown error');
            }
        }
    }

    // Prepare success/error message and redirect
    if ($success_count > 0 && $error_count == 0) {
        $message = "Successfully added $success_count tickets to Appika!";
        $type = 'success';
    } elseif ($success_count > 0 && $error_count > 0) {
        $message = "Added $success_count tickets to Appika, but failed to add $error_count tickets.";
        $type = 'warning';
    } else {
        $message = "Failed to add any tickets to Appika.";
        $type = 'danger';
    }

    // Anti-resubmit: Redirect after POST to prevent resubmission
    redirectAfterPost($message, $type);
}

if (isset($_POST['add_to_appika_id'])) {
    // Anti-resubmit: Validate CSRF token
    if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
        redirectAfterPost('Security token validation failed. Please try again.', 'danger');
    }

    $ticket_id = intval($_POST['add_to_appika_id']);

    // Anti-resubmit: Create unique action key for this single ticket operation
    $actionKey = 'single_add_' . $ticket_id . '_' . $admin_id;

    // Anti-resubmit: Check if this action was already processed
    if (isActionAlreadyProcessed($actionKey)) {
        redirectAfterPost('This ticket has already been processed.', 'warning');
    }

    // Mark action as being processed
    markActionAsProcessed($actionKey);

    $ticket_query = "SELECT st.*, u.email, u.username FROM support_tickets st JOIN user u ON st.user_id = u.id WHERE st.id = $ticket_id";
    $ticket_result = mysqli_query($conn, $ticket_query);
    if ($ticket = mysqli_fetch_assoc($ticket_result)) {
        // Prepare variables for Appika
        $typeMapping = [ 'starter' => 1, 'premium' => 2, 'ultimate' => 3 ];
        $type = $typeMapping[$ticket['ticket_type']] ?? 1;
        $priorityMapping = [
            'Information' => 'LOW', 'Minor' => 'LOW', 'Important' => 'MEDIUM', 'Critical' => 'HIGH',
            'low' => 'LOW', 'medium' => 'MEDIUM', 'high' => 'HIGH', 'critical' => 'HIGH'
        ];
        $apiPriority = $priorityMapping[$ticket['priority']] ?? 'LOW';
        $mutation = 'mutation CreateTicketByAgent($subject: String!, $reply_msg: String!, $req_email: String, $type: Int!, $type_name: String, $priority: String!, $status: String!, $reply_type: String!, $tags: String) { createTicketByAgent(subject: $subject, reply_msg: $reply_msg, req_email: $req_email, type: $type, type_name: $type_name, priority: $priority, status: $status, reply_type: $reply_type, tags: $tags) { id ticket_no contact_id agent_id req_email subject type type_name priority status created updated } }';
        $variables = [
            'subject' => $ticket['subject'],
            'reply_msg' => $ticket['description'],
            'req_email' => $ticket['email'],
            'type' => $type,
            'type_name' => $ticket['ticket_type'],
            'priority' => $apiPriority,
            'status' => 'OPEN',
            'reply_type' => 'note',
            'tags' => ''
        ];
        $apiResult = makeGraphQLRequest($mutation, $variables);
        if ($apiResult['success'] && isset($apiResult['data']['data']['createTicketByAgent']['id'])) {
            $appikaApiId = $apiResult['data']['data']['createTicketByAgent']['id'];
            $appikaId = str_pad($appikaApiId, 3, '0', STR_PAD_LEFT);
            // Update local ticket
            mysqli_query($conn, "UPDATE support_tickets SET appika_id = '$appikaId' WHERE id = $ticket_id");

            // Anti-resubmit: Redirect with success message
            redirectAfterPost("Ticket ID $ticket_id added to Appika successfully! Appika ID: $appikaId", 'success');
        } else {
            // Anti-resubmit: Redirect with error message
            $errorDetail = '';
            if (isset($apiResult)) {
                $errorDetail = ' (Error ' . ($apiResult['status'] ?? 'Unknown') . ')';
                if (!empty($apiResult['error'])) {
                    $errorDetail .= ': ' . htmlspecialchars($apiResult['error']);
                }
            }
            redirectAfterPost("Failed to add ticket to Appika.$errorDetail", 'danger');
        }
    } else {
        // Anti-resubmit: Redirect with error message
        redirectAfterPost('Ticket not found.', 'danger');
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - All Tickets</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
        }

        .admin-sidebar ul li {
            margin-bottom: 5px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .filter-row {
        margin-bottom: 20px;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
        }

        .filter-row {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
        }

        .filter-row {
            margin-bottom: 12px;
        }
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Ticket Status badges */
    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket Log Status badges */
    .badge-success {
        background-color: #28a745;
        color: #fff;
    }

    .badge-pending {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-fail {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-cancel {
        background-color: #6c757d;
        color: #fff;
    }

    /* Ticket Type badges */
    .badge-starter {
        background-color: #fbbf24;
        color: #fff;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    .badge-low {
        background-color: #28a745;
        color: #fff;

    }

    .badge-medium {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-high {
        background-color: #fd7e14;
        color: #fff;

    }

    .badge-critical {
        background-color: #dc3545;
        color: white;
    }

    .badge-Low {
        background-color: #28a745;
        color: #fff;

    }

    .badge-Normal {
        background-color: #ffc107;
        color: #fff;
    }

    .badge-High {
        background-color: #fd7e14;
        color: #fff;

    }

    .badge-Critical {
        background-color: #dc3545;
        color: white;
    }

    /* Search bar styles from my-ticket-log.php */
    .search-filter-row {
        padding: 0 40px;
    }

    .search-box {
        width: auto;
    }

    .search-box .input-group {
        width: 550px !important;
        max-width: 100% !important;
    }

    .search-input {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        height: 38px;
        font-size: 14px;
    }

    .search-button {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        width: 40px !important;
        min-width: 40px !important;
    }

    .filter-row select {
        width: auto !important;
        padding: 4px 8px;
        height: 38px !important;
        margin-right: 5px !important;
        font-size: 14px;
    }

    .filter-container {
        padding-right: 20px;
        padding-left: 0;
    }

    .filter-container .d-flex {
        justify-content: flex-start !important;
    }

    @media (min-width: 992px) {
        .filter-container select {
            width: 120px !important;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }

    @media (max-width: 991px) {
        .filter-container {
            margin-top: 15px;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .search-box {
            width: 100%;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 500px !important;
            margin: 0 auto;
        }
    }

    @media (max-width: 767px) {
        .search-filter-row {
            padding: 0 15px;
        }

        .search-box .input-group {
            width: 100% !important;
            max-width: 100% !important;
        }

        .search-input {
            font-size: 14px;
        }

        .filter-container {
            padding-left: 15px !important;
            padding-right: 15px !important;
            margin-top: 15px;
        }

        .filter-container select {
            width: 48% !important;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .filter-container .d-flex {
            justify-content: space-between !important;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .search-filter-row {
            padding: 0 10px;
        }

        .search-input::placeholder {
            font-size: 13px;
        }

        .filter-container select {
            font-size: 12px;
        }

        .filter-container {

            margin-top: 10px;
        }
    }

    /* View button styles */
    a.btn.btn-primary.view-btn {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 10px !important;
        border-radius: 4px !important;
        text-align: center !important;
        line-height: 1 !important;
        overflow: hidden !important;
        white-space: nowrap !important;

    }

    /* Table styles for mobile */
    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }

        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 12px;
        }

        a.btn.btn-primary.view-btn {
            width: 35px !important;
            height: 35px !important;
            min-width: 35px !important;
            max-width: 35px !important;
            min-height: 35px !important;
            max-height: 35px !important;
            font-size: 12px !important;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.2rem;
        }

        a.btn.btn-primary.view-btn {
            width: 30px !important;
            height: 30px !important;
            min-width: 30px !important;
            max-width: 30px !important;
            min-height: 30px !important;
            max-height: 30px !important;
            font-size: 10px !important;
        }
    }

    /* APK No column styling */
    .apk-no {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #473BF0;
        font-size: 12px;
        white-space: nowrap;
    }

    .apk-no-empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Add these styles for the dropdown functionality */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        margin-right: 10px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            right: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>All Tickets </h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="filter-row">
                        <form method="GET" class="row">
                            <div class="col-md-6">
                                <div class="search-filter-row">
                                    <div class="search-box">
                                        <div class="input-group">
                                            <input type="text" name="search" class="form-control search-input"
                                                placeholder="Search by ID, APK No, subject, username or date"
                                                value="<?php echo htmlspecialchars($search); ?>">
                                            <div class="input-group-append">
                                                <button type="submit" class="btn btn-primary search-button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 filter-container">
                                <div class="d-flex flex-wrap">
                                    <select name="type" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Types</option>
                                        <option value="starter"
                                            <?php echo $type_filter === 'starter' ? 'selected' : ''; ?>>
                                            Starter</option>
                                        <option value="premium"
                                            <?php echo $type_filter === 'premium' ? 'selected' : ''; ?>>Business
                                        </option>
                                        <option value="ultimate"
                                            <?php echo $type_filter === 'ultimate' ? 'selected' : ''; ?>>Ultimate
                                        </option>
                                    </select>
                                    <select name="status" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Statuses</option>
                                        <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>
                                            Open</option>
                                        <option value="in_progress"
                                            <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In
                                            Progress</option>
                                        <option value="resolved"
                                            <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resolved
                                        </option>
                                        <option value="closed"
                                            <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                    </select>

                                    <select name="priority" class="form-control filter-select mr-2"
                                        onchange="this.form.submit()">
                                        <option value="">All Priorities</option>
                                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>
                                            Information</option>
                                        <option value="medium"
                                            <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Minor
                                        </option>
                                        <option value="high"
                                            <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>Important
                                        </option>
                                        <option value="critical"
                                            <?php echo $priority_filter === 'critical' ? 'selected' : ''; ?>>Critical
                                        </option>
                                    </select>

                                    <select name="severity" class="form-control filter-select"
                                        onchange="this.form.submit()">
                                        <option value="">All Severities</option>
                                        <option value="Low" <?php echo $severity_filter === 'Low' ? 'selected' : ''; ?>>
                                            Information</option>
                                        <option value="Normal"
                                            <?php echo $severity_filter === 'Normal' ? 'selected' : ''; ?>>Minor
                                        </option>
                                        <option value="High"
                                            <?php echo $severity_filter === 'High' ? 'selected' : ''; ?>>Important
                                        </option>
                                        <option value="Critical"
                                            <?php echo $severity_filter === 'Critical' ? 'selected' : ''; ?>>Critical
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Display flash messages -->
                    <?php if (!empty($flash_message)): ?>
                    <div class="alert alert-<?php echo $flash_type === 'danger' ? 'danger' : ($flash_type === 'warning' ? 'warning' : 'success'); ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($flash_message); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <form id="bulkActionForm" method="POST">
                            <!-- Anti-resubmit: CSRF token -->
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                            <div class="bulk-actions mb-3">
                                <button type="submit" name="bulk_add_to_appika" class="btn btn-success"
                                    onclick="return confirm('Add selected tickets to Appika?');"
                                    style="background-color: #473BF0; color: white; font-size: 14px; height: 38px; border: none;">
                                    <i class="fa fa-plus"></i> &nbsp; Add to Appika
                                </button>
                            </div>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" onclick="toggleAllCheckboxes(this)">
                                        </th>
                                        <th>ID</th>
                                        <th>Appika ID</th>
                                        <th>Subject</th>
                                        <th>User</th>
                                        <th>Type</th>
                                        <th>Priority</th>
                                        <th>Severity</th>
                                        <th>Status</th>
                                        <th>Assigned Admin</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (mysqli_num_rows($tickets_result) > 0): ?>
                                    <?php while ($ticket = mysqli_fetch_assoc($tickets_result)): ?>
                                    <tr<?php if (isset($ticket['is_seen_by_admin']) && $ticket['is_seen_by_admin'] == 0) echo ' style="background-color: #f0f7ff;"'; ?>>
                                        <td>
                                            <?php if (empty($ticket['appika_id'])): ?>
                                            <input type="checkbox" name="selected_tickets[]"
                                                value="<?php echo $ticket['id']; ?>" class="ticket-checkbox">
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $ticket['id']; ?>
                                            <?php if (isset($ticket['is_seen_by_admin']) && $ticket['is_seen_by_admin'] == 0): ?>
                                            <span class="badge badge-info"
                                                style="font-size:11px;margin-left:4px;">New</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            if (!empty($ticket['appika_id'])) {
                                                echo '<span class="apk-no">' . htmlspecialchars($ticket['appika_id']) . '</span>';
                                            } else {
                                                echo '<span class="apk-no-empty">-</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                                        <td><?php echo htmlspecialchars($ticket['username']); ?></td>
                                        <td>
                                            <?php
                                                    // Display 'Business' for premium tickets
                                                    $ticketType = $ticket['ticket_type'];
                                                    $badgeClass = $ticketType;
                                                    $displayText = ucfirst($ticketType);

                                                    if (strtolower($ticketType) == 'premium') {
                                                        $displayText = 'Business';
                                                    }
                                                    ?>
                                            <span class="badge badge-<?php echo $badgeClass; ?>">
                                                <?php echo $displayText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $ticket['status']; ?>">
                                                <?php echo ucfirst($ticket['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $ticket['priority']; ?>">
                                                <?php echo ucfirst($ticket['priority']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $ticket['severity']; ?>">
                                                <?php echo $ticket['severity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo !empty($ticket['assigned_admin_name']) ? htmlspecialchars($ticket['assigned_admin_name']) : '<span class="text-muted">Not assigned</span>'; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($ticket['created_at'])); ?></td>
                                        <td>
                                            <a href="#" class="btn btn-primary view-btn"
                                                onclick="markSeenAndRedirect(<?php echo $ticket['id']; ?>)">View</a>
                                            <?php if (empty($ticket['appika_id'])): ?>
                                            <form method="POST" style="display:inline;">
                                                <!-- Anti-resubmit: CSRF token -->
                                                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                                                <input type="hidden" name="add_to_appika_id"
                                                    value="<?php echo $ticket['id']; ?>">
                                                <button type="submit" class="btn btn-success"
                                                    onclick="return confirm('Add this ticket to Appika?');"
                                                    style="font-size:10px; color:white; width:60px; height:40px; min-width:60px; min-height:40px; max-width:60px; max-height:40px; padding:0; display:flex; align-items:center; justify-content:center; border-radius:4px; text-align:center; line-height:1; font-weight:600;">+Appika</button>
                                            </form>
                                            <?php endif; ?>
                                        </td>
                                        </tr>
                                        <?php endwhile; ?>
                                        <?php else: ?>
                                        <tr>
                                            <td colspan="12" class="text-center">No tickets found</td>
                                        </tr>
                                        <?php endif; ?>
                                </tbody>
                            </table>
                        </form>
                    </div>
                    <script>
                    function markSeenAndRedirect(ticketId) {
                        fetch('mark-ticket-seen.php?id=' + ticketId, {
                                method: 'POST'
                            })
                            .then(() => {
                                window.location.href = 'admin-ticket-detail.php?id=' + ticketId;
                            });
                    }

                    function toggleAllCheckboxes(source) {
                        var checkboxes = document.getElementsByClassName('ticket-checkbox');
                        for (var i = 0; i < checkboxes.length; i++) {
                            checkboxes[i].checked = source.checked;
                        }
                    }
                    </script>

                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>&severity=<?php echo urlencode($severity_filter); ?>">Previous</a>
                            </li>

                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>&severity=<?php echo urlencode($severity_filter); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                <a class="page-link"
                                    href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&type=<?php echo urlencode($type_filter); ?>&priority=<?php echo urlencode($priority_filter); ?>&severity=<?php echo urlencode($severity_filter); ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
    </script>

    <!-- Auto-hide flash messages after 5 seconds -->
    <script>
    $(document).ready(function() {
        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    });
    </script>
</body>

</html>