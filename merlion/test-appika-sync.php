<?php
/**
 * Test file for Appika API synchronization
 * This file allows admins to test the centralized sync functionality
 */

session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];

// Include centralized Appika sync functions
require_once '../functions/appika_sync.php';

$message = '';
$message_type = '';
$test_results = [];

// Handle test form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_sync'])) {
    $ticket_id = intval($_POST['ticket_id']);
    $test_type = $_POST['test_type'];
    
    if ($ticket_id > 0) {
        switch ($test_type) {
            case 'status_only':
                $new_status = $_POST['status'];
                $result = syncTicketToAppika($ticket_id, $new_status, null, $admin_username);
                $test_results[] = [
                    'type' => 'Status Update Test',
                    'ticket_id' => $ticket_id,
                    'action' => "Update status to: $new_status",
                    'result' => $result
                ];
                break;
                
            case 'priority_only':
                $new_priority = $_POST['priority'];
                $result = syncTicketToAppika($ticket_id, null, $new_priority, $admin_username);
                $test_results[] = [
                    'type' => 'Priority Update Test',
                    'ticket_id' => $ticket_id,
                    'action' => "Update priority to: $new_priority",
                    'result' => $result
                ];
                break;
                
            case 'both':
                $new_status = $_POST['status'];
                $new_priority = $_POST['priority'];
                $result = syncTicketToAppika($ticket_id, $new_status, $new_priority, $admin_username);
                $test_results[] = [
                    'type' => 'Status & Priority Update Test',
                    'ticket_id' => $ticket_id,
                    'action' => "Update status to: $new_status, priority to: $new_priority",
                    'result' => $result
                ];
                break;
        }
        
        if (!empty($test_results)) {
            $message = 'Test completed! Check results below.';
            $message_type = 'info';
        }
    } else {
        $message = 'Please enter a valid ticket ID.';
        $message_type = 'warning';
    }
}

// Get sample tickets for testing
$tickets_query = "SELECT st.id, st.subject, st.status, st.priority, st.ticket_type, st.appika_id, u.email 
                  FROM support_tickets st 
                  JOIN user u ON st.user_id = u.id 
                  ORDER BY st.id DESC 
                  LIMIT 10";
$tickets_result = mysqli_query($conn, $tickets_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Appika API Sync</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../css/main.css">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-sync-alt"></i> Test Appika API Synchronization</h4>
                        <p class="mb-0 text-muted">Test the centralized sync functionality for Priority and Status updates</p>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="close" data-dismiss="alert">&times;</button>
                        </div>
                        <?php endif; ?>

                        <!-- Test Form -->
                        <form method="POST" class="mb-4">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="ticket_id">Ticket ID:</label>
                                    <input type="number" class="form-control" name="ticket_id" id="ticket_id" required min="1">
                                </div>
                                <div class="col-md-3">
                                    <label for="test_type">Test Type:</label>
                                    <select class="form-control" name="test_type" id="test_type" required onchange="toggleFields()">
                                        <option value="">Select Test Type</option>
                                        <option value="status_only">Status Only</option>
                                        <option value="priority_only">Priority Only</option>
                                        <option value="both">Status & Priority</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="status_field" style="display: none;">
                                    <label for="status">Status:</label>
                                    <select class="form-control" name="status" id="status">
                                        <option value="open">Open</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="resolved">Resolved</option>
                                        <option value="closed">Closed</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="priority_field" style="display: none;">
                                    <label for="priority">Priority:</label>
                                    <select class="form-control" name="priority" id="priority">
                                        <option value="low">Low</option>
                                        <option value="medium">Normal (Medium in Appika)</option>
                                        <option value="high">Important (High in Appika)</option>
                                        <option value="critical">Critical (Urgent in Appika)</option>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" name="test_sync" class="btn btn-primary">
                                        <i class="fas fa-play"></i> Run Test
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Test Results -->
                        <?php if (!empty($test_results)): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> Test Results</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($test_results as $test): ?>
                                <div class="border p-3 mb-3 rounded">
                                    <h6 class="text-primary"><?php echo htmlspecialchars($test['type']); ?></h6>
                                    <p><strong>Ticket ID:</strong> <?php echo $test['ticket_id']; ?></p>
                                    <p><strong>Action:</strong> <?php echo htmlspecialchars($test['action']); ?></p>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Local Database:</h6>
                                            <span class="badge badge-<?php echo $test['result']['success'] ? 'success' : 'danger'; ?>">
                                                <?php echo $test['result']['success'] ? 'Success' : 'Failed'; ?>
                                            </span>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Appika API:</h6>
                                            <span class="badge badge-<?php echo $test['result']['appika_updated'] ? 'success' : 'warning'; ?>">
                                                <?php echo $test['result']['appika_updated'] ? 'Synced' : 'Not Synced'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <strong>Message:</strong> 
                                        <span class="text-<?php echo $test['result']['appika_updated'] ? 'success' : 'warning'; ?>">
                                            <?php echo htmlspecialchars($test['result']['message']); ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Sample Tickets -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> Recent Tickets (for testing)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Priority</th>
                                                <th>Type</th>
                                                <th>Appika ID</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($ticket = mysqli_fetch_assoc($tickets_result)): ?>
                                            <tr>
                                                <td><?php echo $ticket['id']; ?></td>
                                                <td><?php echo htmlspecialchars(substr($ticket['subject'], 0, 30)) . '...'; ?></td>
                                                <td><span class="badge badge-info"><?php echo $ticket['status']; ?></span></td>
                                                <td><span class="badge badge-secondary"><?php echo $ticket['priority']; ?></span></td>
                                                <td><span class="badge badge-primary"><?php echo $ticket['ticket_type']; ?></span></td>
                                                <td><?php echo $ticket['appika_id'] ?: 'N/A'; ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="fillTicketId(<?php echo $ticket['id']; ?>)">
                                                        Use ID
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <a href="admin-tickets.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Tickets
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleFields() {
            const testType = document.getElementById('test_type').value;
            const statusField = document.getElementById('status_field');
            const priorityField = document.getElementById('priority_field');
            
            statusField.style.display = 'none';
            priorityField.style.display = 'none';
            
            if (testType === 'status_only' || testType === 'both') {
                statusField.style.display = 'block';
            }
            if (testType === 'priority_only' || testType === 'both') {
                priorityField.style.display = 'block';
            }
        }
        
        function fillTicketId(ticketId) {
            document.getElementById('ticket_id').value = ticketId;
        }
    </script>
</body>
</html>
