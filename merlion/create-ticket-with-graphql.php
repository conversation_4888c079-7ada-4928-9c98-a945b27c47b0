<?php
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: index");
    exit();
}

$username = $_SESSION['username'];

// Fetch user info
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

// Initialize message
$message = "";
$messageType = "";

// Flag to show success animation
$showSuccessAnimation = false;

// Load centralized API configuration
require_once '../config/api-config.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

// Function to make GraphQL requests
function makeGraphQLRequest($query, $variables = []) {
    global $graphqlEndpoint, $apiKey;

    $client = new \GuzzleHttp\Client([
        'timeout' => 30,
        'http_errors' => false,
    ]);

    try {
        $response = $client->post($graphqlEndpoint, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                // 'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => [
                'query' => $query,
                'variables' => $variables
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        return [
            'success' => ($statusCode >= 200 && $statusCode < 300 && !isset($data['errors'])),
            'status' => $statusCode,
            'data' => $data,
            'error' => isset($data['errors']) ? json_encode($data['errors']) : null
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to generate ticket number
function generateTicketNumber($ticketId) {
    return 'HT' . str_pad($ticketId, 6, '0', STR_PAD_LEFT);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $subject = mysqli_real_escape_string($conn, $_POST['subject'] ?? '');
    $priority = mysqli_real_escape_string($conn, $_POST['priority'] ?? '');
    $ticketType = mysqli_real_escape_string($conn, $_POST['ticket_type'] ?? '');
    $severity = mysqli_real_escape_string($conn, $_POST['severity'] ?? '');
    $problemType = mysqli_real_escape_string($conn, $_POST['problem_type'] ?? '');
    $description = mysqli_real_escape_string($conn, $_POST['description'] ?? '');

    // Validate problem type is not empty
    if (empty($problemType)) {
        $message = "Please select a problem type.";
        $messageType = "danger";
    } else {
        // Check if user has remaining tickets of selected type
        $remaining = $user[$ticketType . '_tickets'] ?? 0;

        if ($remaining > 0) {
            // Step 1: Create ticket in Appika API first to get the API ID
            // Map ticket type to API format
            $typeMapping = [
                'starter' => 1,
                'premium' => 2,
                'ultimate' => 3
            ];
            $type = $typeMapping[$ticketType] ?? 1;

            // Map priority to ALL CAPS
            $priorityMapping = [
                'Information' => 'LOW',
                'Minor' => 'LOW',
                'Important' => 'MEDIUM',
                'Critical' => 'HIGH'
            ];
            $apiPriority = $priorityMapping[$priority] ?? 'LOW';

            // Create GraphQL mutation
            $mutation = '
            mutation CreateTicketByAgent(
              $subject: String!,
              $reply_msg: String!,
              $req_email: String,
              $type: Int!,
              $type_name: String,
              $priority: String!,
              $status: String!,
              $reply_type: String!,
              $tags: String
            ) {
              createTicketByAgent(
                subject: $subject,
                reply_msg: $reply_msg,
                req_email: $req_email,
                type: $type,
                type_name: $type_name,
                priority: $priority,
                status: $status,
                reply_type: $reply_type,
                tags: $tags
              ) {
                id
                ticket_no
                contact_id
                agent_id
                req_email
                subject
                type
                type_name
                priority
                status
                created
                updated
              }
            }';

            $variables = [
                'subject' => $subject,
                'reply_msg' => $description,
                'req_email' => $user['email'],
                'type' => $type,
                'type_name' => $ticketType,
                'priority' => $apiPriority,
                'status' => 'OPEN',
                'reply_type' => 'note',
                'tags' => ''
            ];

            // Step 2: Send to GraphQL API first
            $apiResult = makeGraphQLRequest($mutation, $variables);

            // Create logs directory if it doesn't exist
            if (!file_exists("../logs")) {
                mkdir("../logs", 0755, true);
            }

            if ($apiResult['success'] && isset($apiResult['data']['data']['createTicketByAgent']['id'])) {
                // Step 3: Get Appika ID from API response and generate appika_id
                $appikaApiId = $apiResult['data']['data']['createTicketByAgent']['id'];
                $appikaId = 'HT' . str_pad($appikaApiId, 3, '0', STR_PAD_LEFT); // e.g., HT076

                // Step 4: Insert ticket into local database with appika_id
                $insert = "INSERT INTO support_tickets (user_id, appika_id, subject, priority, ticket_type, severity, problem_type, description, status, created_at)
                           VALUES ($userId, '$appikaId', '$subject', '$priority', '$ticketType', '$severity', '$problemType', '$description', 'open', NOW())";

                if (mysqli_query($conn, $insert)) {
                    // Get the newly created local ticket ID
                    $ticketId = mysqli_insert_id($conn);
                    $ticketNumber = generateTicketNumber($ticketId);

                    // Log API result for debugging
                    $log_file = fopen("../logs/ticket_api.log", "a");
                    fwrite($log_file, date('Y-m-d H:i:s') . " - Local ID: $ticketId - Appika ID: $appikaId - API Result: " . json_encode($apiResult) . "\n");
                    fclose($log_file);

                    // Step 5: Decrease ticket count and create log entry
                    $update = "UPDATE user SET {$ticketType}_tickets = {$ticketType}_tickets - 1 WHERE id = $userId";
                    mysqli_query($conn, $update);

                    // Create log entry in ticket_logs table with UTC time
                    $action = "success";
                    $logDescription = "Created new {$ticketType} ticket - 1 Ticket (Synced to API)";
                    $amount = "1";
                    $created_at_utc = getCurrentUTC(); // Get UTC time for consistent storage
                    $logInsert = "INSERT INTO ticket_logs (ticket_id, action, description, user_id, ticket_type, amount, created_at)
                                  VALUES ($ticketId, '$action', '$logDescription', $userId, '$ticketType', '$amount', '$created_at_utc')";
                    mysqli_query($conn, $logInsert);

                    // Set success message
                    $message = "Ticket created successfully and synced to API! Appika ID: $appikaId, Local ID: $ticketNumber";
                    $messageType = "success";
                    $showSuccessAnimation = true;
                } else {
                    $message = "API ticket created but failed to save to local database. Appika ID: $appikaId";
                    $messageType = "warning";
                }
            } else {
                // API failed, create ticket locally without appika_id
                $insert = "INSERT INTO support_tickets (user_id, subject, priority, ticket_type, severity, problem_type, description, status, created_at)
                           VALUES ($userId, '$subject', '$priority', '$ticketType', '$severity', '$problemType', '$description', 'open', NOW())";

                if (mysqli_query($conn, $insert)) {
                    $ticketId = mysqli_insert_id($conn);
                    $ticketNumber = generateTicketNumber($ticketId);

                    // Log API failure
                    $log_file = fopen("../logs/ticket_api.log", "a");
                    fwrite($log_file, date('Y-m-d H:i:s') . " - Local ID: $ticketId - API Failed: " . json_encode($apiResult) . "\n");
                    fclose($log_file);

                    // Decrease ticket count
                    $update = "UPDATE user SET {$ticketType}_tickets = {$ticketType}_tickets - 1 WHERE id = $userId";
                    mysqli_query($conn, $update);

                    // Create log entry with UTC time
                    $action = "success";
                    $logDescription = "Created new {$ticketType} ticket - 1 Ticket (API sync failed)";
                    $amount = "1";
                    $created_at_utc = getCurrentUTC(); // Get UTC time for consistent storage
                    $logInsert = "INSERT INTO ticket_logs (ticket_id, action, description, user_id, ticket_type, amount, created_at)
                                  VALUES ($ticketId, '$action', '$logDescription', $userId, '$ticketType', '$amount', '$created_at_utc')";
                    mysqli_query($conn, $logInsert);

                    $message = "Ticket created locally but API sync failed. Local ID: $ticketNumber";
                    $messageType = "warning";
                } else {
                    $message = "Failed to create ticket in both API and database.";
                    $messageType = "danger";
                }
            }
        } else {
            $message = "You don't have remaining $ticketType tickets.";
            $messageType = "danger";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Create New Support Ticket (with GraphQL)</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <style>
    body {
        padding-top: 200px;
    }

    .container {
        width: 1200px;
        max-width: 95%;
    }

    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    @media (max-width: 991px) {
        body {
            padding-top: 150px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
        }
    }

    .api-status {
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-weight: bold;
    }

    .api-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .api-warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .api-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    </style>
</head>

<body>

    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content -->
            <div class="col-lg-9 col-md-8">
                <h2 class="text-center mb-4">Create New Support Ticket</h2>
                <div class="api-status api-success">
                    <i class="fas fa-sync-alt"></i> Enhanced with GraphQL API Integration
                    <small class="d-block mt-1">Tickets are saved to both local database and Appika API</small>
                </div>

                <?php if (!empty($message)) : ?>
                <div class="alert alert-<?php echo $messageType; ?> text-center">
                    <?php echo $message; ?>
                </div>
                <?php endif; ?>

                <form method="POST" id="ticketForm">
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" name="subject" class="form-control" required>
                    </div>

                    <!-- Form fields in a row -->
                    <div class="form-row">
                        <!-- Severity field -->
                        <div class="form-group col-md-4">
                            <label for="severity">Severity</label>
                            <select name="severity" class="form-control" required>
                                <option value="">Select a severity level</option>
                                <option value="Information">Information (5 Business days)</option>
                                <option value="Minor">Minor (2 Business days)</option>
                                <option value="Important">Important (8 Business hours)</option>
                                <option value="Critical">Critical (4 Business hours)</option>
                            </select>
                        </div>

                        <!-- Priority field -->
                        <div class="form-group col-md-4">
                            <label for="priority">Priority</label>
                            <select name="priority" class="form-control" required>
                                <option value="">Select a priority level</option>
                                <option value="Information">Information → LOW</option>
                                <option value="Minor">Minor → LOW</option>
                                <option value="Important">Important → MEDIUM</option>
                                <option value="Critical">Critical → HIGH</option>
                            </select>
                            <small class="form-text text-muted">Shows API mapping</small>
                        </div>

                        <!-- Ticket Type field -->
                        <div class="form-group col-md-4">
                            <label for="ticket_type">Ticket Type</label>
                            <select name="ticket_type" id="ticket_type" class="form-control" required>
                                <?php if ($user['starter_tickets'] > 0): ?>
                                <option value="starter">Starter (<?php echo $user['starter_tickets']; ?> remaining)
                                </option>
                                <?php endif; ?>
                                <?php if ($user['premium_tickets'] > 0): ?>
                                <option value="premium">Premium (<?php echo $user['premium_tickets']; ?> remaining)
                                </option>
                                <?php endif; ?>
                                <?php if ($user['ultimate_tickets'] > 0): ?>
                                <option value="ultimate">Ultimate (<?php echo $user['ultimate_tickets']; ?> remaining)
                                </option>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">Maps to API: starter→1, premium→2, ultimate→3</small>
                        </div>
                    </div>

                    <!-- Problem Type -->
                    <div class="form-group">
                        <label for="problem_type">Problem Type</label>
                        <select name="problem_type" class="form-control" required>
                            <option value="">Select a problem type...</option>
                            <option value="Account Access">Account Access</option>
                            <option value="Application Error">Application Error</option>
                            <option value="Hardware Issue">Hardware Issue</option>
                            <option value="Network Problem">Network Problem</option>
                            <option value="Software Issue">Software Issue</option>
                            <option value="Email Problem">Email Problem</option>
                            <option value="Performance Issue">Performance Issue</option>
                            <option value="Security Concern">Security Concern</option>
                            <option value="Other">Other</option>
                        </select>
                        <small class="form-text text-muted">Used as tag in API</small>
                    </div>

                    <!-- Problem Description -->
                    <div class="form-group">
                        <label for="description">Problem Description</label>
                        <textarea name="description" class="form-control" rows="5" required
                            placeholder="Please describe your issue in detail."></textarea>
                        <small class="form-text text-muted">Sent as reply_msg to API</small>
                    </div>

                    <div class="form-group row mt-4">
                        <div class="col-md-12 d-flex justify-content-center align-items-center">
                            <a href="my-ticket.php" class="btn btn-danger btn-lg mr-2">Cancel</a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus"></i> Create Ticket (DB + API)
                            </button>
                        </div>
                    </div>
                </form>

                <!-- API Integration Info -->
                <div class="mt-4 p-3" style="background-color: #f8f9fa; border-radius: 5px;">
                    <h5><i class="fas fa-info-circle"></i> GraphQL Integration Details</h5>
                    <ul class="mb-0">
                        <li><strong>Database:</strong> Saves to local support_tickets table</li>
                        <li><strong>API:</strong> Creates ticket via createTicketByAgent mutation</li>
                        <li><strong>Mapping:</strong> Converts local fields to API format</li>
                        <li><strong>Logging:</strong> Records API sync status in ticket_logs</li>
                        <li><strong>Fallback:</strong> Works even if API is unavailable</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Animation Modal -->
    <?php if ($showSuccessAnimation) : ?>
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="success-animation">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h3 class="mt-4">Ticket Created Successfully!</h3>
                    <p class="mb-4">Your support ticket has been created and synced to the API.</p>
                    <p class="text-muted">Redirecting to My Tickets in <span id="countdown">3</span> seconds...</p>
                    <a href="my-ticket.php" class="btn btn-primary">View My Tickets Now</a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <style>
    /* Success Animation Styles */
    .success-animation {
        margin: 0 auto;
        width: 100px;
        height: 100px;
    }

    .checkmark {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #4bb71b;
        stroke-miterlimit: 10;
        box-shadow: inset 0px 0px 0px #4bb71b;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 1;
        stroke-miterlimit: 2;
        stroke: #4bb71b;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {

        0%,
        100% {
            transform: none;
        }

        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 1px #4bb71b;
        }
    }
    </style>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>

    <script>
    // Show success modal on page load if needed
    <?php if ($showSuccessAnimation) : ?>
    $(document).ready(function() {
        $('#successModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Start countdown for automatic redirect
        var countdownElement = document.getElementById('countdown');
        var secondsLeft = 3;

        var countdownInterval = setInterval(function() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            if (secondsLeft <= 0) {
                clearInterval(countdownInterval);
                window.location.href = 'my-ticket.php';
            }
        }, 1000);

        $('#successModal').on('hidden.bs.modal', function() {
            clearInterval(countdownInterval);
            window.location.href = 'my-ticket.php';
        });
    });
    <?php endif; ?>
    </script>

</body>

</html>