<?php
/**
 * Debug script to test email receipts with multiple cart items
 */

include_once(__DIR__ . '/server.php');
include_once(__DIR__ . '/send-purchase-receipt.php');

echo "<h2>🔍 Debug: Multiple Items Cart Purchase</h2>";

// Test with a transaction that has multiple items
$test_transaction = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';

if (empty($test_transaction)) {
    echo "<h3>📋 Recent Multi-Item Transactions</h3>";
    
    try {
        // Find transactions with multiple items
        $stmt = $conn->prepare("
            SELECT pt.transactionid, COUNT(*) as item_count, 
                   MIN(pt.purchase_time) as purchase_time,
                   u.email, u.first_name, u.last_name
            FROM purchasetickets pt 
            LEFT JOIN user u ON pt.username = u.username 
            WHERE pt.transactionid IS NOT NULL 
            GROUP BY pt.transactionid 
            HAVING COUNT(*) > 1 
            ORDER BY MIN(pt.purchase_time) DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<p>Select a multi-item transaction to test:</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>Transaction ID</th>";
            echo "<th style='padding: 8px;'>Items</th>";
            echo "<th style='padding: 8px;'>Date</th>";
            echo "<th style='padding: 8px;'>Customer</th>";
            echo "<th style='padding: 8px;'>Email</th>";
            echo "<th style='padding: 8px;'>Test</th>";
            echo "</tr>";
            
            while ($row = $result->fetch_assoc()) {
                $customer_name = trim(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? ''));
                if (empty($customer_name)) {
                    $customer_name = 'N/A';
                }
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($row['transactionid'], -12)) . "</td>";
                echo "<td style='padding: 8px; text-align: center; font-weight: bold; color: red;'>" . $row['item_count'] . " items</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['purchase_time']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer_name) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 8px;'>";
                echo "<a href='?transaction_id=" . urlencode($row['transactionid']) . "' style='color: blue;'>Debug This</a>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No multi-item transactions found. Try making a purchase with multiple items in the cart.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    // Debug specific transaction
    echo "<h3>🔍 Debugging Transaction: " . htmlspecialchars(substr($test_transaction, -12)) . "</h3>";
    
    try {
        // Get transaction details
        $stmt = $conn->prepare("
            SELECT pt.*, u.first_name, u.last_name, u.email, u.company_name, u.timezone, u.id as user_id
            FROM purchasetickets pt 
            LEFT JOIN user u ON pt.username = u.username 
            WHERE pt.transactionid = ? 
            ORDER BY pt.purchase_time DESC
        ");
        $stmt->bind_param("s", $test_transaction);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
            echo "❌ No purchase found for this transaction ID.";
            echo "</div>";
        } else {
            $purchases = [];
            $customerInfo = null;
            $totalAmount = 0;
            $totalTickets = 0;
            
            echo "<h4>📦 Items in this transaction:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th style='padding: 8px;'>Item</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Package</th>";
            echo "<th style='padding: 8px;'>Tickets</th>";
            echo "<th style='padding: 8px;'>Price</th>";
            echo "</tr>";
            
            $itemCount = 0;
            while ($row = $result->fetch_assoc()) {
                $itemCount++;
                $purchases[] = $row;
                if (!$customerInfo) {
                    $customerInfo = $row;
                }
                $totalAmount += $row['dollar_price_per_package'];
                $totalTickets += $row['remaining_tickets'];
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>#" . $itemCount . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['ticket_type']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['package_size']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['numbers_per_package']) . "</td>";
                echo "<td style='padding: 8px;'>$" . htmlspecialchars($row['dollar_price_per_package']) . "</td>";
                echo "</tr>";
            }
            
            echo "<tr style='background: #e8f5e8; font-weight: bold;'>";
            echo "<td colspan='3' style='padding: 8px;'>TOTAL</td>";
            echo "<td style='padding: 8px;'>" . $totalTickets . "</td>";
            echo "<td style='padding: 8px;'>$" . number_format($totalAmount, 2) . "</td>";
            echo "</tr>";
            echo "</table>";
            
            echo "<h4>👤 Customer Info:</h4>";
            $customerName = trim(($customerInfo['first_name'] ?? '') . ' ' . ($customerInfo['last_name'] ?? ''));
            if (empty($customerName)) {
                $customerName = $customerInfo['username'] ?? 'N/A';
            }
            
            echo "<ul>";
            echo "<li><strong>Name:</strong> " . htmlspecialchars($customerName) . "</li>";
            echo "<li><strong>Email:</strong> " . htmlspecialchars($customerInfo['email'] ?? 'N/A') . "</li>";
            echo "<li><strong>Username:</strong> " . htmlspecialchars($customerInfo['username'] ?? 'N/A') . "</li>";
            echo "</ul>";
            
            // Test email sending
            echo "<h4>📧 Testing Email Receipt:</h4>";
            
            if (empty($customerInfo['email'])) {
                echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
                echo "❌ <strong>PROBLEM FOUND!</strong> No email address for this customer.";
                echo "</div>";
            } else {
                try {
                    $receiptSent = sendPurchaseReceiptEmail($test_transaction, $customerInfo['email']);
                    
                    if ($receiptSent) {
                        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0;'>";
                        echo "✅ <strong>SUCCESS!</strong> Receipt email sent successfully to " . htmlspecialchars($customerInfo['email']);
                        echo "</div>";
                    } else {
                        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
                        echo "❌ <strong>FAILED!</strong> Could not send receipt email.";
                        echo "<br>Check server error logs for details.";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
                    echo "❌ <strong>ERROR!</strong> " . htmlspecialchars($e->getMessage());
                    echo "</div>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0;'>";
        echo "❌ <strong>Database Error:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

echo "<h3>🔍 Common Issues with Multiple Items:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd;'>";
echo "<ol>";
echo "<li><strong>Missing Email:</strong> Customer record doesn't have email address</li>";
echo "<li><strong>Transaction ID Mismatch:</strong> Items have different transaction IDs</li>";
echo "<li><strong>Database Connection:</strong> Connection issues during email sending</li>";
echo "<li><strong>Email Server:</strong> Mail server not configured properly</li>";
echo "<li><strong>PHP Errors:</strong> Errors in email generation code</li>";
echo "</ol>";
echo "</div>";

echo "<h3>📝 How to Test:</h3>";
echo "<ol>";
echo "<li>Add multiple different tickets to cart</li>";
echo "<li>Complete purchase</li>";
echo "<li>Check if transaction appears in the list above</li>";
echo "<li>Use the 'Debug This' link to test email sending</li>";
echo "<li>Check server error logs for any issues</li>";
echo "</ol>";

echo "<p><a href='test-receipt-email.php' style='color: blue;'>← Back to Main Testing</a></p>";
?>
