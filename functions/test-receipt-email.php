<?php
/**
 * Enhanced Receipt Email Test Tool
 * Use this to test if receipt emails are working on your server
 */

// Include the receipt email function
include_once(__DIR__ . '/send-purchase-receipt.php');
include_once(__DIR__ . '/server.php');

echo "<h2>🧾 Enhanced Receipt Email Test Tool</h2>";

// Test receipt email if parameters provided
$test_transaction = isset($_GET['test_transaction']) ? $_GET['test_transaction'] : '';
$test_email = isset($_GET['test_email']) ? $_GET['test_email'] : '';

if ($test_transaction && $test_email) {
    echo "<h3>📧 Testing Receipt Email</h3>";

    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; margin: 10px 0;'>";
    echo "<p><strong>Transaction ID:</strong> " . htmlspecialchars($test_transaction) . "</p>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($test_email) . "</p>";
    echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";

    try {
        // Test the receipt email function
        $result = sendPurchaseReceiptEmail($test_transaction, $test_email);

        if ($result) {
            echo "<div style='color: green; padding: 15px; border: 1px solid green; background: #f0fff0; margin: 10px 0;'>";
            echo "<h4>✅ Success!</h4>";
            echo "<p>Receipt email was sent successfully!</p>";
            echo "<p><strong>Check the following:</strong></p>";
            echo "<ul>";
            echo "<li>Check the recipient's inbox</li>";
            echo "<li>Check spam/junk folder</li>";
            echo "<li>Check server error logs for any warnings</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 15px; border: 1px solid red; background: #fff0f0; margin: 10px 0;'>";
            echo "<h4>❌ Failed!</h4>";
            echo "<p>Receipt email failed to send.</p>";
            echo "<p><strong>Troubleshooting steps:</strong></p>";
            echo "<ul>";
            echo "<li>Check server error logs</li>";
            echo "<li>Verify email configuration</li>";
            echo "<li>Test basic email functionality</li>";
            echo "<li>Contact hosting provider about mail server</li>";
            echo "</ul>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; border: 1px solid red; background: #fff0f0; margin: 10px 0;'>";
        echo "<h4>❌ Error!</h4>";
        echo "<p>Exception occurred: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

// Show recent transactions for testing
echo "<h3>📋 Recent Transactions (for testing)</h3>";

try {
    $stmt = $conn->prepare("
        SELECT DISTINCT pt.transactionid, pt.purchase_time, u.email, u.first_name, u.last_name 
        FROM purchasetickets pt 
        LEFT JOIN user u ON pt.username = u.username 
        WHERE pt.transactionid IS NOT NULL 
        ORDER BY pt.purchase_time DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Date</th>";
        echo "<th style='padding: 8px;'>Customer</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Test</th>";
        echo "</tr>";
        
        while ($row = $result->fetch_assoc()) {
            $customer_name = trim(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? ''));
            if (empty($customer_name)) {
                $customer_name = 'N/A';
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transactionid']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['purchase_time']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer_name) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='?transaction_id=" . urlencode($row['transactionid']) . "&email=" . urlencode($row['email'] ?? $test_email) . "' style='color: blue;'>Test This</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No recent transactions found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching transactions: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>📝 Usage Instructions</h3>";
echo "<ol>";
echo "<li>Use the 'Test This' links above to test with real transaction data</li>";
echo "<li>Or manually add parameters: <code>?transaction_id=YOUR_ID&email=<EMAIL></code></li>";
echo "<li>Check your email inbox and spam folder for the receipt</li>";
echo "<li>Check server error logs for any issues</li>";
echo "</ol>";

echo "<h3>⚙️ Email Configuration</h3>";
echo "<p>Make sure your server's mail configuration is working. Check the EMAIL_SETUP_GUIDE.md for setup instructions.</p>";
?>
