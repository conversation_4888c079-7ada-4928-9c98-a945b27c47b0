<?php
/**
 * Enhanced Email Configuration for HelloIT
 * Automatically detects server environment and provides appropriate email settings
 */

class EmailConfig {
    private static $config = null;
    
    /**
     * Get email configuration based on environment
     */
    public static function getConfig() {
        if (self::$config !== null) {
            return self::$config;
        }
        
        // Detect environment
        $isLocalhost = self::isLocalhost();
        
        if ($isLocalhost) {
            // LOCALHOST CONFIGURATION
            self::$config = [
                'environment' => 'localhost',
                'method' => 'mail', // Use PHP mail() function
                'from_email' => '<EMAIL>',
                'from_name' => 'HelloIT',
                'reply_to' => '<EMAIL>',
                'smtp' => [
                    'enabled' => false, // Disable SMTP for localhost
                    'host' => 'localhost',
                    'port' => 25,
                    'username' => '',
                    'password' => '',
                    'encryption' => 'none'
                ],
                'fallback' => [
                    'enabled' => true,
                    'log_file' => __DIR__ . '/../logs/email-fallback.log'
                ]
            ];
        } else {
            // PRODUCTION SERVER CONFIGURATION
            self::$config = [
                'environment' => 'production',
                'method' => 'mail', // Use server's mail() function
                'from_email' => '<EMAIL>',
                'from_name' => 'HelloIT',
                'reply_to' => '<EMAIL>',
                'smtp' => [
                    'enabled' => false, // Can be enabled if needed
                    'host' => 'mail.helloit.io', // Update with actual SMTP server
                    'port' => 587,
                    'username' => '<EMAIL>',
                    'password' => '', // Set actual password if using SMTP
                    'encryption' => 'tls'
                ],
                'fallback' => [
                    'enabled' => true,
                    'log_file' => __DIR__ . '/../logs/email-fallback.log'
                ]
            ];
        }
        
        return self::$config;
    }
    
    /**
     * Check if running on localhost
     */
    private static function isLocalhost() {
        $localhost_indicators = [
            $_SERVER['HTTP_HOST'] === 'localhost',
            strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false,
            strpos($_SERVER['HTTP_HOST'], 'localhost:') !== false,
            $_SERVER['SERVER_NAME'] === 'localhost',
            strpos($_SERVER['DOCUMENT_ROOT'], 'xampp') !== false,
            strpos($_SERVER['DOCUMENT_ROOT'], 'wamp') !== false
        ];
        
        return in_array(true, $localhost_indicators, true);
    }
    
    /**
     * Send email with automatic fallback
     */
    public static function sendEmail($to, $subject, $message, $isHtml = true) {
        $config = self::getConfig();
        
        // Validate email
        if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
            self::logError("Invalid email address: $to");
            return false;
        }
        
        // Prepare headers
        $headers = self::buildHeaders($config, $isHtml);
        
        // Log attempt
        self::logInfo("Attempting to send email to: $to, Subject: $subject");
        
        // Try to send email
        $success = false;
        
        if ($config['smtp']['enabled']) {
            // Use SMTP (if implemented)
            $success = self::sendViaSMTP($to, $subject, $message, $headers, $config);
        } else {
            // Use PHP mail() function
            $success = @mail($to, $subject, $message, implode("\r\n", $headers));
        }
        
        if ($success) {
            self::logInfo("Email sent successfully to: $to");
        } else {
            self::logError("Failed to send email to: $to");
            
            // Fallback: Log email content
            if ($config['fallback']['enabled']) {
                self::logEmailFallback($to, $subject, $message, $config);
            }
        }
        
        return $success;
    }
    
    /**
     * Build email headers
     */
    private static function buildHeaders($config, $isHtml = true) {
        $headers = [];
        $headers[] = "From: {$config['from_name']} <{$config['from_email']}>";
        $headers[] = "Reply-To: {$config['reply_to']}";
        $headers[] = "Return-Path: {$config['from_email']}";
        $headers[] = "X-Mailer: PHP/" . phpversion();
        $headers[] = "MIME-Version: 1.0";
        
        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }
        
        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "X-Priority: 3";
        $headers[] = "X-MSMail-Priority: Normal";
        
        return $headers;
    }
    
    /**
     * Send via SMTP (placeholder for future implementation)
     */
    private static function sendViaSMTP($to, $subject, $message, $headers, $config) {
        // This would require PHPMailer or similar library
        // For now, fall back to mail() function
        return @mail($to, $subject, $message, implode("\r\n", $headers));
    }
    
    /**
     * Log email to file as fallback
     */
    private static function logEmailFallback($to, $subject, $message, $config) {
        $logFile = $config['fallback']['log_file'];
        $logDir = dirname($logFile);
        
        // Create logs directory if it doesn't exist
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'to' => $to,
            'subject' => $subject,
            'message' => $message,
            'environment' => $config['environment']
        ];
        
        $logContent = "=== EMAIL FALLBACK LOG ===\n";
        $logContent .= "Time: " . $logEntry['timestamp'] . "\n";
        $logContent .= "To: " . $logEntry['to'] . "\n";
        $logContent .= "Subject: " . $logEntry['subject'] . "\n";
        $logContent .= "Environment: " . $logEntry['environment'] . "\n";
        $logContent .= "Message:\n" . $logEntry['message'] . "\n";
        $logContent .= "========================\n\n";
        
        @file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Log info message
     */
    private static function logInfo($message) {
        error_log("EmailConfig INFO: $message");
    }
    
    /**
     * Log error message
     */
    private static function logError($message) {
        error_log("EmailConfig ERROR: $message");
    }
}
?>
