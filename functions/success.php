<?php
session_start();
require '../vendor/autoload.php';
include('../functions/server.php');

// Enable error logging
ini_set('display_errors', 1);
error_reporting(E_ALL);

\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

$session_id = $_GET['session_id'] ?? '';
$messageSuccess = '';
$messageFail = '';

if (!$session_id) {
    $messageFail = "Invalid session ID.";
} else {
    try {
        // Retrieve the session from Stripe
        $session = \Stripe\Checkout\Session::retrieve([
            'id' => $session_id,
            'expand' => ['payment_intent', 'customer']
        ]);

        // Get metadata from session
        $metadata = $session->metadata->toArray();
        error_log("Session metadata: " . print_r($metadata, true));

        // Check if this transaction has already been processed
        $check_query = "SELECT purchaseid FROM purchasetickets WHERE transactionid = ? LIMIT 1";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param("s", $session_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Transaction already processed
            error_log("Transaction already processed: $session_id");
            $messageSuccess = "Thank you for your purchase! Your tickets have been added to your account.";
        } else {
            // Process the purchase
            if (isset($metadata['username']) && !empty($metadata['username'])) {
                $username = $metadata['username'];

                // Get purchase items from metadata
                $purchased_items = [];
                if (isset($metadata['cart']) && !empty($metadata['cart'])) {
                    $purchased_items = json_decode($metadata['cart'], true);
                } elseif (isset($metadata['cart_id'])) {
                    // This is a cart purchase, parse items from metadata
                    $i = 0;
                    while (isset($metadata['item_' . $i . '_ticket_type'])) {
                        $purchased_items[] = [
                            'ticket_type' => $metadata['item_' . $i . '_ticket_type'],
                            'package_size' => $metadata['item_' . $i . '_package_size'],
                            'numbers_per_package' => $metadata['item_' . $i . '_numbers_per_package'] ?? 1,
                            'dollar_price_per_package' => 0, // Will be fetched from database
                            'quantity' => $metadata['item_' . $i . '_quantity'] ?? 1
                        ];
                        $i++;
                    }
                } else {
                    // Single item purchase
                    $purchased_items[] = [
                        'ticket_type' => $metadata['ticket_type'] ?? '',
                        'package_size' => $metadata['packagesize'] ?? '',
                        'numbers_per_package' => 1,
                        'dollar_price_per_package' => 0,
                        'quantity' => 1
                    ];
                }

                if (!empty($purchased_items)) {
                    // Begin transaction
                    $conn->begin_transaction();

                    try {
                        foreach ($purchased_items as $item) {
                            $ticket_type = $item['ticket_type'];
                            $package_size = $item['package_size'];
                            $numbers_per_package = $item['numbers_per_package'];
                            $quantity = $item['quantity'];
                            $total_tickets = $numbers_per_package * $quantity;

                            // Get ticket price from database
                            $ticket_query = "SELECT dollar_price_per_package FROM tickets WHERE package_size = ? AND ticket_type = ?";
                            $ticket_stmt = $conn->prepare($ticket_query);
                            $ticket_stmt->bind_param("ss", $package_size, $ticket_type);
                            $ticket_stmt->execute();
                            $ticket_result = $ticket_stmt->get_result();

                            if ($ticket_result->num_rows > 0) {
                                $ticket_data = $ticket_result->fetch_assoc();
                                $dollar_price_per_package = $ticket_data['dollar_price_per_package'];
                            } else {
                                $dollar_price_per_package = $item['dollar_price_per_package'] ?? 0;
                            }

                            // Update user's ticket count
                            $column = '';
                            $ticket_type_upper = strtoupper(trim($ticket_type));
                            if ($ticket_type_upper == 'STARTER' || strpos($ticket_type_upper, 'STARTER') !== false) {
                                $column = 'starter_tickets';
                            } elseif ($ticket_type_upper == 'BUSINESS' || strpos($ticket_type_upper, 'BUSINESS') !== false ||
                                      $ticket_type_upper == 'PREMIUM' || strpos($ticket_type_upper, 'PREMIUM') !== false) {
                                $column = 'premium_tickets';
                            } elseif ($ticket_type_upper == 'ULTIMATE' || strpos($ticket_type_upper, 'ULTIMATE') !== false) {
                                $column = 'ultimate_tickets';
                            } else {
                                $column = 'premium_tickets'; // default
                            }

                            $update = $conn->prepare("UPDATE user SET $column = $column + ? WHERE username = ?");
                            $update->bind_param("is", $total_tickets, $username);
                            if (!$update->execute()) {
                                throw new Exception("Failed to update ticket count: " . $update->error);
                            }

                            // Insert into purchasetickets
                            $insert = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)");
                            $insert->bind_param("sssiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $session_id, $total_tickets);
                            if (!$insert->execute()) {
                                throw new Exception("Failed to insert purchase history: " . $insert->error);
                            }
                        }

                        // Commit transaction
                        $conn->commit();
                        $messageSuccess = "Thank you for your purchase! Your tickets have been added to your account.";
                        error_log("Purchase processed successfully for user: $username");

                    } catch (Exception $e) {
                        $conn->rollback();
                        error_log("Error processing purchase: " . $e->getMessage());
                        $messageFail = "There was an error processing your purchase. Please contact support.";
                    }
                } else {
                    $messageFail = "No purchase items found.";
                }
            } else {
                $messageFail = "User information not found in session.";
            }
        }

    } catch (Exception $e) {
        error_log("Error retrieving session: " . $e->getMessage());
        $messageFail = "Error verifying payment: " . $e->getMessage();
    }
}

// Redirect to appropriate page
if ($messageSuccess) {
    header('location: ../front-end/my-ticket.php?success=' . urlencode($messageSuccess));
} else {
    header('location: ../front-end/buy-now.php?error=' . urlencode($messageFail));
}
exit();