<?php
/**
 * Centralized Appika API Synchronization Functions
 * Handles automatic sync of Priority and Status updates from local database to Appika API
 */

require_once __DIR__ . '/graphql_functions.php';
require_once __DIR__ . '/../config/api-config.php';

/**
 * Sync ticket priority and status to Appika API
 * 
 * @param int $ticketId Local ticket ID
 * @param string $newStatus New status value (local format: open, in_progress, resolved, closed)
 * @param string $newPriority New priority value (local format: low, medium, high, critical)
 * @param string $updatedBy Who updated the ticket (admin username or 'system')
 * @return array Result array with success status and message
 */
function syncTicketToAppika($ticketId, $newStatus = null, $newPriority = null, $updatedBy = 'system') {
    global $conn;
    
    $result = [
        'success' => false,
        'message' => '',
        'appika_updated' => false
    ];
    
    try {
        // Get ticket information from database
        $query = "SELECT st.*, u.email 
                  FROM support_tickets st 
                  JOIN user u ON st.user_id = u.id 
                  WHERE st.id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $ticketId);
        mysqli_stmt_execute($stmt);
        $ticketResult = mysqli_stmt_get_result($stmt);
        $ticket = mysqli_fetch_assoc($ticketResult);
        mysqli_stmt_close($stmt);
        
        if (!$ticket) {
            $result['message'] = "Ticket not found with ID: $ticketId";
            return $result;
        }
        
        // Skip sync if no appika_id
        if (empty($ticket['appika_id'])) {
            $result['success'] = true;
            $result['message'] = "Local update successful. No Appika ID found, skipping API sync.";
            return $result;
        }
        
        // Extract numeric ID from appika_id (e.g., HT076 -> 76)
        $numericId = (int)filter_var($ticket['appika_id'], FILTER_SANITIZE_NUMBER_INT);
        if ($numericId <= 0) {
            $result['message'] = "Invalid Appika ID format: " . $ticket['appika_id'];
            return $result;
        }
        
        // Use provided values or current ticket values
        $statusToSync = $newStatus ?? $ticket['status'];
        $priorityToSync = $newPriority ?? $ticket['priority'];
        
        // Map local values to Appika API format
        $apiStatus = mapStatusToAppika($statusToSync);
        $apiPriority = mapPriorityToAppika($priorityToSync);
        $apiType = mapTypeToAppika($ticket['ticket_type']);
        
        // Prepare complete update data for Appika API with all required fields
        $updateData = [
            'contact_id' => null,
            'agent_id' => null,
            'subject' => $ticket['subject'] ?: 'No Subject', // Ensure non-empty string
            'type' => $apiType,
            'type_name' => $ticket['ticket_type'] ?: 'starter',
            'priority' => $apiPriority,
            'status' => $apiStatus,
            'req_email' => $ticket['email'] ?: '<EMAIL>',
            'time_track' => '00:00:00',
            'reply_msg' => "Priority/Status updated to {$priorityToSync}/{$statusToSync} by {$updatedBy}",
            'tags' => ''
        ];

        // Call Appika API update
        $apiResult = updateAppikaTicket($numericId, $updateData);

        if ($apiResult['success']) {
            $result['success'] = true;
            $result['appika_updated'] = true;
            $result['message'] = "Successfully synced to Appika API. Priority: {$priorityToSync} → {$apiPriority}, Status: {$statusToSync} → {$apiStatus}";
        } else {
            $result['success'] = true; // Local update was successful
            $errorDetails = '';

            // Extract detailed error information
            if (isset($apiResult['data']['errors']) && is_array($apiResult['data']['errors'])) {
                $errors = [];
                foreach ($apiResult['data']['errors'] as $error) {
                    $errors[] = $error['message'] ?? 'Unknown GraphQL error';
                }
                $errorDetails = 'GraphQL Errors: ' . implode(', ', $errors);
            } else {
                $errorDetails = $apiResult['error'] ?? 'Unknown error';
            }

            $result['message'] = "Local update successful, but Appika API sync failed: " . $errorDetails;
        }
        
    } catch (Exception $e) {
        $result['success'] = true; // Local update was successful
        $result['message'] = "Local update successful, but Appika API sync failed: " . $e->getMessage();
    }
    
    return $result;
}

/**
 * Map local status to Appika API status format
 */
function mapStatusToAppika($localStatus) {
    $statusMapping = [
        'open' => 'OPEN',
        'in_progress' => 'WIP',
        'resolved' => 'CLOSED',
        'closed' => 'CLOSED'
    ];
    return $statusMapping[strtolower($localStatus)] ?? 'OPEN';
}

/**
 * Map local priority to Appika API priority format
 */
function mapPriorityToAppika($localPriority) {
    $priorityMapping = [
        // Legacy lowercase system
        'low' => 'LOW',
        'medium' => 'MEDIUM',
        'high' => 'HIGH',
        'critical' => 'URGENT',
        'urgent' => 'URGENT',
        // New capitalized system (preferred)
        'Normal' => 'MEDIUM',      // Normal → MEDIUM (what you wanted!)
        'Important' => 'HIGH',     // Important → HIGH
        'Critical' => 'URGENT',    // Critical → URGENT
        // Admin creation system
        'Information' => 'LOW',
        'Minor' => 'LOW'
    ];
    return $priorityMapping[$localPriority] ?? 'MEDIUM';
}

/**
 * Map local ticket type to Appika API type format
 */
function mapTypeToAppika($localType) {
    $typeMapping = [
        'starter' => 1,
        'premium' => 2,
        'ultimate' => 3
    ];
    return $typeMapping[strtolower($localType)] ?? 1;
}

/**
 * Update ticket status in local database and sync to Appika
 * 
 * @param int $ticketId Local ticket ID
 * @param string $newStatus New status value
 * @param string $updatedBy Who updated the ticket
 * @return array Result array with success status and message
 */
function updateTicketStatus($ticketId, $newStatus, $updatedBy = 'system') {
    global $conn;
    
    $result = [
        'success' => false,
        'message' => '',
        'appika_sync' => null
    ];
    
    try {
        // Update local database
        $updateQuery = "UPDATE support_tickets SET status = ?, updated_at = NOW() WHERE id = ?";
        $stmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($stmt, 'si', $newStatus, $ticketId);
        $success = mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        if ($success) {
            $result['success'] = true;
            $result['message'] = "Status updated to: $newStatus";
            
            // Sync to Appika API
            $syncResult = syncTicketToAppika($ticketId, $newStatus, null, $updatedBy);
            $result['appika_sync'] = $syncResult;
            
            if ($syncResult['appika_updated']) {
                $result['message'] .= " (Synced to Appika API)";
            } else {
                $result['message'] .= " (Appika sync: " . $syncResult['message'] . ")";
            }
        } else {
            $result['message'] = "Failed to update local database: " . mysqli_error($conn);
        }
        
    } catch (Exception $e) {
        $result['message'] = "Error updating ticket status: " . $e->getMessage();
    }
    
    return $result;
}

/**
 * Update ticket priority in local database and sync to Appika
 * 
 * @param int $ticketId Local ticket ID
 * @param string $newPriority New priority value
 * @param string $updatedBy Who updated the ticket
 * @return array Result array with success status and message
 */
function updateTicketPriority($ticketId, $newPriority, $updatedBy = 'system') {
    global $conn;
    
    $result = [
        'success' => false,
        'message' => '',
        'appika_sync' => null
    ];
    
    try {
        // Update local database
        $updateQuery = "UPDATE support_tickets SET priority = ?, updated_at = NOW() WHERE id = ?";
        $stmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($stmt, 'si', $newPriority, $ticketId);
        $success = mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        if ($success) {
            $result['success'] = true;
            $result['message'] = "Priority updated to: $newPriority";
            
            // Sync to Appika API
            $syncResult = syncTicketToAppika($ticketId, null, $newPriority, $updatedBy);
            $result['appika_sync'] = $syncResult;
            
            if ($syncResult['appika_updated']) {
                $result['message'] .= " (Synced to Appika API)";
            } else {
                $result['message'] .= " (Appika sync: " . $syncResult['message'] . ")";
            }
        } else {
            $result['message'] = "Failed to update local database: " . mysqli_error($conn);
        }
        
    } catch (Exception $e) {
        $result['message'] = "Error updating ticket priority: " . $e->getMessage();
    }
    
    return $result;
}

/**
 * Update both ticket status and priority in local database and sync to Appika
 * 
 * @param int $ticketId Local ticket ID
 * @param string $newStatus New status value
 * @param string $newPriority New priority value
 * @param string $updatedBy Who updated the ticket
 * @return array Result array with success status and message
 */
function updateTicketStatusAndPriority($ticketId, $newStatus, $newPriority, $updatedBy = 'system') {
    global $conn;
    
    $result = [
        'success' => false,
        'message' => '',
        'appika_sync' => null
    ];
    
    try {
        // Update local database
        $updateQuery = "UPDATE support_tickets SET status = ?, priority = ?, updated_at = NOW() WHERE id = ?";
        $stmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($stmt, 'ssi', $newStatus, $newPriority, $ticketId);
        $success = mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        if ($success) {
            $result['success'] = true;
            $result['message'] = "Status updated to: $newStatus, Priority updated to: $newPriority";
            
            // Sync to Appika API
            $syncResult = syncTicketToAppika($ticketId, $newStatus, $newPriority, $updatedBy);
            $result['appika_sync'] = $syncResult;
            
            if ($syncResult['appika_updated']) {
                $result['message'] .= " (Synced to Appika API)";
            } else {
                $result['message'] .= " (Appika sync: " . $syncResult['message'] . ")";
            }
        } else {
            $result['message'] = "Failed to update local database: " . mysqli_error($conn);
        }
        
    } catch (Exception $e) {
        $result['message'] = "Error updating ticket: " . $e->getMessage();
    }
    
    return $result;
}

/**
 * Log sync operations for debugging
 */
function logAppikaSync($ticketId, $operation, $result, $logFile = null) {
    if (!$logFile) {
        $logFile = __DIR__ . '/../logs/appika_sync.log';
    }
    
    // Create logs directory if it doesn't exist
    $logDir = dirname($logFile);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ticket_id' => $ticketId,
        'operation' => $operation,
        'success' => $result['success'],
        'appika_updated' => $result['appika_updated'] ?? false,
        'message' => $result['message']
    ];
    
    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}
?>
