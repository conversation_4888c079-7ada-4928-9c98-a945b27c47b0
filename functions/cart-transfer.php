<?php
/**
 * Cart Transfer Function
 * Transfers guest cart items to logged-in user's cart
 */

function transferGuestCartToUser($user_id, $conn) {
    // Check if there are guest cart items to transfer
    if (!isset($_SESSION['guest_cart']) || empty($_SESSION['guest_cart'])) {
        return ['success' => false, 'message' => 'No guest cart items to transfer'];
    }
    
    $guest_cart = $_SESSION['guest_cart'];
    $transferred_count = 0;
    
    try {
        // Start transaction
        $conn->begin_transaction();
        
        // Get or create active cart for user
        $cart_query = "SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active' LIMIT 1";
        $cart_stmt = $conn->prepare($cart_query);
        $cart_stmt->bind_param("i", $user_id);
        $cart_stmt->execute();
        $cart_result = $cart_stmt->get_result();
        
        if ($cart_result->num_rows > 0) {
            $cart_row = $cart_result->fetch_assoc();
            $cart_id = $cart_row['cart_id'];
        } else {
            // Create new cart for user
            $create_cart_query = "INSERT INTO cart (user_id, status) VALUES (?, 'active')";
            $create_cart_stmt = $conn->prepare($create_cart_query);
            $create_cart_stmt->bind_param("i", $user_id);
            $create_cart_stmt->execute();
            $cart_id = $conn->insert_id;
        }
        
        // Transfer each guest cart item
        foreach ($guest_cart as $guest_item) {
            $ticket_id = $guest_item['ticket_id'];
            $quantity = $guest_item['quantity'];
            
            // Check if this ticket already exists in user's cart
            $existing_query = "SELECT id, quantity FROM cart_items WHERE cart_id = ? AND ticket_id = ?";
            $existing_stmt = $conn->prepare($existing_query);
            $existing_stmt->bind_param("ii", $cart_id, $ticket_id);
            $existing_stmt->execute();
            $existing_result = $existing_stmt->get_result();
            
            if ($existing_result->num_rows > 0) {
                // Update existing item quantity
                $existing_row = $existing_result->fetch_assoc();
                $new_quantity = $existing_row['quantity'] + $quantity;
                
                // Ensure quantity doesn't exceed 10
                if ($new_quantity > 10) {
                    $new_quantity = 10;
                }
                
                $update_query = "UPDATE cart_items SET quantity = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("ii", $new_quantity, $existing_row['id']);
                $update_stmt->execute();
                
                if ($update_stmt->affected_rows > 0) {
                    $transferred_count++;
                }
            } else {
                // Add new item to cart
                $insert_query = "INSERT INTO cart_items (cart_id, ticket_id, quantity) VALUES (?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bind_param("iii", $cart_id, $ticket_id, $quantity);
                $insert_stmt->execute();

                if ($insert_stmt->affected_rows > 0) {
                    $transferred_count++;
                }
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        // Clear guest cart after successful transfer
        unset($_SESSION['guest_cart']);
        
        return [
            'success' => true, 
            'message' => "Successfully transferred $transferred_count item(s) to your cart",
            'transferred_count' => $transferred_count
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        
        error_log("Cart transfer error: " . $e->getMessage());
        return [
            'success' => false, 
            'message' => 'Failed to transfer cart items: ' . $e->getMessage()
        ];
    }
}

/**
 * Check if user has guest cart items that can be transferred
 */
function hasGuestCartItems() {
    return isset($_SESSION['guest_cart']) && !empty($_SESSION['guest_cart']);
}

/**
 * Get guest cart items count
 */
function getGuestCartItemsCount() {
    if (!hasGuestCartItems()) {
        return 0;
    }
    
    return count($_SESSION['guest_cart']);
}

/**
 * Get guest cart total value
 */
function getGuestCartTotal() {
    if (!hasGuestCartItems()) {
        return 0;
    }
    
    $total = 0;
    foreach ($_SESSION['guest_cart'] as $item) {
        $quantity = isset($item['quantity']) ? (int)$item['quantity'] : 0;
        $price = isset($item['dollar_price_per_package']) ? (float)$item['dollar_price_per_package'] : 0.00;
        $total += $quantity * $price;
    }
    
    return $total;
}
?>
