<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle

$errors = array();

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to add location to a customer in Appika
function addLocationToAppika($customerDbId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for adding a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

if (isset($_POST['reg_user'])) {
    $first_name = mysqli_real_escape_string($conn, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($conn, $_POST['last_name']);
    $username = mysqli_real_escape_string($conn, $_POST['username']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password_1 = mysqli_real_escape_string($conn, $_POST['password_1']);
    $password_2 = mysqli_real_escape_string($conn, $_POST['password_2']);

    $tell = mysqli_real_escape_string($conn, $_POST['tell']);
    $company_name = mysqli_real_escape_string($conn, $_POST['company_name']);
    $tax_id = mysqli_real_escape_string($conn, $_POST['tax_id']);
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $address2 = mysqli_real_escape_string($conn, $_POST['address2']);
    $district = mysqli_real_escape_string($conn, $_POST['district']);
    $city = mysqli_real_escape_string($conn, $_POST['city']);
    $state = mysqli_real_escape_string($conn, $_POST['state']);
    $country = mysqli_real_escape_string($conn, $_POST['country']);
    $postal_code = mysqli_real_escape_string($conn, $_POST['postal_code']);

    if (empty($first_name)) {
        array_push($errors, "First name is required");
    }
    if (empty($last_name)) {
        array_push($errors, "Last name is required");
    }
    if (empty($username)) {
        array_push($errors, "Username is required");
    }
    if (empty($email)) {
        array_push($errors, "Email is required");
    }
    if (empty($password_1)) {
        array_push($errors, "Password is required");
    }
    if ($password_1 != $password_2) {
        array_push($errors, "The two password do not match");
    }
    if (empty($tell)) {
        array_push($errors, "Tell number is required");
    }
    if (empty($district)) {
        array_push($errors, "District is required");
    }
    if (empty($city)) {
        array_push($errors, "City is required");
    }
    if (empty($country)) {
        array_push($errors, "Country is required");
    }
    if (empty($postal_code)) {
        array_push($errors, "Postal Code is required");
    }
    if (empty($address)) {
        array_push($errors, "Address is required");
    }

    $user_check_query = "SELECT * FROM user WHERE username = '$username' or email = '$email' LIMIT 1";
    $query = mysqli_query($conn, $user_check_query);
    $result = mysqli_fetch_assoc($query);

    if ($result) {
        //if user exists
        if ($result['username'] === $username) {
            array_push($errors, "Username already exists");
        }
        if ($result['email'] === $email) {
            array_push($errors, "Email already exists");
        }
    }

    if (count($errors) == 0) {
        $password = md5($password_1);
        $registration_time = getCurrentUTC(); // Get UTC time for consistent storage

        // Insert user without appika_id first
        $sql = "INSERT INTO user (username, email, password, tell, company_name, tax_id, address, address2, district, city, state, country, postal_code, registration_time, first_name, last_name)
                VALUES ('$username', '$email', '$password', '$tell', '$company_name', '$tax_id', '$address', '$address2', '$district', '$city', '$state', '$country', '$postal_code', '$registration_time', '$first_name', '$last_name')";
        mysqli_query($conn, $sql);

        // Get the last inserted user id
        $user_id = mysqli_insert_id($conn);
        if ($user_id) {
            // Prepare data for Appika API
            $customerName = $first_name . ' ' . $last_name;
            $customerNo = 'HI' . $user_id;
            $customerStartDate = date('Y-m-d', strtotime($registration_time));

            // Set fixed values as per requirements
            $customerGroup = '10';
            $ofcId = '511';
            $assignTo = '1';
            $creator = '1';
            $status = 'a';

            // Create customer data structure for Appika API (without location data)
            $customerData = [
                'no' => $customerNo,
                'name' => $customerName,
                'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
                'grp_id' => $customerGroup,
                'ofc_id' => $ofcId,
                'assign2' => $assignTo,
                'creator' => $creator,
                'start_date' => $customerStartDate,
                'status' => $status
            ];

            // Create logs directory if it doesn't exist
            if (!file_exists("../logs")) {
                mkdir("../logs", 0755, true);
            }

            // Step 1: Send customer data to Appika API
            $apiResult = sendToAppikaAPI($customerData);

            // Log API result for debugging
            $log_file = fopen("../logs/appika_api.log", "a");
            fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Create Customer API Result: " . json_encode($apiResult) . "\n");

            // Step 2: If customer creation was successful, update appika_id and add location data
            if ($apiResult['success'] && isset($apiResult['data'][0]['id'])) {
                // Get the Appika customer ID
                $appikaCustomerId = $apiResult['data'][0]['id'];
                $appika_id = $customerNo;
                // Update the user with the generated appika_id
                $update_sql = "UPDATE user SET appika_id = '$appika_id' WHERE id = $user_id";
                mysqli_query($conn, $update_sql);

                // Prepare location data
                $locationData = [
                    'loc_code' => 'LOC-' . $customerNo,
                    'loc_name' => $customerName . ' Location',
                    'add1' => $address,
                    'add2' => $address2,
                    'ccode' => $country,
                    'state_code' => $state,
                    'city' => $city,
                    'status' => $status,
                    'is_primary_loc' => 'y',
                    'zip' => $postal_code,
                    'parent_id' => 0
                ];

                // Add location to Appika
                $locationResult = addLocationToAppika($appikaCustomerId, $locationData);

                // Log location API result
                fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Add Location API Result: " . json_encode($locationResult) . "\n");
            } else {
                // If API failed, leave appika_id empty
                $update_sql = "UPDATE user SET appika_id = NULL WHERE id = $user_id";
                mysqli_query($conn, $update_sql);
            }

            fclose($log_file);
        }

        // Set registration success flag
        $_SESSION['registration_success'] = true;
        header('location: ../front-end/sign-in.php');
        exit();
    } else {
        // Store form data in session to repopulate the form
        $_SESSION['form_data'] = array(
            'first_name' => $first_name,
            'last_name' => $last_name,
            'username' => $username,
            'email' => $email,
            'tell' => $tell,
            'company_name' => $company_name,
            'tax_id' => $tax_id,
            'address' => $address,
            'address2' => $address2,
            'district' => $district,
            'city' => $city,
            'state' => $state,
            'country' => $country,
            'postal_code' => $postal_code
        );

        $_SESSION['error'] = $errors;
        header("location: ../front-end/sign-up.php");
    }
}