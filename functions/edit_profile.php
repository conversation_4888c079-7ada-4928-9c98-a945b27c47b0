<?php
session_start();
include('../functions/server.php');
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to update location in Appika
function updateLocationInAppika($customerDbId, $locationId, $locationData) {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create the path for updating a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations/' . $locationId;

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'PUT', $locationPath);
}

if (isset($_SESSION['username'])) {
    $current_username = $_SESSION['username'];
    $new_username = mysqli_real_escape_string($conn, $_POST['username']);
    $first_name = mysqli_real_escape_string($conn, $_POST['first_name']);
    $last_name = mysqli_real_escape_string($conn, $_POST['last_name']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $tell = mysqli_real_escape_string($conn, $_POST['tell']);
    $company_name = mysqli_real_escape_string($conn, $_POST['company_name']);
    $tax_id = mysqli_real_escape_string($conn, $_POST['tax_id']);
    $address = mysqli_real_escape_string($conn, $_POST['address']);
    $address2 = mysqli_real_escape_string($conn, $_POST['address2']);
    $district = mysqli_real_escape_string($conn, $_POST['district']);
    $city = mysqli_real_escape_string($conn, $_POST['city']);
    $state = mysqli_real_escape_string($conn, $_POST['state']);
    $country = mysqli_real_escape_string($conn, $_POST['country']);
    $postal_code = mysqli_real_escape_string($conn, $_POST['postal_code']);
    // Sanitize and validate other input fields as needed

    // Validate username format
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $new_username)) {
        $_SESSION['profile_update_error'] = "Username must contain only letters, numbers, and underscores.";
        header('Location: ../front-end/profile.php?error=1');
        exit();
    }

    if (strlen($new_username) < 3 || strlen($new_username) > 50) {
        $_SESSION['profile_update_error'] = "Username must be between 3 and 50 characters long.";
        header('Location: ../front-end/profile.php?error=1');
        exit();
    }

    // First, get the user's current email and appika_id
    $current_user_query = "SELECT email, appika_id FROM user WHERE username = '$current_username'";
    $current_user_result = mysqli_query($conn, $current_user_query);
    $current_user_row = mysqli_fetch_assoc($current_user_result);
    $current_email = $current_user_row['email'];
    $appika_id = $current_user_row['appika_id'];

    // Check if the username has changed
    if ($new_username != $current_username) {
        // Check if the new username already exists for another user
        $username_check_query = "SELECT * FROM user WHERE username = '$new_username'";
        $username_check_result = mysqli_query($conn, $username_check_query);

        if (mysqli_num_rows($username_check_result) > 0) {
            // Username already exists for another user
            $_SESSION['profile_update_error'] = "This username is already taken by another account.";
            header('Location: ../front-end/profile.php?error=3'); // Using error code 3 for username exists error
            exit();
        }
    }

    // Email cannot be changed for security reasons - skip email validation
    // Use the current email from database instead of form input
    $email = $current_email;

    // Update user in local database (including username)
    $sql = "UPDATE user SET username='$new_username', first_name='$first_name', last_name='$last_name', email='$email', tell='$tell', company_name='$company_name', tax_id='$tax_id', address='$address', address2='$address2', district='$district', city='$city', state='$state', country='$country', postal_code='$postal_code' WHERE username='$current_username'";

    if (mysqli_query($conn, $sql)) {
        // Create logs directory if it doesn't exist
        if (!file_exists("../logs")) {
            mkdir("../logs", 0755, true);
        }

        // Log file for debugging
        $log_file = fopen("../logs/appika_api_update.log", "a");

        // If user has an Appika ID, update their data in Appika
        if (!empty($appika_id)) {
            // First, search for the customer in Appika to get their ID
            $client = new \GuzzleHttp\Client([
                'base_uri' => $apiEndpoint,
                'timeout' => 30,
                'http_errors' => false,
            ]);

            // Search for customer by appika_id
            $searchResult = $client->request('GET', $apiPath, [
                'headers' => [
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json',
                ],
                'query' => [
                    'no' => $appika_id
                ]
            ]);

            $searchData = json_decode($searchResult->getBody()->getContents(), true);

            // Log search result
            fwrite($log_file, date('Y-m-d H:i:s') . " - Search Result: " . json_encode($searchData) . "\n");

            // If customer found in Appika
            if (isset($searchData['items']) && !empty($searchData['items'])) {
                $customerData = $searchData['items'][0];
                $customerDbId = $customerData['id'];

                // Prepare customer data for update
                $customerName = $first_name . ' ' . $last_name;
                $customerStartDate = date('Y-m-d', strtotime($customerData['start_date']));

                // Set fixed values as per requirements
                $customerGroup = '10';
                $ofcId = '511';
                $assignTo = '1';
                $creator = '1';
                $status = 'a';

                // Create customer data structure for Appika API update
                $updateData = [
                    'no' => $appika_id,
                    'name' => $customerName,
                    'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
                    'grp_id' => $customerGroup,
                    'ofc_id' => $ofcId,
                    'assign2' => $assignTo,
                    'creator' => $creator,
                    'start_date' => $customerStartDate,
                    'status' => $status
                ];

                // Update customer in Appika
                $updatePath = $apiPath . '/' . $customerDbId;
                $updateResult = sendToAppikaAPI($updateData, 'PUT', $updatePath);

                // Log update result
                fwrite($log_file, date('Y-m-d H:i:s') . " - Customer Update Result: " . json_encode($updateResult) . "\n");

                // Now check if there's a primary location to update
                $locationsResult = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                    'headers' => [
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                    ]
                ]);

                $locationsData = json_decode($locationsResult->getBody()->getContents(), true);

                // Log locations result
                fwrite($log_file, date('Y-m-d H:i:s') . " - Locations Result: " . json_encode($locationsData) . "\n");

                // Find primary location
                $primaryLocationId = null;
                if (isset($locationsData['items']) && !empty($locationsData['items'])) {
                    foreach ($locationsData['items'] as $location) {
                        if (isset($location['is_primary_loc']) && $location['is_primary_loc'] === 'y') {
                            $primaryLocationId = $location['id'];
                            break;
                        }
                    }
                }

                // If primary location found, update it
                if ($primaryLocationId) {
                    // Prepare location data
                    $locationData = [
                        'loc_code' => 'LOC-' . $appika_id,
                        'loc_name' => $customerName . ' Location',
                        'add1' => $address,
                        'add2' => $address2,
                        'ccode' => $country,
                        'state_code' => $state,
                        'city' => $city,
                        'status' => $status,
                        'is_primary_loc' => 'y',
                        'zip' => $postal_code,
                        'parent_id' => 0
                    ];

                    // Update location in Appika
                    $locationResult = updateLocationInAppika($customerDbId, $primaryLocationId, $locationData);

                    // Log location update result
                    fwrite($log_file, date('Y-m-d H:i:s') . " - Location Update Result: " . json_encode($locationResult) . "\n");
                }
            }
        }

        // Close log file
        fclose($log_file);

        // Update session username if it changed
        if ($new_username != $current_username) {
            $_SESSION['username'] = $new_username;
        }

        // Use a specific session variable for profile updates instead of the general 'success' variable
        $_SESSION['profile_update_success'] = "Profile updated successfully.";
        header('Location: ../front-end/profile.php?success=1');
    } else {
        $_SESSION['profile_update_error'] = "Error updating profile: " . mysqli_error($conn);
        header('Location: ../front-end/profile.php?error=1');
    }
    exit();
} else {
    header('Location: ../front-end/login.php');
    exit();
}