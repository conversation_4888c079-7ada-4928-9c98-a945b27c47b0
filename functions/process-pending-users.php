<?php
session_start();
include('server.php');

echo "<h2>Process Pending Users</h2>";

// Find payment_temp entries that don't have corresponding users
$pending_query = "
    SELECT pt.* 
    FROM payment_temp pt 
    LEFT JOIN user u ON pt.username = u.username 
    WHERE u.id IS NULL 
    ORDER BY pt.id DESC
";

$pending_result = mysqli_query($conn, $pending_query);

if ($pending_result && mysqli_num_rows($pending_result) > 0) {
    echo "<h3>Found " . mysqli_num_rows($pending_result) . " pending users:</h3>";
    
    while ($pending = mysqli_fetch_assoc($pending_result)) {
        echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>Processing: " . $pending['username'] . "</h4>";
        echo "<p><strong>Email:</strong> " . $pending['email'] . "</p>";
        echo "<p><strong>Password:</strong> " . $pending['password'] . "</p>";
        echo "<p><strong>Session ID:</strong> " . $pending['session_id'] . "</p>";
        
        // Create the user manually
        try {
            // Generate user data
            $username = $pending['username'];
            $email = $pending['email'];
            $password = $pending['password'];
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $registration_time = date('Y-m-d H:i:s');
            
            // Extract first name from username (remove 'user' prefix and numbers)
            $first_name = $username;
            $last_name = '';
            
            // Insert user
            $insert_user_sql = "INSERT INTO user (username, email, password, first_name, last_name, registration_time, starter_tickets, premium_tickets, ultimate_tickets) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)";
            $insert_stmt = $conn->prepare($insert_user_sql);
            $insert_stmt->bind_param("ssssss", $username, $email, $hashed_password, $first_name, $last_name, $registration_time);
            
            if ($insert_stmt->execute()) {
                $user_id = $conn->insert_id;
                echo "<p>✅ <strong>User created successfully!</strong> ID: $user_id</p>";
                
                // Now try to process any Stripe session data if available
                $session_id = $pending['session_id'];
                if (!empty($session_id) && strpos($session_id, 'cs_test_') === 0) {
                    echo "<p>🔄 <strong>Processing Stripe session...</strong></p>";
                    
                    // You could add Stripe session processing here if needed
                    // For now, just mark as processed
                    echo "<p>ℹ️ Stripe session processing would happen here</p>";
                }
                
                echo "<p>✅ <strong>User is now ready for sign-in!</strong></p>";
                
            } else {
                echo "<p>❌ <strong>Error creating user:</strong> " . $conn->error . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ <strong>Exception:</strong> " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>All pending users have been processed. You can now try signing in with their credentials.</p>";
    
} else {
    echo "<p>✅ No pending users found. All payment_temp entries have corresponding users in the main table.</p>";
}

// Show current status
echo "<h3>Current Status Check</h3>";
echo "<p><a href='quick-debug.php'>Check Database Status</a></p>";
echo "<p><a href='../front-end/sign-in.php'>Try Sign-In</a></p>";

// Test specific user
if (isset($_GET['test_user'])) {
    $test_user = $_GET['test_user'];
    echo "<h3>Testing User: $test_user</h3>";
    
    $test_query = "SELECT * FROM user WHERE username = ?";
    $test_stmt = $conn->prepare($test_query);
    $test_stmt->bind_param("s", $test_user);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    
    if ($test_result->num_rows > 0) {
        $test_user_data = $test_result->fetch_assoc();
        echo "<p>✅ User exists in main table</p>";
        echo "<p><strong>ID:</strong> " . $test_user_data['id'] . "</p>";
        echo "<p><strong>Username:</strong> " . $test_user_data['username'] . "</p>";
        echo "<p><strong>Email:</strong> " . $test_user_data['email'] . "</p>";
        
        // Get password from payment_temp
        $temp_query = "SELECT password FROM payment_temp WHERE username = ?";
        $temp_stmt = $conn->prepare($temp_query);
        $temp_stmt->bind_param("s", $test_user);
        $temp_stmt->execute();
        $temp_result = $temp_stmt->get_result();
        
        if ($temp_result->num_rows > 0) {
            $temp_data = $temp_result->fetch_assoc();
            echo "<p><strong>Password:</strong> " . $temp_data['password'] . "</p>";
            echo "<p>✅ Ready for sign-in!</p>";
        }
    } else {
        echo "<p>❌ User not found in main table</p>";
    }
}
?>

<h3>Manual Test</h3>
<form method="GET">
    <label>Test specific user:</label>
    <input type="text" name="test_user" placeholder="username" value="<?php echo $_GET['test_user'] ?? 'user46281'; ?>">
    <input type="submit" value="Test User">
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
form { margin: 20px 0; }
input[type="text"] { padding: 5px; margin: 5px; }
input[type="submit"] { padding: 8px 15px; background: #007cba; color: white; border: none; cursor: pointer; }
</style>
