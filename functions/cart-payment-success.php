<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

/**
 * Enforce 2-card limit: remove oldest payment method if user has more than 2
 */
function enforcePaymentMethodLimit($stripe_customer_id) {
    try {
        // Get all payment methods for this customer
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        // If user has more than 2 payment methods, remove the oldest ones
        if (count($payment_methods->data) > 2) {
            // Sort by created timestamp (oldest first)
            $methods_array = $payment_methods->data;
            usort($methods_array, function($a, $b) {
                return $a->created - $b->created;
            });

            // Remove oldest methods until we have exactly 2
            $methods_to_remove = count($methods_array) - 2;
            for ($i = 0; $i < $methods_to_remove; $i++) {
                $oldest_method = $methods_array[$i];
                $oldest_method->detach();
                error_log("Enforced 2-card limit: Removed oldest payment method {$oldest_method->id} for customer {$stripe_customer_id}");
            }
        }
    } catch (\Exception $e) {
        error_log("Error enforcing payment method limit: " . $e->getMessage());
    }
}

/**
 * Enforce 2-card limit in database: remove oldest payment method if user has more than 2
 */
function enforceDatabasePaymentMethodLimit($user_id) {
    global $conn;

    try {
        // Get count of payment methods for this user
        $count_query = "SELECT COUNT(*) as count FROM payment_methods WHERE user_id = ?";
        $stmt = $conn->prepare($count_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $count_result = $stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $payment_method_count = $count_row['count'];

        // If user has more than 2 payment methods, remove the oldest ones
        if ($payment_method_count > 2) {
            // Get oldest payment methods (excluding default)
            $oldest_query = "SELECT id, payment_method_id FROM payment_methods
                           WHERE user_id = ? AND is_default = 0
                           ORDER BY created_at ASC
                           LIMIT ?";
            $methods_to_remove = $payment_method_count - 2;
            $stmt = $conn->prepare($oldest_query);
            $stmt->bind_param("ii", $user_id, $methods_to_remove);
            $stmt->execute();
            $oldest_result = $stmt->get_result();

            // Remove oldest payment methods
            while ($method = $oldest_result->fetch_assoc()) {
                $method_id = $method['id'];
                $payment_method_id = $method['payment_method_id'];

                // Delete from database
                $delete_query = "DELETE FROM payment_methods WHERE id = ? AND user_id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->bind_param("ii", $method_id, $user_id);
                $delete_stmt->execute();

                error_log("Enforced 2-card database limit: Removed oldest payment method {$payment_method_id} (DB ID: {$method_id}) for user {$user_id}");
            }
        }
    } catch (\Exception $e) {
        error_log("Error enforcing database payment method limit: " . $e->getMessage());
    }
}

// Verify the session ID or payment intent ID from the URL
$session_id = isset($_GET['session_id']) ? $_GET['session_id'] : '';
$payment_intent_id = isset($_GET['payment_intent_id']) ? $_GET['payment_intent_id'] : '';
$messageSuccess = '';
$messageFail = '';

if (empty($session_id) && empty($payment_intent_id)) {
    header('location: ../front-end/cart.php');
    exit();
}

try {
    $metadata = [];
    $cart_id = '';
    $user_id = '';
    $transaction_id = '';

    if (!empty($session_id)) {
        // Retrieve the checkout session from Stripe
        $session = \Stripe\Checkout\Session::retrieve($session_id);
        $metadata = $session->metadata->toArray();

        // Get cart ID from metadata
        $cart_id = $metadata['cart_id'] ?? '';
        $user_id = $metadata['user_id'] ?? '';
        $transaction_id = $session->id ?? '';

        // Get payment intent to retrieve payment method and billing details
        $payment_intent_id = $session->payment_intent;
    } else if (!empty($payment_intent_id)) {
        // Retrieve the payment intent directly
        $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
        $metadata = $payment_intent->metadata->toArray();

        // Get cart ID from metadata
        $cart_id = $metadata['cart_id'] ?? '';
        $user_id = $metadata['user_id'] ?? '';
        $transaction_id = $payment_intent->id ?? '';
    }

    // Get payment method details if we have a payment intent ID
    if ($payment_intent_id) {
        if (!isset($payment_intent)) {
            $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
        }
        $payment_method_id = $payment_intent->payment_method;

        // Get the payment method details
        $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

        // Get customer ID from database
        $stripe_customer_id = '';
        $customer_query = $conn->prepare("SELECT stripe_customer_id FROM user WHERE id = ?");
        $customer_query->bind_param("i", $user_id);
        $customer_query->execute();
        $customer_query->bind_result($stripe_customer_id);
        $customer_query->fetch();
        $customer_query->close();

        if ($stripe_customer_id && $payment_method && isset($payment_method->billing_details)) {
            // Update customer with billing details
            $billing_details = $payment_method->billing_details;
            $billing_address = $billing_details->address;

            // Create a formatted address string
            $address_parts = [];
            if (!empty($billing_address->line1)) $address_parts[] = $billing_address->line1;
            if (!empty($billing_address->line2)) $address_parts[] = $billing_address->line2;
            if (!empty($billing_address->city)) $address_parts[] = $billing_address->city;
            if (!empty($billing_address->state)) $address_parts[] = $billing_address->state;
            if (!empty($billing_address->postal_code)) $address_parts[] = $billing_address->postal_code;
            if (!empty($billing_address->country)) $address_parts[] = $billing_address->country;

            $formatted_address = implode(', ', $address_parts);

            // Update customer metadata with billing details
            \Stripe\Customer::update($stripe_customer_id, [
                'metadata' => [
                    'billing_name' => $billing_details->name ?? '',
                    'billing_email' => $billing_details->email ?? '',
                    'billing_phone' => $billing_details->phone ?? '',
                    'billing_address' => $formatted_address,
                ],
            ]);

            // Attach the payment method to the customer if not already attached
            try {
                $payment_method->attach([
                    'customer' => $stripe_customer_id,
                ]);

                // Enforce 2-card limit: remove oldest payment method if user already has 2
                enforcePaymentMethodLimit($stripe_customer_id);

                // For logged-in users, payment methods are saved to Stripe only (not database)
                // Database saving is only for guest users handled by webhook
                error_log("Payment method saved to Stripe for logged-in user ID: $user_id");

            } catch (\Exception $e) {
                // Payment method might already be attached, log the error but continue
                error_log("Error attaching payment method: " . $e->getMessage());
            }
        }
    }

    // Verify user is logged in and matches the user who made the purchase
    if (!isset($_SESSION['user_id']) || $_SESSION['user_id'] != $user_id) {
        header('location: ../front-end/sign-in.php');
        exit();
    }

    // First, check if this transaction has already been processed
    $check_query = "SELECT purchaseid FROM purchasetickets WHERE transactionid = ? LIMIT 1";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("s", $transaction_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Transaction already processed, just show success message
        $messageSuccess = "Thank you for your purchase! Your tickets have been added to your account and your payment method has been saved.";
    } else {
        try {
            // Process the purchase
            processCartPurchase($cart_id, $transaction_id);
            $messageSuccess = "Thank you for your purchase! Your payment method has been saved for future transactions.";

            // Clear cart transfer messages after successful purchase
            if (isset($_SESSION['cart_transfer_success'])) {
                unset($_SESSION['cart_transfer_success']);
            }
            if (isset($_SESSION['cart_transfer_error'])) {
                unset($_SESSION['cart_transfer_error']);
            }
        } catch (Exception $inner_e) {
            // Log the error but still show success to the user
            error_log("Error processing purchase: " . $inner_e->getMessage());
            $messageSuccess = "Thank you for your purchase! Your tickets have been added to your account and your payment method has been saved.";

            // Clear cart transfer messages even if there was an error
            if (isset($_SESSION['cart_transfer_success'])) {
                unset($_SESSION['cart_transfer_success']);
            }
            if (isset($_SESSION['cart_transfer_error'])) {
                unset($_SESSION['cart_transfer_error']);
            }
        }
    }
} catch (Exception $e) {
    error_log("Error verifying payment: " . $e->getMessage());
    $messageFail = "Error verifying payment: " . $e->getMessage();
}

// Function to save payment method to database and set as default if it's the first one
function savePaymentMethodToDatabase($user_id, $payment_method) {
    global $conn;

    // Extract card details
    $card = $payment_method->card;
    $payment_method_id = $payment_method->id;
    $card_brand = $card->brand;
    $card_last4 = $card->last4;
    $card_exp_month = $card->exp_month;
    $card_exp_year = $card->exp_year;

    // Check if this payment method is already in the database
    $check_query = "SELECT id FROM payment_methods WHERE payment_method_id = ? AND user_id = ?";
    $stmt = $conn->prepare($check_query);
    $stmt->bind_param("si", $payment_method_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        // This is a new payment method

        // Check if user has any payment methods already
        $count_query = "SELECT COUNT(*) as count FROM payment_methods WHERE user_id = ?";
        $stmt = $conn->prepare($count_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $count_result = $stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $payment_method_count = $count_row['count'];

        // Get make_default preference from metadata
        global $metadata;
        $make_default_pref = isset($metadata['make_default']) ? ($metadata['make_default'] === 'true') : true;

        // Set is_default to 1 if:
        // 1. This is the first payment method, OR
        // 2. User explicitly chose to make this the default
        $is_default = ($payment_method_count == 0 || $make_default_pref) ? 1 : 0;

        // If making this the default, reset all other payment methods
        if ($is_default && $payment_method_count > 0) {
            $reset_query = "UPDATE payment_methods SET is_default = 0 WHERE user_id = ?";
            $reset_stmt = $conn->prepare($reset_query);
            $reset_stmt->bind_param("i", $user_id);
            $reset_stmt->execute();
        }

        // Insert the payment method into the database
        $insert_query = "INSERT INTO payment_methods (user_id, payment_method_id, card_brand, card_last4, card_exp_month, card_exp_year, is_default, created_at)
                         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("isssiis", $user_id, $payment_method_id, $card_brand, $card_last4, $card_exp_month, $card_exp_year, $is_default);

        if (!$stmt->execute()) {
            error_log("Error saving payment method to database: " . $stmt->error);
        } else {
            if ($is_default) {
                error_log("First payment method saved and set as default for user ID: $user_id");
            } else {
                error_log("Additional payment method saved for user ID: $user_id");
            }

            // Enforce 2-card limit in database after adding new payment method
            enforceDatabasePaymentMethodLimit($user_id);
        }
    }
}

// Function to process cart purchase
function processCartPurchase($cart_id, $transaction_id) {
    global $conn;

    // Sanitize inputs
    $cart_id = mysqli_real_escape_string($conn, $cart_id);
    $transaction_id = mysqli_real_escape_string($conn, $transaction_id);
    $user_id = $_SESSION['user_id'];
    $current_time = date('Y-m-d H:i:s');

    // Get cart items
    $query = "SELECT ci.ticket_id, ci.quantity, t.ticket_type, t.package_size, t.numbers_per_package, t.dollar_price_per_package
              FROM cart_items ci
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE ci.cart_id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $cart_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);

    // Begin transaction
    $conn->begin_transaction();

    try {
        // Get username
        $username_query = "SELECT username FROM user WHERE id = ?";
        $stmt = $conn->prepare($username_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $username_result = $stmt->get_result();
        $username_row = $username_result->fetch_assoc();
        $username = $username_row['username'] ?? '';

        // Process each item in the cart
        foreach ($cart_items as $item) {
            // Get item details
            $ticket_type = $item['ticket_type'];
            $package_size = $item['package_size'];
            $numbers_per_package = $item['numbers_per_package'];
            $dollar_price_per_package = $item['dollar_price_per_package'];
            $quantity = $item['quantity'];

            // Calculate total tickets
            $total_tickets = $numbers_per_package * $quantity;

            // Insert into purchasetickets table
            $insert_query = "INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("sssiissi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $current_time, $transaction_id, $total_tickets);
            $stmt->execute();

            // Debug ticket type
            error_log("Original ticket_type: '$ticket_type'");
            error_log("Uppercase ticket_type: '" . strtoupper($ticket_type) . "'");
            error_log("Lowercase ticket_type: '" . strtolower($ticket_type) . "'");

            // Update user's ticket count - using case-insensitive comparison with trim
            $ticket_type_upper = trim(strtoupper($ticket_type));
            error_log("Trimmed uppercase ticket_type: '$ticket_type_upper'");

            // Check for 'STARTER' (maps to starter_tickets)
            if ($ticket_type_upper == 'STARTER' ||
                strpos($ticket_type_upper, 'STARTER') !== false) {
                $update_query = "UPDATE user SET starter_tickets = starter_tickets + ? WHERE id = ?";
                error_log("Updating STARTER tickets for user $user_id, adding $total_tickets tickets");
            }
            // Check for 'BUSINESS' (maps to premium_tickets)
            else if ($ticket_type_upper == 'BUSINESS' ||
                     strpos($ticket_type_upper, 'BUSINESS') !== false ||
                     $ticket_type_upper == 'PREMIUM' ||
                     strpos($ticket_type_upper, 'PREMIUM') !== false) {
                $update_query = "UPDATE user SET premium_tickets = premium_tickets + ? WHERE id = ?";
                error_log("Updating PREMIUM/BUSINESS tickets for user $user_id, adding $total_tickets tickets");
            }
            // Check for 'ULTIMATE'
            else if ($ticket_type_upper == 'ULTIMATE' ||
                     strpos($ticket_type_upper, 'ULTIMATE') !== false) {
                $update_query = "UPDATE user SET ultimate_tickets = ultimate_tickets + ? WHERE id = ?";
                error_log("Updating ULTIMATE tickets for user $user_id, adding $total_tickets tickets");
            }
            // Default case
            else {
                error_log("Unknown ticket type: '$ticket_type' - defaulting to premium tickets");
                $update_query = "UPDATE user SET premium_tickets = premium_tickets + ? WHERE id = ?";
            }

            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("ii", $total_tickets, $user_id);
            $result = $stmt->execute();

            if (!$result) {
                error_log("Update failed: " . $stmt->error);
                throw new Exception("Database update error: " . $stmt->error);
            }

            // Check if any rows were affected
            if ($stmt->affected_rows == 0) {
                error_log("Warning: No rows affected when updating user tickets. User ID: $user_id");
            } else {
                error_log("User ticket count updated successfully. Affected rows: " . $stmt->affected_rows);
            }
        }

        // Update cart status to 'completed'
        $update_cart_query = "UPDATE cart SET status = 'completed' WHERE cart_id = ?";
        $stmt = $conn->prepare($update_cart_query);
        $stmt->bind_param("i", $cart_id);
        $stmt->execute();

        // Clean up cart_sessions data to save storage space
        try {
            // Delete cart session data for this transaction
            $cleanup_cart_sessions_query = "DELETE FROM cart_sessions WHERE user_id = ? AND processed_at IS NOT NULL";
            $cleanup_stmt = $conn->prepare($cleanup_cart_sessions_query);
            $cleanup_stmt->bind_param("i", $user_id);
            $cleanup_stmt->execute();
            $deleted_sessions = $cleanup_stmt->affected_rows;

            if ($deleted_sessions > 0) {
                error_log("Cart sessions cleanup: Deleted $deleted_sessions processed cart sessions for user $user_id");
            }

            // Also clean up old unprocessed cart sessions (older than 24 hours) to prevent storage waste
            $cleanup_old_sessions_query = "DELETE FROM cart_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND processed_at IS NULL";
            $cleanup_old_stmt = $conn->prepare($cleanup_old_sessions_query);
            $cleanup_old_stmt->execute();
            $deleted_old_sessions = $cleanup_old_stmt->affected_rows;

            if ($deleted_old_sessions > 0) {
                error_log("Cart sessions cleanup: Deleted $deleted_old_sessions old unprocessed cart sessions");
            }
        } catch (Exception $cleanup_e) {
            error_log("Error cleaning up cart sessions: " . $cleanup_e->getMessage());
            // Don't throw exception as this is cleanup, not critical for purchase completion
        }

        // Commit transaction
        $conn->commit();

        // Send receipt email after successful purchase
        try {
            include_once(__DIR__ . '/send-purchase-receipt.php');

            // Get user email
            $email_stmt = $conn->prepare("SELECT email FROM user WHERE username = ?");
            $email_stmt->bind_param("s", $username);
            $email_stmt->execute();
            $email_result = $email_stmt->get_result();

            if ($email_row = $email_result->fetch_assoc()) {
                $user_email = $email_row['email'];

                if (empty($user_email)) {
                    error_log("Cart purchase: No email address found for user $username");
                } else {
                    error_log("Cart purchase: Attempting to send receipt to $user_email for transaction $transaction_id");
                    $receiptSent = sendPurchaseReceiptEmail($transaction_id, $user_email);

                    if ($receiptSent) {
                        error_log("Cart purchase: Receipt email sent successfully to $user_email for transaction $transaction_id");
                    } else {
                        error_log("Cart purchase: Failed to send receipt email to $user_email for transaction $transaction_id");
                    }
                }
            } else {
                error_log("Cart purchase: No user record found for username $username");
            }
            $email_stmt->close();

        } catch (Exception $receipt_e) {
            error_log("Cart purchase: Error sending receipt email: " . $receipt_e->getMessage());
            error_log("Cart purchase: Stack trace: " . $receipt_e->getTraceAsString());
        }

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Success</title>
    <style>
    body {
        font-family: 'Circular Std', sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
    }

    .success-container {
        max-width: 600px;
        padding: 2rem;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .success-icon {
        font-size: 5rem;
        color: #28a745;
        margin-bottom: 1rem;
    }

    h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #333;
    }

    p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        color: #666;
    }

    .btn {
        display: inline-block;
        padding: 0.8rem 2rem;
        background-color: #473BF0;
        color: #fff;
        text-decoration: none;
        border-radius: 4px;
        font-size: 1rem;
        transition: background-color 0.3s;
    }

    .btn:hover {
        background-color: #3730c0;
    }

    .error-icon {
        font-size: 5rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .countdown {
        font-size: 1.2rem;
        margin-top: 1rem;
        color: #666;
    }
    </style>
</head>

<body>
    <div class="success-container">
        <?php if ($messageSuccess): ?>
        <div class="success-icon">✓</div>
        <h1>Payment Successful!</h1>
        <p><?php echo $messageSuccess; ?></p>
        <p>Your tickets have been added to your account.</p>
        <p><small>Your payment information is securely stored with Stripe for future purchases.</small></p>
        <a href="../front-end/my-ticket.php" class="btn">View My Tickets</a>
        <?php else: ?>
        <div class="error-icon">✗</div>
        <h1>Payment Error</h1>
        <p><?php echo $messageFail; ?></p>
        <a href="../front-end/cart.php" class="btn">Return to Cart</a>
        <?php endif; ?>
    </div>

    <script>
    // No auto-redirect for logged-in users - let them choose where to go
    console.log('Payment successful - no auto-redirect');
    </script>
</body>

</html>