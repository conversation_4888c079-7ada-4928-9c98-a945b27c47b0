<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Auto-detect environment for URL paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';

// Set your Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

/**
 * Enforce 2-card limit: remove oldest payment method if user has more than 2
 */
function enforcePaymentMethodLimit($stripe_customer_id) {
    try {
        // Get all payment methods for this customer
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        // If user has more than 2 payment methods, remove the oldest ones
        if (count($payment_methods->data) > 2) {
            // Sort by created timestamp (oldest first)
            $methods_array = $payment_methods->data;
            usort($methods_array, function($a, $b) {
                return $a->created - $b->created;
            });

            // Remove oldest methods until we have exactly 2
            $methods_to_remove = count($methods_array) - 2;
            for ($i = 0; $i < $methods_to_remove; $i++) {
                $oldest_method = $methods_array[$i];
                $oldest_method->detach();
                error_log("Enforced 2-card limit: Removed oldest payment method {$oldest_method->id} for customer {$stripe_customer_id}");
            }
        }
    } catch (\Exception $e) {
        error_log("Error enforcing payment method limit: " . $e->getMessage());
    }
}

/**
 * Enforce 2-card limit in database: remove oldest payment method if user has more than 2
 */
function enforceDatabasePaymentMethodLimit($user_id) {
    global $conn;

    try {
        // Get count of payment methods for this user
        $count_query = "SELECT COUNT(*) as count FROM payment_methods WHERE user_id = ?";
        $stmt = $conn->prepare($count_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $count_result = $stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $payment_method_count = $count_row['count'];

        // If user has more than 2 payment methods, remove the oldest ones
        if ($payment_method_count > 2) {
            // Get oldest payment methods (excluding default)
            $oldest_query = "SELECT id, payment_method_id FROM payment_methods
                           WHERE user_id = ? AND is_default = 0
                           ORDER BY created_at ASC
                           LIMIT ?";
            $methods_to_remove = $payment_method_count - 2;
            $stmt = $conn->prepare($oldest_query);
            $stmt->bind_param("ii", $user_id, $methods_to_remove);
            $stmt->execute();
            $oldest_result = $stmt->get_result();

            // Remove oldest payment methods
            while ($method = $oldest_result->fetch_assoc()) {
                $method_id = $method['id'];
                $payment_method_id = $method['payment_method_id'];

                // Delete from database
                $delete_query = "DELETE FROM payment_methods WHERE id = ? AND user_id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->bind_param("ii", $method_id, $user_id);
                $delete_stmt->execute();

                error_log("Enforced 2-card database limit: Removed oldest payment method {$payment_method_id} (DB ID: {$method_id}) for user {$user_id}");
            }
        }
    } catch (\Exception $e) {
        error_log("Error enforcing database payment method limit: " . $e->getMessage());
    }
}

$session_id = $_GET['session_id'] ?? '';
// Note: payment_intent parameter from URL is not reliable for displaying payment method ID
// We'll retrieve the actual payment method ID from Stripe checkout session instead

if (!$session_id) {
    header('location: ' . $url_base . '/front-end/cart.php');
    exit();
}

// Initialize variables for purchase type detection
$is_guest_purchase = false;
$is_existing_user_purchase = false;
$guest_credentials = null;

$user_id = $_SESSION['user_id'] ?? null;

// Initialize variables
$is_guest_purchase = false;
$is_existing_user_purchase = false;

// Debug: Check session status
error_log("Payment success DEBUG: Session user_id = " . ($user_id ?? 'null'));
error_log("Payment success DEBUG: Session username = " . ($_SESSION['username'] ?? 'null'));

// Retrieve the checkout session early (needed for fallback processing)
try {
    $checkout_session = \Stripe\Checkout\Session::retrieve($session_id);
} catch (Exception $e) {
    error_log("Error retrieving checkout session: " . $e->getMessage());
    header('location: ' . $url_base . '/front-end/cart.php');
    exit();
}

// Check if user is logged in first
if ($user_id) {
    // User is already logged in - this is a regular logged-in user purchase
    error_log("Payment success DEBUG: User is logged in (user_id: $user_id) - regular purchase");
} else {
    // User is NOT logged in - check for existing user or create new account
    error_log("Payment success DEBUG: User is NOT logged in - checking for existing user or creating new account");

    $customer_details = $checkout_session->customer_details;
    $email = $customer_details->email ?? '';
    $full_name = $customer_details->name ?? '';

    if (empty($email)) {
        error_log("Payment success: No email found in checkout session");
        header("Location: " . $url_base . "/index.php?error=no_email");
        exit();
    }

    // Check if user already exists with this email
    $check_user = $conn->prepare("SELECT id, username FROM user WHERE email = ? LIMIT 1");
    $check_user->bind_param("s", $email);
    $check_user->execute();
    $user_result = $check_user->get_result();

    if ($user_result->num_rows > 0) {
        // EXISTING USER - Add tickets to existing account, show login form
        $existing_user = $user_result->fetch_assoc();
        $user_id = $existing_user['id'];
        $username = $existing_user['username'];
        $check_user->close();

        // Set flags for existing user
        $is_existing_user_purchase = true;
        $is_guest_purchase = false;

        error_log("Payment success: Found existing user: $username (email: $email) - will show login form");
    } else {
        // NEW USER - Create new account
        // First create user with temporary username to get the ID
        $temp_username = 'temp_' . rand(10000,99999);
        $password = bin2hex(random_bytes(4));
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        $address = $customer_details->address ?? null;
        $address1 = $address->line1 ?? '';
        $address2_raw = $address->line2 ?? '';
        $city = $address->city ?? '';
        $state = $address->state ?? '';
        $postal_code = $address->postal_code ?? '';
        $country = $address->country ?? '';

        // Parse address2 to extract suburb/district if it's appended with comma
        $address2 = $address2_raw;
        $district = '';

        if (!empty($address2_raw) && strpos($address2_raw, ',') !== false) {
            $parts = explode(',', $address2_raw);
            if (count($parts) >= 2) {
                $address2 = trim($parts[0]); // Clean address2 (remove suburb)
                $district = trim($parts[1]); // Extract suburb as district
                error_log("Payment success: Parsed address2 '$address2_raw' into address2='$address2' and district='$district'");
            }
        }

        // Fallback: try to get district from Stripe fields if parsing didn't work
        if (empty($district)) {
            $district = $address->district ?? ($address->suburb ?? '');
        }

        $registration_time = date('Y-m-d H:i:s');

        $create_user = $conn->prepare("INSERT INTO user (username, email, password, address, address2, district, city, state, country, postal_code, registration_time, first_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $create_user->bind_param("ssssssssssss", $temp_username, $email, $hashed_password, $address1, $address2, $district, $city, $state, $country, $postal_code, $registration_time, $full_name);

        if ($create_user->execute()) {
            $user_id = $conn->insert_id;

            // Now update with the proper username format: HC + user_id
            $username = 'HC' . $user_id;
            $update_username = $conn->prepare("UPDATE user SET username = ? WHERE id = ?");
            $update_username->bind_param("si", $username, $user_id);
            $update_username->execute();
            $update_username->close();

            // DON'T auto-login for guest purchases - let them see credentials first
            // $_SESSION['user_id'] = $user_id;
            // $_SESSION['username'] = $username;

            $guest_credentials = [
                'customer_code' => $username,  // Changed from 'username' to 'customer_code'
                'email' => $email,
                'password' => $password
            ];
            $is_guest_purchase = true;

            error_log("Payment success: Created new user: $username (guest purchase - no auto-login)");
        } else {
            error_log("Payment success: Failed to create new user: " . $create_user->error);
            header("Location: " . $url_base . "/index.php?error=user_creation_failed");
            exit();
        }
        $create_user->close();
    }

    // Process purchase using webhook logic (more reliable than buyprocess)
    try {
        // Check if already processed
        $check_processed = $conn->prepare("SELECT COUNT(*) FROM purchasetickets WHERE transactionid = ?");
        $check_processed->bind_param("s", $session_id);
        $check_processed->execute();
        $check_processed->bind_result($already_processed);
        $check_processed->fetch();
        $check_processed->close();

        if ($already_processed == 0) {
            // Get cart data from database using cart_session_id from metadata
            $metadata = $checkout_session->metadata;
            $cart_session_id = $metadata['cart_session_id'] ?? null;

            if (!$cart_session_id) {
                throw new Exception("No cart session ID found in checkout session metadata");
            }

            // Retrieve cart data from database
            $cart_query = $conn->prepare("SELECT cart_data FROM cart_sessions WHERE session_id = ?");
            $cart_query->bind_param("s", $cart_session_id);
            $cart_query->execute();
            $cart_query->bind_result($cart_data_json);
            $cart_query->fetch();
            $cart_query->close();

            if (!$cart_data_json) {
                throw new Exception("No cart data found for session: $cart_session_id");
            }

            $purchased_items = json_decode($cart_data_json, true);
            if (!$purchased_items) {
                throw new Exception("Invalid cart data format");
            }

            $conn->begin_transaction();

            foreach ($purchased_items as $item) {
                $ticket_type = $item['ticket_type'];
                $package_size = $item['package_size'];
                $numbers_per_package = (int)$item['numbers_per_package'];
                $dollar_price_per_package = $item['dollar_price_per_package'];
                $quantity = $item['quantity'];

                $total_tickets = $numbers_per_package * $quantity;

                // Determine ticket column
                if (stripos($ticket_type, 'starter') !== false) $column = 'starter_tickets';
                elseif (stripos($ticket_type, 'premium') !== false || stripos($ticket_type, 'business') !== false) $column = 'premium_tickets';
                elseif (stripos($ticket_type, 'ultimate') !== false) $column = 'ultimate_tickets';
                else $column = 'starter_tickets'; // default

                // Update user tickets
                $update = $conn->prepare("UPDATE user SET $column = $column + ? WHERE id = ?");
                $update->bind_param("ii", $total_tickets, $user_id);
                if (!$update->execute()) {
                    throw new Exception("Failed to update ticket count: " . $update->error);
                }

                // Insert into purchasetickets
                $insert = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)");
                $insert->bind_param("sssiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $session_id, $total_tickets);
                if (!$insert->execute()) {
                    throw new Exception("Failed to insert purchase record: " . $insert->error);
                }

                error_log("Payment success: Added $total_tickets $ticket_type tickets to user $username");
            }

            // Create Stripe customer if needed (only for new users)
            if (!empty($email) && !$is_existing_user_purchase) {
                try {
                    $stripe_customer = \Stripe\Customer::create([
                        'email' => $email,
                        'name' => $full_name,
                        'metadata' => [
                            'user_id' => $user_id,
                            'username' => $username
                        ]
                    ]);

                    // Update user with Stripe customer ID
                    $update_customer = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
                    $update_customer->bind_param("si", $stripe_customer->id, $user_id);
                    $update_customer->execute();

                    error_log("Payment success: Created Stripe customer: " . $stripe_customer->id);

                    // Handle payment method saving if requested
                    $save_payment_method = ($metadata['save_payment_method'] ?? '0') === '1';
                    if ($save_payment_method) {
                        try {
                            // Get the payment intent from the checkout session
                            $payment_intent_id = $checkout_session->payment_intent;
                            if ($payment_intent_id) {
                                $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
                                $payment_method_id = $payment_intent->payment_method;

                                if ($payment_method_id) {
                                    // Attach payment method to customer
                                    \Stripe\PaymentMethod::retrieve($payment_method_id)->attach([
                                        'customer' => $stripe_customer->id,
                                    ]);

                                    // Enforce 2-card limit after attaching new payment method
                                    enforcePaymentMethodLimit($stripe_customer->id);

                                    error_log("Payment success: Saved payment method for customer: " . $stripe_customer->id);
                                }
                            }
                        } catch (Exception $e) {
                            error_log("Payment success: Error saving payment method: " . $e->getMessage());
                        }
                    }
                } catch (Exception $e) {
                    error_log("Payment success: Error creating Stripe customer: " . $e->getMessage());
                }
            } elseif ($is_existing_user_purchase) {
                // For existing users, check if they have a Stripe customer ID and handle payment method saving
                try {
                    $check_stripe_customer = $conn->prepare("SELECT stripe_customer_id FROM user WHERE id = ?");
                    $check_stripe_customer->bind_param("i", $user_id);
                    $check_stripe_customer->execute();
                    $check_stripe_customer->bind_result($existing_stripe_customer_id);
                    $check_stripe_customer->fetch();
                    $check_stripe_customer->close();

                    if ($existing_stripe_customer_id) {
                        // User already has a Stripe customer ID, handle payment method saving if requested
                        $save_payment_method = ($metadata['save_payment_method'] ?? '0') === '1';
                        if ($save_payment_method) {
                            try {
                                $payment_intent_id = $checkout_session->payment_intent;
                                if ($payment_intent_id) {
                                    $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
                                    $payment_method_id = $payment_intent->payment_method;

                                    if ($payment_method_id) {
                                        // Attach payment method to existing customer
                                        \Stripe\PaymentMethod::retrieve($payment_method_id)->attach([
                                            'customer' => $existing_stripe_customer_id,
                                        ]);

                                        // Enforce 2-card limit after attaching new payment method
                                        enforcePaymentMethodLimit($existing_stripe_customer_id);

                                        error_log("Payment success: Saved payment method for existing customer: " . $existing_stripe_customer_id);
                                    }
                                }
                            } catch (Exception $e) {
                                error_log("Payment success: Error saving payment method for existing user: " . $e->getMessage());
                            }
                        }
                        error_log("Payment success: Used existing Stripe customer: " . $existing_stripe_customer_id);
                    } else {
                        // Existing user doesn't have Stripe customer ID, create one
                        try {
                            $stripe_customer = \Stripe\Customer::create([
                                'email' => $email,
                                'name' => $full_name,
                                'metadata' => [
                                    'user_id' => $user_id,
                                    'username' => $username
                                ]
                            ]);

                            // Update user with Stripe customer ID
                            $update_customer = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
                            $update_customer->bind_param("si", $stripe_customer->id, $user_id);
                            $update_customer->execute();

                            error_log("Payment success: Created Stripe customer for existing user: " . $stripe_customer->id);

                            // Handle payment method saving if requested
                            $save_payment_method = ($metadata['save_payment_method'] ?? '0') === '1';
                            if ($save_payment_method) {
                                try {
                                    $payment_intent_id = $checkout_session->payment_intent;
                                    if ($payment_intent_id) {
                                        $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
                                        $payment_method_id = $payment_intent->payment_method;

                                        if ($payment_method_id) {
                                            \Stripe\PaymentMethod::retrieve($payment_method_id)->attach([
                                                'customer' => $stripe_customer->id,
                                            ]);

                                            // Enforce 2-card limit after attaching new payment method
                                            enforcePaymentMethodLimit($stripe_customer->id);

                                            error_log("Payment success: Saved payment method for new Stripe customer: " . $stripe_customer->id);
                                        }
                                    }
                                } catch (Exception $e) {
                                    error_log("Payment success: Error saving payment method for existing user: " . $e->getMessage());
                                }
                            }
                        } catch (Exception $e) {
                            error_log("Payment success: Error creating Stripe customer for existing user: " . $e->getMessage());
                        }
                    }
                } catch (Exception $e) {
                    error_log("Payment success: Error handling existing user Stripe customer: " . $e->getMessage());
                }
            }

            $conn->commit();
            error_log("Payment success: Purchase processed successfully for user: $username");

            // Send receipt email
            try {
                include_once(__DIR__ . '/../functions/send-purchase-receipt.php');
                $receiptSent = sendPurchaseReceiptEmail($session_id, $email);
                if ($receiptSent) {
                    error_log("Payment success: Receipt email sent successfully to $email");
                } else {
                    error_log("Payment success: Failed to send receipt email to $email");
                }
            } catch (Exception $receipt_e) {
                error_log("Payment success: Error sending receipt email: " . $receipt_e->getMessage());
            }

        } else {
            error_log("Payment success: Purchase already processed");
        }
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        error_log("Payment success: Error processing purchase: " . $e->getMessage());
    }
}

// Clear cart after successful payment - COMPREHENSIVE CLEARING
error_log("Payment success: Starting cart cleanup - is_guest_purchase: " . ($is_guest_purchase ? 'true' : 'false') . ", user_id: " . ($user_id ?? 'null'));

// Clear session-based cart data (for all non-login purchases)
$session_cart_cleared = false;
if (isset($_SESSION['guest_cart'])) {
    unset($_SESSION['guest_cart']);
    $session_cart_cleared = true;
    error_log("Payment success: Cleared guest_cart from session");
}

// Also clear any other cart-related session variables
$other_cart_vars = ['cart_items', 'cart_data', 'user_cart'];
foreach ($other_cart_vars as $var) {
    if (isset($_SESSION[$var])) {
        unset($_SESSION[$var]);
        $session_cart_cleared = true;
        error_log("Payment success: Cleared {$var} from session");
    }
}

if (!$session_cart_cleared) {
    error_log("Payment success: No session cart data found to clear");
}

// Clear database cart for logged-in users
if ($user_id) {
    try {
        $clear_cart_query = "UPDATE cart SET status = 'completed' WHERE user_id = ? AND status = 'active'";
        $clear_cart_stmt = $conn->prepare($clear_cart_query);
        $clear_cart_stmt->bind_param("i", $user_id);
        $clear_cart_stmt->execute();

        if ($clear_cart_stmt->affected_rows > 0) {
            error_log("Payment success: Cleared database cart for user_id: $user_id");
        } else {
            error_log("Payment success: No active database cart found for user_id: $user_id");
        }
    } catch (Exception $e) {
        error_log("Payment success: Error clearing cart for user_id $user_id: " . $e->getMessage());
    }
}

// Clear cart_sessions data (used during checkout process)
if (!empty($session_id)) {
    try {
        // Get cart_session_id from Stripe metadata
        $metadata = $checkout_session->metadata ?? [];
        $cart_session_id = $metadata['cart_session_id'] ?? null;

        if ($cart_session_id) {
            $clear_cart_sessions = $conn->prepare("DELETE FROM cart_sessions WHERE session_id = ?");
            $clear_cart_sessions->bind_param("s", $cart_session_id);
            $clear_cart_sessions->execute();

            if ($clear_cart_sessions->affected_rows > 0) {
                error_log("Payment success: Cleared cart_sessions data for session: $cart_session_id");
            } else {
                error_log("Payment success: No cart_sessions data found for session: $cart_session_id");
            }
        } else {
            error_log("Payment success: No cart_session_id found in metadata");
        }
    } catch (Exception $e) {
        error_log("Payment success: Error clearing cart_sessions: " . $e->getMessage());
    }
}

// Get payment details (checkout session already retrieved above)
$amount_total = $checkout_session->amount_total / 100; // Convert from cents
$currency = strtoupper($checkout_session->currency);
$customer_email = $checkout_session->customer_details->email ?? '';
$payment_status = $checkout_session->payment_status;

// Get payment method ID from the checkout session
$payment_method_id = null;
try {
    if ($checkout_session->payment_intent) {
        $payment_intent_obj = \Stripe\PaymentIntent::retrieve($checkout_session->payment_intent);
        $payment_method_id = $payment_intent_obj->payment_method ?? null;
        error_log("Payment success: Retrieved payment method ID: " . ($payment_method_id ?? 'null'));
    }
} catch (Exception $e) {
    error_log("Payment success: Error retrieving payment method ID: " . $e->getMessage());
}

// LOCALHOST CLEANUP: Clean up payment_temp data after showing credentials
// This ensures that on localhost, after the user sees their credentials, we clean up the temp data
if ($is_guest_purchase && $guest_credentials && $is_localhost) {
    try {
        $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
        $cleanup_stmt->bind_param("s", $session_id);
        $cleanup_stmt->execute();
        if ($cleanup_stmt->affected_rows > 0) {
            error_log("Payment success: Cleaned up payment_temp data for localhost session: $session_id");
        }
    } catch (Exception $e) {
        error_log("Payment success: Error cleaning up payment_temp: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Success - HelloIT</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: 'Circular Std', sans-serif;
    }

    .success-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .success-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 40px;
        text-align: center;
        max-width: 500px;
        width: 100%;
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        animation: pulse 2s infinite;
    }

    .success-icon i {
        color: white;
        font-size: 40px;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    .btn-primary {
        background: linear-gradient(135deg, #6B62F3, #473BF0);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 500;
    }

    .btn-outline-secondary {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 500;
    }

    .payment-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .payment-details .text-break {
        word-break: break-all;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .credentials-section {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 20px;
    }

    .copy-btn {
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .input-group .btn {
        border-left: 0;
    }

    .input-group .btn:first-of-type {
        border-left: 1px solid #ced4da;
    }

    #passwordField::placeholder {
        color: #6c757d;
        font-style: italic;
    }

    .password-help-text {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-success {
        animation: successPulse 0.3s ease-in-out;
    }

    @keyframes successPulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Editable credential fields styling */
    .credentials-section .form-control:not([readonly]) {
        background-color: #fff;
        border: 2px solid #e3f2fd;
        transition: all 0.3s ease;
    }

    .credentials-section .form-control:not([readonly]):focus {
        border-color: #2196f3;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
        background-color: #f8f9ff;
    }

    .credentials-section .form-control:not([readonly]):hover {
        border-color: #90caf9;
        background-color: #fafbff;
    }

    /* Visual indicator for editable fields */
    .credentials-section .form-control:not([readonly])::before {
        content: "✏️";
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
        opacity: 0.6;
    }

    /* Readonly field styling */
    .credentials-section .form-control[readonly] {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    /* Enhanced help text for editable fields */
    .credentials-section .text-muted {
        color: #1976d2 !important;
        font-weight: 500;
        font-size: 0.85rem;
    }

    /* Login button enhancement */
    #loginWithUpdatedCredentials {
        background: linear-gradient(135deg, #4caf50, #45a049);
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    #loginWithUpdatedCredentials:hover {
        background: linear-gradient(135deg, #45a049, #3d8b40);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    #loginWithUpdatedCredentials:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
    }

    /* Make email and password text have normal cursor instead of text cursor */
    .text-break,
    .password-display {
        cursor: default !important;
        user-select: none;
    }

    /* Ensure the credential display areas have normal cursor */
    .d-flex.align-items-center.justify-content-between.p-3.bg-light.border.rounded span {
        cursor: default !important;
        user-select: none;
    }
    </style>
</head>

<body>
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>

            <h1 class="h3 mb-3 text-success">Payment Successful!</h1>

            <?php
            // Debug: Show which condition is being used
            error_log("Payment success DEBUG: is_guest_purchase = " . ($is_guest_purchase ? 'true' : 'false'));
            error_log("Payment success DEBUG: is_existing_user_purchase = " . ($is_existing_user_purchase ? 'true' : 'false'));
            error_log("Payment success DEBUG: user_id = " . ($user_id ?? 'null'));
            ?>

            <?php if ($is_guest_purchase): ?>
            <p class="text-muted mb-4">Thank you for your purchase! Your payment method has been saved for future
                transactions.</p>
            <p class="text-muted mb-4">Your tickets have been added to your account.</p>
            <?php elseif ($is_existing_user_purchase): ?>
            <p class="text-muted mb-4">Thank you for your purchase! Your payment has been processed successfully.</p>
            <p class="text-muted mb-4">Your tickets have been added to your existing account.</p>
            <?php else: ?>
            <p class="text-muted mb-4">Thank you for your purchase. Your payment has been processed successfully.</p>
            <?php endif; ?>

            <div class="payment-details">
                <div class="row">
                    <div class="col-12 col-sm-6 text-start">
                        <strong>Amount:</strong>
                    </div>
                    <div class="col-12 col-sm-6 text-start text-sm-end">
                        <?php echo $currency; ?> <?php echo number_format($amount_total, 2); ?>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12 col-sm-6 text-start">
                        <strong>Status:</strong>
                    </div>
                    <div class="col-12 col-sm-6 text-start text-sm-end">
                        <span class="badge bg-success"><?php echo ucfirst($payment_status); ?></span>
                    </div>
                </div>
                <?php if ($payment_method_id): ?>
                <div class="row mt-2">
                    <div class="col-12 col-sm-6 text-start">
                        <strong>Payment Method ID:</strong>
                    </div>
                    <div class="col-12 col-sm-6 text-start text-sm-end">
                        <small class="text-muted text-break" style="word-break: break-all; font-family: monospace;"><?php echo htmlspecialchars($payment_method_id); ?></small>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <?php if ($is_guest_purchase): ?>
            <?php if ($guest_credentials): ?>
            <?php if (isset($guest_credentials['pending']) && $guest_credentials['pending']): ?>
            <!-- Guest Purchase - User Creation Pending -->
            <div class="alert alert-warning mt-4">
                <h5><i class="fas fa-clock me-2"></i>Account Being Created</h5>
                <p class="mb-3">Your account is being set up. Here are your credentials (save them now):</p>
                <?php else: ?>
                <!-- Guest Purchase - Show Credentials -->
                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-user-plus me-2"></i>Your Account Credentials</h5>
                    <p class="mb-3">Your account has been created! Please save these credentials:</p>
                    <?php endif; ?>

                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label fw-bold text-start">Email:</label>
                            <div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded">
                                <span class="text-break"><?php echo htmlspecialchars($guest_credentials['email']); ?></span>
                                <button class="btn btn-outline-secondary copy-btn" type="button"
                                    onclick="copyToClipboard('<?php echo htmlspecialchars($guest_credentials['email']); ?>')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold text-start">Password:</label>
                            <div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded">
                                <span class="password-display" id="passwordDisplay">••••••••</span>
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePasswordBtn"
                                        onclick="togglePasswordVisibility()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" id="passwordCopyBtn"
                                        onclick="copyToClipboard('<?php echo htmlspecialchars($guest_credentials['password']); ?>')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted text-start">Use this password to sign in. You can change it later in your profile.</small>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3 mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> Please save these credentials in a safe place. You'll need them to
                        access your account and tickets.
                    </div>
                </div>

                <div class="d-grid gap-2 mt-4">
                    <button type="button" class="btn btn-primary" id="confirmAndGoToSignIn">
                        <i class="fas fa-user-check me-2"></i>Confirm & Go to Sign-in
                    </button>
                    <small class="text-muted text-center">
                        <i class="fas fa-info-circle me-1"></i>
                        Your credentials will be saved and you'll be redirected to sign-in page
                    </small>
                </div>
                <?php else: ?>
                <!-- No credentials found - account still being set up -->
                <div class="alert alert-warning mt-4">
                    <h5><i class="fas fa-clock me-2"></i>Account Being Set Up</h5>
                    <p class="mb-3">Your account is still being created. Please wait a moment and refresh this page.</p>
                    <div class="d-grid gap-2">
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-refresh me-2"></i>Refresh Page
                        </button>
                        <a href="<?php echo $url_base; ?>/front-end/sign-in.php" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-in-alt me-2"></i>Try Log-in Your Account
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                <?php elseif ($is_existing_user_purchase): ?>
                <!-- Existing User Purchase - Show Sign-in Form -->
                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-user-check me-2"></i>Welcome Back!</h5>
                    <p class="mb-3">We found that you already have an account with us using the email
                        <strong><?php echo htmlspecialchars($email); ?></strong>.
                    </p>
                    <p class="mb-3">Your tickets have been added to your existing account. Please sign in to access
                        them.</p>

                    <!-- <div class="alert alert-success mt-3 mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <b> No new account was created.</b> Your purchase has been added to your
                        existing account.
                    </div> -->
                </div>

                <!-- Sign-in Form -->
                <div class="signin-form-container mt-4">
                    <div class="signin-form bg-light border rounded p-4">
                        <h5 class="text-center mb-4"><i class="fas fa-sign-in-alt me-2"></i>Sign In to Access Your
                            Tickets</h5>

                        <!-- Display login error if present -->
                        <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger mb-3" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Login Failed:</strong> <?php echo htmlspecialchars($_SESSION['error']); ?>
                            <?php unset($_SESSION['error']); ?>
                        </div>
                        <?php endif; ?>

                        <form action="<?php echo $url_base; ?>/functions/sign-in-db.php" method="POST"
                            id="existingUserSigninForm">
                            <input type="hidden" name="redirect_to_tickets" value="1">
                            <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">

                            <div class="mb-3">
                                <label for="signin_email" class="form-label fw-bold">Email Address</label>
                                <input type="email" class="form-control" id="signin_email" name="username"
                                    value="<?php echo htmlspecialchars($email); ?>" readonly
                                    style="background-color: #e9ecef;">
                                <small class="text-muted">This email was used for your purchase</small>
                            </div>

                            <div class="mb-3">
                                <label for="signin_password" class="form-label fw-bold">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="signin_password" name="password"
                                        placeholder="Enter your password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleSigninPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="signin_user" class="btn btn-primary">
                                    <i class="fas fa-ticket-alt me-2"></i>Sign In & View My Tickets
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <a href="#" class="text-decoration-none" data-bs-toggle="modal"
                                data-bs-target="#forgotPasswordModal">
                                <i class="fas fa-question-circle me-1"></i>Forgot your password?
                            </a>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <!-- Logged-in User Purchase -->
                <div class="d-grid gap-2 mt-4">
                    <a href="<?php echo $url_base; ?>/front-end/my-ticket.php" class="btn btn-primary">
                        <i class="fas fa-ticket-alt me-2"></i>View My Tickets
                    </a>
                    <a href="<?php echo $url_base; ?>/front-end/purchase-history.php" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-2"></i>Purchase History
                    </a>
                </div>
                <?php endif; ?>

                <div class="mt-4 pt-3 border-top">
                    <p class="small text-muted mb-0">
                        <i class="fas fa-shield-alt me-1"></i>
                        Your payment was processed securely by Stripe
                    </p>
                </div>
            </div>
        </div>

        <!-- Forgot Password Modal -->
        <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="forgotPasswordModalLabel">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="resetPasswordForm">
                            <div class="mb-3">
                                <label for="resetEmail" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="resetEmail" name="email"
                                    value="<?php echo htmlspecialchars($email ?? ''); ?>" readonly
                                    style="background-color: #e9ecef;">
                                <small class="form-text text-muted">Enter the email address associated with your
                                    account.</small>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-secondary w-100" id="send2FACodeBtn"
                                    style="color: white; background-color: #473BF0;">Send 2FA Code</button>
                            </div>
                            <div class="mb-3" id="codeGroup" style="display:none;">
                                <label for="reset2FACode" class="form-label">2FA Code</label>
                                <input type="text" class="form-control" id="reset2FACode" name="code"
                                    placeholder="Enter the 2FA code" maxlength="6">
                            </div>
                            <div class="mb-3" id="passwordGroup" style="display:none;">
                                <label for="newPassword" class="form-label">New Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="newPassword" name="new_password"
                                        placeholder="Enter new password" minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword"><i
                                            class="fas fa-eye"></i></button>
                                </div>
                                <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                            </div>
                            <div class="mb-3" id="confirmPasswordGroup" style="display:none;">
                                <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirmPassword"
                                        name="confirm_password" placeholder="Confirm new password">
                                    <button class="btn btn-outline-secondary" type="button"
                                        id="toggleConfirmPassword"><i class="fas fa-eye"></i></button>
                                </div>
                            </div>
                            <div class="mb-3" id="updatePasswordGroup" style="display:none;">
                                <button type="button" class="btn btn-primary w-100" id="resetPasswordBtn"
                                    style="color: white; background-color: #473BF0;">Update
                                    Password</button>
                            </div>
                            <div id="resetMessage" class="alert" style="display: none;"></div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>

                    </div>
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

        <script>
        // Clear any remaining cart data from client storage (for all purchases)
        // This runs for all purchase types to ensure complete cleanup
        if (typeof(Storage) !== "undefined") {
            // Clear all possible cart-related storage items
            const cartKeys = [
                'guest_cart', 'cart_items', 'cart_data', 'user_cart',
                'checkout_cart', 'temp_cart', 'shopping_cart'
            ];

            let clearedItems = [];

            cartKeys.forEach(function(key) {
                // Clear from localStorage
                if (localStorage.getItem(key) !== null) {
                    localStorage.removeItem(key);
                    clearedItems.push('localStorage.' + key);
                }

                // Clear from sessionStorage
                if (sessionStorage.getItem(key) !== null) {
                    sessionStorage.removeItem(key);
                    clearedItems.push('sessionStorage.' + key);
                }
            });

            if (clearedItems.length > 0) {
                console.log('Cart data cleared from client storage:', clearedItems.join(', '));
            } else {
                console.log('No cart data found in client storage to clear');
            }
        }

        // Enhanced clipboard function with fallbacks
        function copyToClipboardAdvanced(text, button) {
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                return navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess(button);
                    return true;
                }).catch(function(err) {
                    console.warn('Clipboard API failed, trying fallback:', err);
                    return fallbackCopyTextToClipboard(text, button);
                });
            } else {
                // Use fallback method
                return fallbackCopyTextToClipboard(text, button);
            }
        }

        // Fallback copy method for older browsers or non-HTTPS
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    showCopySuccess(button);
                    return Promise.resolve(true);
                } else {
                    throw new Error('execCommand failed');
                }
            } catch (err) {
                document.body.removeChild(textArea);
                console.error('Fallback copy failed:', err);

                // Final fallback - show text in prompt for manual copy
                const userAgent = navigator.userAgent.toLowerCase();
                if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
                    // Mobile devices - show in alert
                    alert('Please copy this manually:\n\n' + text);
                } else {
                    // Desktop - show in prompt
                    prompt('Copy this text manually (Ctrl+C):', text);
                }
                return Promise.resolve(false);
            }
        }

        // Show visual success feedback
        function showCopySuccess(button, customMessage = null) {
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('btn-success');
            button.classList.remove('btn-outline-secondary');

            // Show custom message if provided (for password)
            if (customMessage) {
                const smallText = document.querySelector('.col-12:last-child .text-muted');
                if (smallText) {
                    const originalText = smallText.textContent;
                    smallText.textContent = customMessage;
                    smallText.style.color = '#28a745';

                    setTimeout(function() {
                        smallText.textContent = originalText;
                        smallText.style.color = '';
                    }, 2000);
                }
            }

            setTimeout(function() {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }

        function copyToClipboard(text) {
            const button = event.target.closest('button');
            copyToClipboardAdvanced(text, button);
        }

        function togglePasswordVisibility() {
            const passwordDisplay = document.getElementById('passwordDisplay');
            const toggleBtn = document.getElementById('togglePasswordBtn');
            const icon = toggleBtn.querySelector('i');
            const actualPassword = '<?php echo htmlspecialchars($guest_credentials['password'] ?? ''); ?>';

            if (passwordDisplay.textContent === '••••••••') {
                passwordDisplay.textContent = actualPassword;
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordDisplay.textContent = '••••••••';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        <?php if ($is_guest_purchase && $guest_credentials): ?>
        // Handle confirm and redirect to sign-in for guest users
        document.getElementById('confirmAndGoToSignIn').addEventListener('click', function() {
            const confirmBtn = this;
            const email = '<?php echo htmlspecialchars($guest_credentials['email']); ?>';
            const customerCode = '<?php echo htmlspecialchars($guest_credentials['customer_code']); ?>';
            const password = '<?php echo htmlspecialchars($guest_credentials['password']); ?>';

            // Show loading state
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Redirecting...';

            // Clean up payment data
            cleanupPaymentData();

            // Redirect to sign-in page with credentials
            setTimeout(function() {
                redirectToSignIn(customerCode, password);
            }, 1000);
        });

        function redirectToSignIn(customerCode, password) {
            // Clean up payment data before redirect
            cleanupPaymentData();

            // Redirect to sign-in page with pre-filled credentials
            const signInUrl = '<?php echo $url_base; ?>/front-end/sign-in.php' +
                '?email=' + encodeURIComponent('<?php echo htmlspecialchars($guest_credentials['email']); ?>') +
                '&password=' + encodeURIComponent(password) +
                '&from_payment=1';

            window.location.href = signInUrl;
        }

        function cleanupPaymentData() {
            // Clean up payment_temp data after successful login
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $url_base; ?>/functions/cleanup-payment-data.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            const params = 'session_id=' + encodeURIComponent('<?php echo htmlspecialchars($session_id); ?>');
            xhr.send(params);
        }
        <?php endif; ?>

        <?php if (!$is_guest_purchase): ?>
        // No auto-redirect for logged-in users - let them choose where to go
        console.log('Payment successful for logged-in user - no auto-redirect');
        <?php endif; ?>

        // Password toggle functionality for existing user sign-in form
        <?php if ($is_existing_user_purchase): ?>
        document.getElementById('toggleSigninPassword').addEventListener('click', function() {
            const passwordField = document.getElementById('signin_password');
            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Password toggle for reset password modal
        document.getElementById('toggleNewPassword').addEventListener('click', function() {
            const passwordField = document.getElementById('newPassword');
            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
            const passwordField = document.getElementById('confirmPassword');
            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Reset password functionality
        document.getElementById('resetPasswordBtn').addEventListener('click', function() {
            const email = document.getElementById('resetEmail').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const messageDiv = document.getElementById('resetMessage');
            const resetBtn = this;

            // Validation
            if (!email || !newPassword || !confirmPassword) {
                showResetMessage('Please fill in all fields.', 'danger');
                return;
            }

            if (newPassword !== confirmPassword) {
                showResetMessage('Passwords do not match.', 'danger');
                return;
            }

            if (newPassword.length < 6) {
                showResetMessage('Password must be at least 6 characters long.', 'danger');
                return;
            }

            // Show loading state
            resetBtn.disabled = true;
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';

            // Send AJAX request
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $url_base; ?>/functions/reset-password.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    resetBtn.disabled = false;
                    // resetBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Reset Password';

                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                showResetMessage(response.message, 'success');
                                // Clear form
                                document.getElementById('resetPasswordForm').reset();
                                // Auto-close modal after 3 seconds
                                setTimeout(function() {
                                    bootstrap.Modal.getInstance(document.getElementById(
                                        'forgotPasswordModal')).hide();
                                }, 3000);
                            } else {
                                showResetMessage(response.message, 'danger');
                            }
                        } catch (e) {
                            showResetMessage('An error occurred. Please try again.', 'danger');
                        }
                    } else {
                        showResetMessage('Network error. Please try again.', 'danger');
                    }
                }
            };

            const params = 'email=' + encodeURIComponent(email) + '&new_password=' + encodeURIComponent(
                newPassword);
            xhr.send(params);
        });

        function showResetMessage(message, type) {
            const messageDiv = document.getElementById('resetMessage');
            messageDiv.className = 'alert alert-' + type;
            messageDiv.innerHTML = message;
            messageDiv.style.display = 'block';

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }
        <?php endif; ?>

        document.getElementById('send2FACodeBtn').addEventListener('click', function() {
            var email = document.getElementById('resetEmail').value;
            var btn = this;
            btn.disabled = true;
            btn.innerHTML = 'Sending...';
            fetch('<?php echo $url_base; ?>/functions/reset-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'action=send_2fa&email=' + encodeURIComponent(email)
                })
                .then(response => response.json())
                .then(data => {
                    btn.disabled = false;
                    btn.innerHTML = 'Send 2FA Code';
                    showResetMessage(data.message, data.success ? 'success' : 'danger');
                    if (data.success) {
                        document.getElementById('codeGroup').style.display = '';
                        document.getElementById('passwordGroup').style.display = '';
                        document.getElementById('confirmPasswordGroup').style.display = '';
                        document.getElementById('updatePasswordGroup').style.display = '';
                    }
                })
                .catch(() => {
                    btn.disabled = false;
                    btn.innerHTML = 'Send 2FA Code';
                    showResetMessage('Network error. Please try again.', 'danger');
                });
        });

        document.getElementById('resetPasswordBtn').addEventListener('click', function() {
            var email = document.getElementById('resetEmail').value;
            var code = document.getElementById('reset2FACode').value;
            var newPassword = document.getElementById('newPassword').value;
            var confirmPassword = document.getElementById('confirmPassword').value;
            var resetBtn = this;
            if (!email || !code || !newPassword || !confirmPassword) {
                showResetMessage('Please fill in all fields.', 'danger');
                return;
            }
            if (newPassword !== confirmPassword) {
                showResetMessage('Passwords do not match.', 'danger');
                return;
            }
            if (newPassword.length < 6) {
                showResetMessage('Password must be at least 6 characters long.', 'danger');
                return;
            }
            resetBtn.disabled = true;
            resetBtn.innerHTML = 'Updating...';
            fetch('<?php echo $url_base; ?>/functions/reset-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'action=reset_password&email=' + encodeURIComponent(email) + '&code=' +
                        encodeURIComponent(code) + '&new_password=' + encodeURIComponent(newPassword)
                })
                .then(response => response.json())
                .then(data => {
                    resetBtn.disabled = false;
                    resetBtn.innerHTML = 'Update Password';
                    showResetMessage(data.message, data.success ? 'success' : 'danger');
                    if (data.success) {
                        document.getElementById('resetPasswordForm').reset();
                        setTimeout(function() {
                            bootstrap.Modal.getInstance(document.getElementById(
                                'forgotPasswordModal')).hide();
                        }, 3000);
                    }
                })
                .catch(() => {
                    resetBtn.disabled = false;
                    resetBtn.innerHTML = 'Update Password';
                    showResetMessage('Network error. Please try again.', 'danger');
                });
        });
        </script>
</body>

</html>