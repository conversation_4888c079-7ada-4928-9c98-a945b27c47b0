<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
require '../vendor/autoload.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>pricing</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->


    <!-- Existing head content -->
    <script src="https://js.stripe.com/v3/"></script>

    <script> </script>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php
        include('../header-footer/newnavtest.php');
        ?>
        <!-- navbar-dark -->
        <!-- navbar- -->
        <!-- <div class="inner-banner bg-default-2 pt-25 pt-lg-29">
            <div class="container">
                <div class="row  justify-content-center pt-5">
                    <div class="col-xl-10 col-lg-11">
                        <div class="text-center">
                            <h2 class="title gr-text-2 mb-5 mb-lg-8">Pick The Plan That Works For You</h2>
                            <p class="gr-text-6 mb-0">Whether you're a small, medium or large organization, we have the right support <br> plan that fits your need.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- Section 1 -->
        <!-- bg-default-2 -->
        <div class="inner-banner  pt-30 pt-lg-34">
            <div class="container">
                <div class="row  justify-content-center pt-5">
                    <div class="col-xl-10 col-lg-11">
                        <div class="text-center">
                            <h2 class="title gr-text-2 mb-5 mb-lg-8">Pick The Plan That Works For You</h2>
                            <p class="gr-text-6 mb-0">Whether you're a small, medium or large organization, we have the
                                right support <br> plan that fits your need.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>




        <!-- section 2 -->
        <!-- toggle -->
        <style>
        .shadepro-pricing-tabs {
            margin-top: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            /* Center the toggle switch */
        }

        .shadepro-pricing-tab {
            background-color: #f0f0f0;
            /* Light grey background for the container */
            border-radius: 20px;
            /* Rounded corners for the container */
            padding: 5px;
            /* Small padding inside the container */
            display: inline-flex;
            /* Align items horizontally */
            overflow: hidden;
            /* Hide any overflow from the active state */
            border: 1px solid #ddd;
            /* Optional subtle border */
        }

        .shadepro-pricing-tab a {
            display: block;
            /* padding: 10px 20px; */
            padding-left: 20px;
            padding-right: 20px;
            text-decoration: none;
            color: #777;
            /* Grey color for inactive tabs */
            font-weight: bold;
            border-radius: 20px;
            transition: background-color 0.3s ease, color 0.3s ease;
            white-space: nowrap;
            /* Prevent text from wrapping */
        }

        .shadepro-pricing-tab a.active {
            background-color: #5045F1;
            /* Blue background for the active tab */
            color: #fff;
            /* White text for the active tab */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            /* Subtle shadow for active state */
        }

        .shadepro-pricing-tab a:not(.active):hover {
            color: #333;
        }

        .tabs-title-toggle {
            font-family: 'Circular Std', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            color: #6c757d;
            /* Neutral text color */
            text-decoration: none;
            padding: 5px 10px;
            /* Reduced padding for smaller button size */
            border-radius: 5px;
            transition: all 0.3s ease;
            height: 35px;
        }

        .tabs-title-toggle:hover {
            background-color: #e9ecef;
            /* Light hover effect */
            color: #495057;
        }

        .tabs-title-toggle.active {
            background-color: #5045F1;
            /* Active tab background */
            color: #ffffff;
            /* White text for active tab */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
            /* Slight shadow for active tab */
        }

        /* Most Popular Ribbon Styles */
        .popular-ribbon {
            position: absolute;
            top: 35px;
            right: -55px;
            background: #5045F1;
            color: white;
            padding: 5px 55px 5px 55px;
            /* Increased left and right padding */
            transform: rotate(45deg);
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            z-index: 1;
            text-align: center;
        }

        .popular-ribbon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #5045F1;
            transform: rotate(45deg);
            z-index: -1;
            display: none;
        }

        .shadepro-pricing-item {
            position: relative;
            overflow: hidden;
        }
        </style>
        <!--                            ///////////////////////////////////////////// -->

        <div class="container-fluid pricing-container" style="padding-left: 100px; padding-right: 100px;">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="shadepro-pricing-tabs style-2">
                        <div class="shadepro-pricing-tab">
                            <a href="javascript:" class="tabs-title-toggle first-tabs-title active"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="starter">
                                Starter
                            </a>
                            <a href="javascript:" class="tabs-title-toggle second-tabs-title"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="business">
                                Business
                            </a>
                            <a href="javascript:" class="tabs-title-toggle second-tabs-title"
                                data-pricing-tab-trigger="" data-target="#shadepro-dynamic-deck"
                                data-category="ultimate">
                                Ultimate
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- product pricing -->
            <div class="pricing-box-wrap d-flex flex-wrap justify-content-center" id="shadepro-dynamic-deck"
                data-pricing-dynamic="" data-value-active="monthly">
                <?php
                // Fetch products from the database
                $sql = "SELECT ticketid, ticket_type, package_size, info ,numbers_per_package, dollar_price_per_package FROM tickets";
                $result = $conn->query($sql);
                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        echo '<div class="shadepro-pricing-item-wrap" data-category="' . htmlspecialchars(strtolower($row['ticket_type'])) . '">';
                        echo '<div class="shadepro-pricing-item" data-plan="' . htmlspecialchars(strtolower($row['ticket_type'])) . '">';

                        // Add Most Popular ribbon for 25 Ticket Credits
                        if ($row['numbers_per_package'] == 25) {
                            echo '<div class="popular-ribbon">Most Popular</div>';
                        }

                        // Plan name
                        echo '<div class="plan-name">' . htmlspecialchars($row['ticket_type']) . '</div>';

                        // Price section
                        echo '<div class="price-section">';

                        // Check if this is the $350 package for discount display
                        if ($row['dollar_price_per_package'] == 350) {
                            echo '<div class="original-price">$375</div>';
                        }

                        echo '<div class="price-amount"><span class="currency">$</span>' . number_format($row['dollar_price_per_package']) . '</div>';
                        echo '<div class="price-label">' . htmlspecialchars($row['numbers_per_package']) . ' Ticket Credits</div>';
                        echo '</div>';

                        // Description - Service types based on plan
                        echo '<div class="plan-description">';
                        switch (strtoupper($row['ticket_type'])) {
                            case 'STARTER':
                                echo 'Ticketing';
                                break;
                            case 'BUSINESS':
                                echo 'Ticketing / Chat / Email / Remote Access';
                                break;
                            case 'ULTIMATE':
                                echo 'Ticketing / Chat / Email / Remote Access';
                                break;
                            default:
                                echo 'Ticketing';
                                break;
                        }
                        echo '</div>';

                        // Features section
                        echo '<div class="features-section">';
                        echo '<div class="features-wrapper">';
                        echo '<ul class="features-list">';
                        switch (strtoupper($row['ticket_type'])) {
                            case 'STARTER':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">24 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Basic PC troubleshooting</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Software & OS support</li>';
                                break;
                            case 'BUSINESS':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">24 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting (software, OS, hardware issues)</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> + Custom Configurations</li>';
                                break;
                            case 'ULTIMATE':
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">4 hours respond time</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> <b style="font-weight: 700;">Online knowledge base</b></li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> PC & Network troubleshooting (software, OS, hardware issues)</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> + Custom Configurations</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Dedicated AE</li>';
                                echo '<li class="feature-included"><i class="fas fa-check"></i> Priority Help Desk</li>';
                                break;
                            default:
                                echo '<p>' . htmlspecialchars($row['info']) . '</p>';
                                break;
                        }
                        echo '</ul>'; // Close features list
                        echo '</div>'; // Close features wrapper
                        echo '</div>'; // Close features section

                        // Action button section
                        echo '<div class="action-section">';
                        echo '<button class="get-plan-btn" onclick="addToCart(' . htmlspecialchars($row['ticketid']) . ')">';
                        echo 'Get this plan';
                        echo '</button>';
                        echo '<span style="font-size: 12px; margin-top: 20px;">09:00 AM - 06:00 PM (GMT+8)</span>';
                        echo '</div>';

                        echo '</div>'; // Close pricing item
                        echo '</div>'; // Close pricing item wrap
                    }
                } else {
                    echo '<p>No products available.</p>';
                }
                // Don't close connection here - move to end of file
                ?>
            </div>
        </div>
        <!-- Cart Success Modal -->
        <div class="modal fade" id="cartSuccessModal" tabindex="-1" role="dialog"
            aria-labelledby="cartSuccessModalLabel" aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="success-animation">
                            <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                            </svg>
                        </div>
                        <h3 class="mt-4" id="cartSuccessMessage">Added to cart successfully!</h3>
                        <p class="mb-4">Your item has been added to your shopping cart.</p>
                        <p class="text-muted">This popup will close automatically in <span id="countdown"
                                data-initial-value="3">3</span> seconds...</p>
                        <div class="mt-4">
                            <!-- <button type="button" class="btn btn-outline-primary mr-2" data-dismiss="modal">Continue Shopping</button> -->
                            <?php
                            // Auto-detect environment for cart URL
                            $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                            $cart_url = $is_localhost ? '/helloit/support-ticket/cart' : '/support-ticket/cart';
                            ?>
                            <a href="<?php echo $cart_url; ?>" class="btn btn-primary">Go to Cart</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Error Modal -->
        <div class="modal fade" id="cartErrorModal" tabindex="-1" role="dialog" aria-labelledby="cartErrorModalLabel"
            aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="error-animation">
                            <svg class="crossmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="crossmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="crossmark__path crossmark__path--right" fill="none" d="M16,16 l20,20" />
                                <path class="crossmark__path crossmark__path--left" fill="none" d="M16,36 l20,-20" />
                            </svg>
                        </div>
                        <h3 class="mt-4" id="cartErrorMessage">Failed to add to cart</h3>
                        <p class="mb-4">Please try again or contact customer support.</p>
                        <div class="mt-4">
                            <button type="button" class="btn btn-danger" data-dismiss="modal"
                                style="color: white;">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add to cart script-->
        <script>
        // Function to add product to cart via AJAX
        function addToCart(ticket_id) {
            var quantity = 1; // Set quantity to 1

            // Send data via AJAX
            var xhr = new XMLHttpRequest();
            xhr.open("POST", "cart-add", true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            // Send ticket_id and quantity to cart-add.php
            xhr.send("ticket_id=" + ticket_id + "&quantity=" + quantity);

            // When the request is complete
            xhr.onload = function() {
                if (xhr.status == 200) {
                    try {
                        var response = JSON.parse(xhr.responseText); // Parse JSON response

                        // Show appropriate modal based on response
                        if (response.status == 'success') {
                            // Set success message and show success modal
                            document.getElementById('cartSuccessMessage').textContent = response.message ||
                                'Added to cart successfully!';
                            // Reset countdown display before showing the modal
                            document.getElementById('countdown').textContent = '3';

                            // Show the modal
                            $('#cartSuccessModal').modal('show');

                            // Start countdown for automatic close
                            var countdownElement = document.getElementById('countdown');

                            // Reset the countdown text to 3 before starting the timer
                            countdownElement.textContent = '3';
                            var secondsLeft = 3;

                            // Clear any existing interval to prevent multiple timers
                            if (window.countdownInterval) {
                                clearInterval(window.countdownInterval);
                            }

                            // Update countdown every second
                            window.countdownInterval = setInterval(function() {
                                secondsLeft--;
                                countdownElement.textContent = secondsLeft;

                                if (secondsLeft <= 0) {
                                    clearInterval(window.countdownInterval);
                                    $('#cartSuccessModal').modal('hide');
                                }
                            }, 1000);

                            // Clear interval if modal is closed manually
                            $('#cartSuccessModal').on('hidden.bs.modal', function() {
                                if (window.countdownInterval) {
                                    clearInterval(window.countdownInterval);
                                }
                            });
                        } else {
                            // Set error message and show error modal
                            document.getElementById('cartErrorMessage').textContent = response.message ||
                                'Failed to add to cart. Please try again.';
                            $('#cartErrorModal').modal('show');
                        }
                    } catch (e) {
                        // If JSON parsing fails, show error modal
                        document.getElementById('cartErrorMessage').textContent =
                            'An unexpected error occurred. Please try again.';
                        $('#cartErrorModal').modal('show');
                    }
                } else {
                    // If request fails, show error modal
                    document.getElementById('cartErrorMessage').textContent =
                        'Failed to add to cart. Please try again.';
                    $('#cartErrorModal').modal('show');
                }
            };

            // If request times out or fails
            xhr.onerror = function() {
                document.getElementById('cartErrorMessage').textContent =
                    'Network error. Please check your connection and try again.';
                $('#cartErrorModal').modal('show');
            };
        }
        </script>


        <style>
        /* Success Animation Styles */
        .success-animation {
            margin: 0 auto;
            width: 100px;
            height: 100px;
        }

        .checkmark {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #4bb71b;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #4bb71b;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 1;
            stroke-miterlimit: 2;
            stroke: #4bb71b;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {

            0%,
            100% {
                transform: none;
            }

            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 1px #4bb71b;
            }
        }

        @keyframes resetStrokeWidth {
            0% {
                stroke-width: 1;
            }

            100% {
                stroke-width: 1;
            }
        }

        /* Error Animation Styles */
        .error-animation {
            margin: 0 auto;
            width: 100px;
            height: 100px;
        }

        .crossmark {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #ff6245;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #ff6245;
            animation: fillError .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .crossmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 1;
            stroke-miterlimit: 2;
            stroke: #ff6245;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .crossmark__path {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes fillError {
            100% {
                box-shadow: inset 0px 0px 0px 1px #ff6245;
            }
        }

        /* General Styles */
        .pricing-box-wrap {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
        }

        /* Legacy styles - keeping for compatibility */
        .shadepro-pricing-btn,
        .shadepro-pricing-btn:hover {
            color: white;
        }


        /* Clean Pricing Cards - 3 per row */
        .shadepro-pricing-item-wrap {
            flex: 0 0 calc(30% - 16px);
            max-width: calc(30% - 16px);
            box-sizing: border-box;
            margin: 8px;
        }

        .shadepro-pricing-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 0;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .shadepro-pricing-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        /* Top colored border for each plan */
        .shadepro-pricing-item[data-plan="starter"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #fbbf24;
            /* Starter */
        }

        .shadepro-pricing-item[data-plan="business"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #01A7E1;
            /* Business */
        }

        .shadepro-pricing-item[data-plan="ultimate"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #793BF0;
            /* Ultimate */
        }

        /* Plan Name */
        .plan-name {
            padding: 20px 16px 12px;
            font-size: 22px;
            font-weight: 600;
            color: #374151;
            text-align: center;
        }

        /* Price Section */
        .price-section {
            padding: 0 16px 12px;
            text-align: center;
        }

        .price-amount {
            font-size: 42px;
            font-weight: 700;
            color: #111827;
            line-height: 1;
            margin-bottom: 4px;
        }

        .currency {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-right: 2px;
        }

        /* Original price (crossed out) */
        .original-price {
            font-size: 18px;
            color: #9ca3af;
            text-decoration: line-through;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .price-label {
            font-size: 16px;
            color: #6b7280;
            font-weight: 500;
        }

        /* Description */
        .plan-description {
            padding: 0 16px 16px;
            font-size: 16px;
            color: black;
            line-height: 1.4;
            text-align: center;
            font-weight: 700;
        }

        /* Features Section */
        .features-section {
            padding: 0 16px 20px !important;
            flex-grow: 1;
            display: flex !important;
            justify-content: center !important;
            align-items: flex-start !important;
            width: 100% !important;
        }

        .features-wrapper {
            display: flex !important;
            justify-content: center !important;
            width: 100% !important;
        }

        .features-list {
            list-style: none !important;
            padding: 0 !important;
            margin: 0 auto !important;
            text-align: left !important;
            width: 280px !important;
            display: block !important;
        }

        .features-list li {
            display: flex;
            align-items: center;
            padding: 6px 0;
            font-size: 16px;
            line-height: 1.3;
        }

        .features-list i {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
            font-size: 10px;
        }

        .feature-included i {
            background: #10b981;
            color: white;
        }

        .feature-excluded i {
            background: #ef4444;
            color: white;
        }

        .feature-included {
            color: #374151;
            font-weight: 500;
        }

        .feature-excluded {
            color: #9ca3af;
            font-weight: 400;
        }

        /* Action Section */
        .action-section {
            padding: 0 16px 24px;
            margin-top: auto;
        }

        .get-plan-btn {
            width: 100%;
            background: #5045F1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .get-plan-btn:hover {
            transform: translateY(-2px);
            background: #3E36C0;
        }

        .get-plan-btn:active {
            transform: translateY(0);
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .shadepro-pricing-item-wrap {
                flex: 0 0 calc(31% - 12px);
                max-width: calc(31% - 12px);
                margin: 6px;
            }
        }

        @media (max-width: 900px) {
            .shadepro-pricing-item-wrap {
                flex: 0 0 calc(48% - 12px);
                max-width: calc(48% - 12px);
                margin: 6px;
            }

            .plan-name {
                font-size: 20px;
                padding: 16px 12px 10px;
            }

            .price-amount {
                font-size: 36px;
            }

            .currency {
                font-size: 18px;
            }

            .original-price {
                font-size: 16px;
            }
        }

        @media (max-width: 768px) {

            .shadepro-pricing-item-wrap {
                flex: 0 0 100%;
                max-width: 100%;
                margin: 12px 0;
            }

            .shadepro-pricing-item {
                margin: 0 auto;
                max-width: 350px;
            }

            .plan-name {
                font-size: 20px;
                padding: 20px 20px 12px;
            }

            .price-section {
                padding: 0 20px 12px;
            }

            .price-amount {
                font-size: 36px;
            }

            .currency {
                font-size: 18px;
            }

            .original-price {
                font-size: 14px;
            }

            .plan-description {
                padding: 0 20px 16px;
                font-size: 16px;
            }

            .features-section {
                padding: 0 20px 20px !important;
                display: flex !important;
                justify-content: center !important;
                align-items: flex-start !important;
            }

            .features-wrapper {
                display: flex !important;
                justify-content: center !important;
                width: 100% !important;
            }

            .features-list {
                width: 250px !important;
                margin: 0 auto !important;
                text-align: left !important;
            }

            .features-list li {
                font-size: 16px;
                padding: 6px 0;
            }

            .features-list i {
                width: 14px;
                height: 14px;
                font-size: 9px;
                margin-right: 10px;
            }

            .action-section {
                padding: 0 20px 24px;
            }

            .get-plan-btn {
                padding: 10px 20px;
                font-size: 16px;
                border-radius: 20px;
            }
        }

        /* Responsive container padding */
        @media (max-width: 1200px) {
            .pricing-container {
                padding-left: 50px !important;
                padding-right: 50px !important;
            }
        }

        @media (max-width: 992px) {
            .pricing-container {
                padding-left: 30px !important;
                padding-right: 30px !important;

            }
        }

        @media (max-width: 768px) {
            .pricing-container {
                padding-left: 15px !important;
                padding-right: 15px !important;
            }

            .pt-30,
            .py-30 {
                /* padding-top: 9.6875rem !important; */
                margin-top: -100px !important;
            }
        }
        </style>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Reset countdown whenever the success modal is shown
            $('#cartSuccessModal').on('show.bs.modal', function() {
                // Reset the countdown text to initial value
                var countdownElement = document.getElementById('countdown');
                var initialValue = countdownElement.getAttribute('data-initial-value') || '3';
                countdownElement.textContent = initialValue;
            });
            const tabs = document.querySelectorAll('.shadepro-pricing-tab a');
            const items = document.querySelectorAll('.shadepro-pricing-item-wrap');

            tabs.forEach(tab => {
                tab.addEventListener('click', function(event) {
                    event.preventDefault();
                    const category = this.dataset.category;

                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to the clicked tab
                    this.classList.add('active');

                    // Show/hide items based on the selected category
                    items.forEach(item => {
                        if (category === 'all' || item.dataset.category === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });

            // Initially show only the 'starter' category
            document.querySelector('.first-tabs-title').click();
        });
        </script>





        <!-- ////////////////////////////////////////////////// -->
        <!-- Pricing section  -->

        <!-- section 2- -->
        <br>
        <br>

        <div class="inner-banner support-severity-banner"
            style="background: linear-gradient(to top,  #473BF0, #6754E2); color: white; padding: 60px 0;">
            <div class="container">
                <div class="row justify-content-center" style="padding: 50px 0;">
                    <!-- First Section: Support Severity Definitions -->
                    <div class="col-12 mb-5">
                        <div class="text-center mb-5">
                            <h2 style="color: white !important; font-size: 32px; margin-bottom: 20px;">Support Severity
                                Definitions</h2>
                            <p style="color: white !important; font-size: 18px;">To ensure efficient service delivery,
                                your support request will be categorized into one of four tiers and handled accordingly.
                            </p>
                        </div>
                    </div>

                    <div class="col-lg-10 col-md-11 mb-5 d-flex justify-content-center">
                        <div class="table-responsive bg-white p-3 rounded-10" style="max-width: 800px;">
                            <table class="table table-striped table-bordered">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th scope="col" width="15%">Priority</th>
                                        <th scope="col" width="40%">Definition</th>
                                        <th scope="col" width="45%">Response Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold">1</td>
                                        <td>Critical</td>
                                        <td>4 Business hours</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">2</td>
                                        <td>Important</td>
                                        <td>8 Business hours</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">3</td>
                                        <td>Normal</td>
                                        <td>1 Business days</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                    <br>
                    <br>
                    <!-- Second Section: Priority Help Desk -->
                    <div class="col-xl-10 col-lg-11 mt-5 mb-5" style="margin-top: 100px !important;">
                        <div class="px-md-12 text-center mb-5">
                            <h2 class="title gr-text-4 mb-4" style="color: white !important;">Priority Help Desk</h2>
                            <p class="gr-text-8 mb-0" style="color: white !important;">
                                <b>Priority Help Desk</b> offers significantly faster response times and broader support
                                coverage, including both PC and network-related issues. It's specifically designed for
                                clients operating mission-critical systems that demand high availability and rapid
                                resolution.
                            </p>
                            </p>
                        </div>
                    </div>

                    <div class="col-lg-10 col-md-11 mb-5 d-flex justify-content-center">
                        <div class="table-responsive bg-white p-3 rounded-10" style="max-width: 800px;">
                            <table class="table table-striped table-bordered">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th scope="col" width="15%">Priority</th>
                                        <th scope="col" width="40%">Definition</th>
                                        <th scope="col" width="45%">Response Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold">1</td>
                                        <td>Critical</td>
                                        <td>1 Business hours</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">2</td>
                                        <td>Important</td>
                                        <td>2 Business hours</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">3</td>
                                        <td>Normal</td>
                                        <td>4 Business hours</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
        /* Responsive styles for support-severity-banner */
        .support-severity-banner {
            padding: 60px 0;
        }

        .support-severity-banner .table {
            margin-bottom: 0 !important;
        }

        .support-severity-banner .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        .support-severity-banner .bg-primary {
            background-color: #473BF0 !important;
        }

        .support-severity-banner .rounded-10 {
            border-radius: 10px !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .support-severity-banner {
                padding: 40px 0;
            }

            .support-severity-banner .table-responsive {
                max-width: 100% !important;
            }

            .support-severity-banner h2 {
                font-size: 24px !important;
            }

            .support-severity-banner p {
                font-size: 14px !important;
            }
        }

        .support-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .support-list li {
            font-size: 1rem;
        }

        @media (min-width: 992px) {

            .pt-lg-34,
            .py-lg-34 {
                margin-top: -170px !important;
            }
        }

        @media (max-width: 992px) {
            .support-severity-banner {
                padding: 30px 0;
            }

            .support-title {
                font-size: 1rem;
            }

            .support-list li {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
            .support-severity-banner {
                padding: 20px 0;
            }

            .support-title {
                font-size: 0.9rem;
            }

            .support-list li {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 576px) {
            .support-severity-banner {
                padding: 15px 0;
            }

            .support-title {
                font-size: 0.8rem;
            }

            .support-list li {
                font-size: 0.7rem;
            }
        }
        </style>


        <!-- Footer section -->
        <?php
        $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
        $include_path = $is_localhost ? '/helloit' : '';
        include($_SERVER['DOCUMENT_ROOT'] . $include_path . '/header-footer/footer.php');
        ?>
    </div>

    <!-- Vendor Scripts -->
    <?php
    $asset_path = $is_localhost ? '/helloit' : '';
    ?>
    <script src="<?php echo $asset_path; ?>/js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="<?php echo $asset_path; ?>/plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/aos/aos.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/slick/slick.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/date-picker/js/gijgo.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="<?php echo $asset_path; ?>/plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="<?php echo $asset_path; ?>/js/custom.js"></script>
    <!--  -->
    <script>
    function handleBuyNow(event, packageId) {

        event.preventDefault(); // Prevent default action

        var packagesize = packageId; // This will be 'packageS' or 'packageXS'
        var dataValueActiveElement = document.getElementById('pricing-dynamic-deck');
        var tickettype = '';

        if (dataValueActiveElement.getAttribute('data-value-active') == 'monthly') {
            tickettype = 'STARTER';
        } else {
            tickettype = 'BUSINESS';
        }

        // Send request to server to create a Stripe Checkout session
        fetch('<?php echo $asset_path; ?>/functions/create-checkout-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    packagesize: packagesize,
                    tickettype: tickettype
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log(data); // Log response from PHP script
                // Redirect to Stripe Checkout session
                redirectToCheckout(data.sessionId);
            })
            .catch(error => {
                console.error('Error:', error);
            });

    }

    function redirectToCheckout(sessionId) {
        var stripe = Stripe(
            'pk_test_51ROUofEJRyUMDOj5NvEAEvLKglZbOVz4KWqBL10RJRKW5Jd81mZyxhTcGCfE8rCUqdXlpQC9mmBHWSatmdKlRuPc00gG5RvQXn'
        );
        stripe.redirectToCheckout({
            sessionId: sessionId
        }).then(function(result) {
            if (result.error) {
                console.error(result.error.message);
            }
        });
    }
    // Handle any errors returned from Checkout
    const handleResult = function(result) {
        if (result.error) {
            showMessage(result.error.message);
        }

        setLoading(false);
    };

    // Show a spinner on payment processing
    function setLoading(isLoading) {
        if (isLoading) {
            // Disable the button and show a spinner
            payBtn.disabled = true;
            document.querySelector("#spinner").classList.remove("hidden");
            document.querySelector("#buttonText").classList.add("hidden");
        } else {
            // Enable the button and hide spinner
            payBtn.disabled = false;
            document.querySelector("#spinner").classList.add("hidden");
            document.querySelector("#buttonText").classList.remove("hidden");
        }
    }

    // Display message
    function showMessage(messageText) {
        const messageContainer = document.querySelector("#paymentResponse");

        messageContainer.classList.remove("hidden");
        messageContainer.textContent = messageText;

        setTimeout(function() {
            messageContainer.classList.add("hidden");
            messageContainer.textContent = "";
        }, 5000);
    }
    </script>
    <!-- Bootstrap and jQuery for modal functionality -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

<?php
// Close database connection at the end
if (isset($conn)) {
    $conn->close();
}
?>

</html>