<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>faq</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
</head>
<style>
.pt-lg-24,
.py-lg-24 {

    /* padding-top: 6.875rem !important; */
    margin-top: -50px !important;
}
</style>

<body data-theme="light" class="theme-light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/newnavtest.php'); ?>
        <!-- Hero Area -->
        <div class="newsletter-section bg-default-4 py-13 py-lg-24 border-bottom" style="margin-top: 80px;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-10 col-lg-8">
                        <div class="section-title text-center mb-12">
                            <h2 class="title gr-text-4 mb-6">Frequently Asked Questions</h2>
                            <p class="gr-text-8 mb-0">Find answers to common questions about our support desk and remote
                                IT services.</p>
                        </div>
                    </div>
                </div>
                <!-- FAQ Accordion -->
                <div class="row justify-content-center">
                    <div class="col-12 col-md-10 col-lg-8" data-aos="fade-left" data-aos-duration="1200"
                        data-aos-once="true">
                        <div class="accordion rounded-10 bg-white shadow-sm " id="accordionExample">
                            <?php
                            $faqs = [
                                [
                                    "question" => "What is HelloIT and how does it Work?",
                                    "answer" => "HelloIT is an online IT support desk created to provide fast help desk and remote support for fixing computer, applications, and network issues. It allows users to submit tickets and get instant responses at an affordable cost."
                                ],
                                [
                                    "question" => "How do I get started on HelloIT?",
                                    "answer" => "You must first subscribe to a <a href='#'>plan</a>. Upon successful payment, you can log in to your account and submit a ticket through the customer portal."
                                ],
                                [
                                    "question" => "What devices and platforms are being supported?",
                                    "answer" => "We support desktops and laptops on both Windows and Mac operating systems. Servers and mobile devices are not supported in the plan."
                                ],
                                [
                                    "question" => "What type of issues does HelloIT cover?",
                                    "answer" => "<ul>
                                <li>Troubleshooting & Repairs</li>
                                <li>Upgrades</li>
                                <li>Software Installation</li>
                                <li>New PC Set-up</li>
                                <li>Networking</li>
                                <li>Cybersecurity</li>
                                <li>Malware and Virus Protection</li>
                                <li>Data Backup & Recovery</li>
                                <li>IT Consultancy and Advice</li>
                            </ul>"
                                ],
                                [
                                    "question" => "How do I submit a ticket on HelloIT?",
                                    "answer" => "Login to the customer portal with your username and password. On the dashboard left navigation bar, click on “Open a Support Ticket” to submit a ticket."
                                ],
                                [
                                    "question" => "How long will it take before HelloIT responds to my ticket?",
                                    "answer" => "We will respond to your ticket as soon as possible. Each submitted ticket will be tagged with a priority and processed accordingly. Please refer to “Support Severity Definitions” on our <a href='#'>pricing page</a> for information about our response time."
                                ],
                                [
                                    "question" => "I forgot my password associated with my account.",
                                    "answer" => "Go to the account <a href='#'>login page</a>, and click on the “Forgot Password?” link. Enter your username and click “Send Reset Link”. An email with a password reset link will be sent to your account to reset your password."
                                ],
                                [
                                    "question" => "How can I cancel my order?",
                                    "answer" => "You may cancel your order within 30 days of your initial purchase. Simply submit your request through our online form, and we will refund the outstanding balance to your account. Cancellation will not be accepted after 30 days of purchase."
                                ]
                            ];

                            foreach ($faqs as $index => $faq) {
                                $isFirst = $index === 0 ? "show" : "";
                                $expanded = $index === 0 ? "true" : "false";
                                echo "
                    <div class='border-bottom overflow-hidden'>
                        <div class='mb-0' id='heading$index'>
                        <button class='btn-reset gr-text-7 font-weight-bold text-left text-blackish-blue px-0 py-8 accordion-trigger arrow-icon w-100' type='button' data-toggle='collapse' data-target='#collapse$index' aria-expanded='$expanded' aria-controls='collapse$index'>
                            {$faq['question']}
                        </button>
                        </div>
                        <div id='collapse$index' class='collapse $isFirst' aria-labelledby='heading$index' data-parent='#accordionExample'>
                        <div class='gr-color-blackish-blue-opacity-7 mt-n3 gr-text-9 pb-8 pr-8'>
                            {$faq['answer']}
                        </div>
                        </div>
                    </div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="form-bottom text-center pt-10">

                    <p class="gr-text-11 gr-text-color-opacity mb-0">Haven’t got your answer?
                        <a
                            href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'support-ticket/contact' : '../support-ticket/contact'; ?>">Contact
                            our support now</a>
                    </p>
                </div>
            </div>
        </div>
        <!-- Footer Section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>