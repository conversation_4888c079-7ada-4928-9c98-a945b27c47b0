<?php
require '../vendor/autoload.php';
include('../functions/server.php');
require_once '../config/api-config.php'; // For Appika API
require_once '../config/environment-config.php'; // Smart environment detection

// SMART ENVIRONMENT CONFIGURATION: Automatically detect localhost vs production
$stripe_config = EnvironmentConfig::getStripeConfig();
$environment = EnvironmentConfig::getEnvironment();

// Set Stripe API key based on environment
\Stripe\Stripe::setApiKey($stripe_config['api_key']);
$endpoint_secret = $stripe_config['webhook_secret'];

// Log environment detection for debugging
EnvironmentConfig::logEnvironmentInfo(__DIR__ . '/webhook.log');

// Add robust webhook logging for debugging
file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Webhook file loaded (Environment: $environment)" . PHP_EOL, FILE_APPEND);

/**
 * Enforce 2-card limit: remove oldest payment method if user has more than 2
 */
function enforcePaymentMethodLimit($stripe_customer_id) {
    try {
        // Get all payment methods for this customer
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        // If user has more than 2 payment methods, remove the oldest ones
        if (count($payment_methods->data) > 2) {
            // Sort by created timestamp (oldest first)
            $methods_array = $payment_methods->data;
            usort($methods_array, function($a, $b) {
                return $a->created - $b->created;
            });

            // Remove oldest methods until we have exactly 2
            $methods_to_remove = count($methods_array) - 2;
            for ($i = 0; $i < $methods_to_remove; $i++) {
                $oldest_method = $methods_array[$i];
                $oldest_method->detach();
                file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Enforced 2-card limit: Removed oldest payment method {$oldest_method->id} for customer {$stripe_customer_id}" . PHP_EOL, FILE_APPEND);
            }
        }
    } catch (\Exception $e) {
        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Error enforcing payment method limit: " . $e->getMessage() . PHP_EOL, FILE_APPEND);
    }
}

/**
 * Enforce 2-card limit in database: remove oldest payment method if user has more than 2
 */
function enforceDatabasePaymentMethodLimit($user_id) {
    global $conn;

    try {
        // Get count of payment methods for this user
        $count_query = "SELECT COUNT(*) as count FROM payment_methods WHERE user_id = ?";
        $stmt = $conn->prepare($count_query);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $count_result = $stmt->get_result();
        $count_row = $count_result->fetch_assoc();
        $payment_method_count = $count_row['count'];

        // If user has more than 2 payment methods, remove the oldest ones
        if ($payment_method_count > 2) {
            // Get oldest payment methods (excluding default)
            $oldest_query = "SELECT id, payment_method_id FROM payment_methods
                           WHERE user_id = ? AND is_default = 0
                           ORDER BY created_at ASC
                           LIMIT ?";
            $methods_to_remove = $payment_method_count - 2;
            $stmt = $conn->prepare($oldest_query);
            $stmt->bind_param("ii", $user_id, $methods_to_remove);
            $stmt->execute();
            $oldest_result = $stmt->get_result();

            // Remove oldest payment methods
            while ($method = $oldest_result->fetch_assoc()) {
                $method_id = $method['id'];
                $payment_method_id = $method['payment_method_id'];

                // Delete from database
                $delete_query = "DELETE FROM payment_methods WHERE id = ? AND user_id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->bind_param("ii", $method_id, $user_id);
                $delete_stmt->execute();

                file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Enforced 2-card database limit: Removed oldest payment method {$payment_method_id} (DB ID: {$method_id}) for user {$user_id}" . PHP_EOL, FILE_APPEND);
            }
        }
    } catch (\Exception $e) {
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error enforcing database payment method limit: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    }
}

$payload = @file_get_contents('php://input');
file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payload: ' . $payload . PHP_EOL, FILE_APPEND);
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
$event = null;

function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false,
    ]);
    $fullPath = empty($path) ? $apiPath : $path;
    try {
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Appika API error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

function addLocationToAppika($customerDbId, $locationData) {
    $apiConfig = getCustomerApiConfig();
    $apiEndpoint = $apiConfig['endpoint'];
    $apiPath = $apiConfig['path'];
    $apiKey = $apiConfig['key'];
    $locationPath = $apiPath . '/' . $customerDbId . '/locations';
    return sendToAppikaAPI($locationData, 'POST', $locationPath);
}

try {
    $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Event parsed: ' . $event->type . PHP_EOL, FILE_APPEND);
} catch(Exception $e) {
    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Webhook error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    http_response_code(400); exit();
}

if ($event->type == 'checkout.session.completed') {
    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Processing checkout.session.completed' . PHP_EOL, FILE_APPEND);
    $session = $event->data->object;

    // LOCALHOST PROTECTION: Disable webhook processing on localhost completely
    if ($environment === 'localhost') {
        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Localhost environment detected, skipping webhook processing (will be handled by payment-success.php)" . PHP_EOL, FILE_APPEND);
        http_response_code(200);
        echo "OK - Localhost webhook disabled";
        exit();
    }
    $customer_details = $session->customer_details;
    $email = $customer_details->email ?? '';
    $full_name = $customer_details->name ?? '';
    $address = $customer_details->address ?? null;
    $session_id = $session->id;
    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Session ID: $session_id, Email: $email, Name: $full_name" . PHP_EOL, FILE_APPEND);

    // Check if user already exists
    $check = $conn->prepare("SELECT id, username FROM user WHERE email = ?");
    $check->bind_param("s", $email);
    $check->execute();
    $result = $check->get_result();
    $user_exists = false;
    if ($result->num_rows > 0) {
        $existing_user = $result->fetch_assoc();
        $user_id = $existing_user['id'];
        $username = $existing_user['username'];
        $user_exists = true;
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - User already exists with email: ' . $email . ', user_id: ' . $user_id . PHP_EOL, FILE_APPEND);
    }

    if (!$user_exists) {
        // Generate username & password
        $username = 'user' . rand(10000,99999);
        $password = bin2hex(random_bytes(4));
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $appika_id = uniqid('appika_');

        // Parse address fields
        $address1 = $address->line1 ?? '';
        $address2_raw = $address->line2 ?? '';
        $city = $address->city ?? '';
        $state = $address->state ?? '';
        $postal_code = $address->postal_code ?? '';
        $country = $address->country ?? '';

        // Parse address2 to extract suburb/district if it's appended with comma
        $address2 = $address2_raw;
        $district = '';

        if (!empty($address2_raw) && strpos($address2_raw, ',') !== false) {
            $parts = explode(',', $address2_raw);
            if (count($parts) >= 2) {
                $address2 = trim($parts[0]); // Clean address2 (remove suburb)
                $district = trim($parts[1]); // Extract suburb as district
                file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Parsed address2 '$address2_raw' into address2='$address2' and district='$district'" . PHP_EOL, FILE_APPEND);
            }
        }

        // Fallback: try to get district from Stripe fields if parsing didn't work
        if (empty($district)) {
            $district = $address->district ?? ($address->suburb ?? '');
        }
        $first_name = $full_name;
        $last_name = '';
        $registration_time = date('Y-m-d H:i:s');

        // Create Stripe customer first
        $stripe_customer_id = null;
        try {
            $customer = \Stripe\Customer::create([
                'email' => $email,
                'name' => $full_name,
                'address' => [
                    'line1' => $address1,
                    'line2' => $address2,
                    'city' => $city,
                    'state' => $state,
                    'postal_code' => $postal_code,
                    'country' => $country,
                ],
                'metadata' => [
                    'source' => 'guest_purchase',
                    'username' => $username
                ]
            ]);
            $stripe_customer_id = $customer->id;
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Stripe customer created: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);

            // Retrieve and attach the payment method used in this checkout session
            try {
                // Get the payment intent from the session
                $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
                if ($payment_intent && $payment_intent->payment_method) {
                    $payment_method_id = $payment_intent->payment_method;
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Found payment method: ' . $payment_method_id . PHP_EOL, FILE_APPEND);

                    // Retrieve the payment method to check if it's already attached
                    $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

                    // Only attach if not already attached to a customer
                    if (!$payment_method->customer) {
                        $payment_method->attach(['customer' => $stripe_customer_id]);
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method attached to customer: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);

                        // Enforce 2-card limit after attaching new payment method
                        enforcePaymentMethodLimit($stripe_customer_id);
                    } else {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method already attached to customer: ' . $payment_method->customer . PHP_EOL, FILE_APPEND);
                    }
                } else {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - No payment method found in payment intent' . PHP_EOL, FILE_APPEND);
                }
            } catch(Exception $e) {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error attaching payment method: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                // Continue even if payment method attachment fails
            }

        } catch(Exception $e) {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error creating Stripe customer: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
            // Continue without Stripe customer ID if creation fails
        }

        // Insert user into user table (like old sign-up-db.php)
        try {
            // Auto-generate timezone from country code
            $user_timezone = autoGenerateUserTimezone($country);

            $sql = "INSERT INTO user (username, email, password, address, address2, district, city, state, country, postal_code, registration_time, first_name, last_name, stripe_customer_id, timezone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssssssssssss", $username, $email, $hashed_password, $address1, $address2, $district, $city, $state, $country, $postal_code, $registration_time, $first_name, $last_name, $stripe_customer_id, $user_timezone);
            $stmt->execute();
            $user_id = $conn->insert_id;
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - User saved to user table, id: ' . $user_id . ', country: ' . $country . ', timezone: ' . $user_timezone . ', stripe_customer_id: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);
        } catch(Exception $e) {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - DB error (user): ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }
    } else {
        // For existing users, DO NOT generate new password or change their existing password
        // Just use a placeholder for payment_temp (not used for existing users anyway)
        $password = 'existing_user_placeholder';

        // Check if existing user has stripe_customer_id, if not create one
        $stmt = $conn->prepare("SELECT stripe_customer_id FROM user WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $stmt->bind_result($existing_stripe_customer_id);
        $stmt->fetch();
        $stmt->close();

        if (!$existing_stripe_customer_id) {
            // Only create Stripe customer if user doesn't have one AND the session doesn't have a customer
            // This prevents duplicate customer creation when user already has a Stripe customer ID from checkout
            if (!$session->customer) {
                try {
                    $customer = \Stripe\Customer::create([
                        'email' => $email,
                        'name' => $full_name,
                        'address' => [
                            'line1' => $address->line1 ?? '',
                            'line2' => $address->line2 ?? '',
                            'city' => $address->city ?? '',
                            'state' => $address->state ?? '',
                            'postal_code' => $address->postal_code ?? '',
                            'country' => $address->country ?? '',
                        ],
                        'metadata' => [
                            'source' => 'existing_user_purchase',
                            'user_id' => $user_id
                        ]
                    ]);
                    $stripe_customer_id = $customer->id;

                    // Update user with stripe_customer_id
                    $update_stmt = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
                    $update_stmt->bind_param("si", $stripe_customer_id, $user_id);
                    $update_stmt->execute();
                    $update_stmt->close();

                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Stripe customer created for existing user: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);

                    // Attach payment method for existing user too
                    try {
                        $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
                        if ($payment_intent && $payment_intent->payment_method) {
                            $payment_method_id = $payment_intent->payment_method;
                            $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

                            if (!$payment_method->customer) {
                                $payment_method->attach(['customer' => $stripe_customer_id]);
                                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method attached to existing user customer: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);

                                // Enforce 2-card limit after attaching new payment method
                                enforcePaymentMethodLimit($stripe_customer_id);
                            }
                        }
                    } catch(Exception $e) {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error attaching payment method for existing user: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                    }

                } catch(Exception $e) {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error creating Stripe customer for existing user: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
                }
            } else {
                // Session already has a customer, update user with that customer ID
                $session_customer_id = $session->customer;
                $update_stmt = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
                $update_stmt->bind_param("si", $session_customer_id, $user_id);
                $update_stmt->execute();
                $update_stmt->close();
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Updated existing user with session customer ID: ' . $session_customer_id . PHP_EOL, FILE_APPEND);
            }
        } else {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Existing user already has Stripe customer ID: ' . $existing_stripe_customer_id . PHP_EOL, FILE_APPEND);

            // Even if user has existing customer ID, try to attach the payment method from this session
            try {
                $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
                if ($payment_intent && $payment_intent->payment_method) {
                    $payment_method_id = $payment_intent->payment_method;
                    $payment_method = \Stripe\PaymentMethod::retrieve($payment_method_id);

                    if (!$payment_method->customer) {
                        $payment_method->attach(['customer' => $existing_stripe_customer_id]);
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method attached to existing customer: ' . $existing_stripe_customer_id . PHP_EOL, FILE_APPEND);

                        // Enforce 2-card limit after attaching new payment method
                        enforcePaymentMethodLimit($existing_stripe_customer_id);
                    }
                }
            } catch(Exception $e) {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error attaching payment method to existing customer: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
            }
        }
    }

    // Prepare and send to Appika API (only for new users)
    if (!$user_exists) {
        $customerName = $first_name . ' ' . $last_name;
        $customerNo = 'HI' . $user_id;
        $customerStartDate = date('Y-m-d', strtotime($registration_time));
        $customerGroup = '10';
        $ofcId = '511';
        $assignTo = '1';
        $creator = '1';
        $status = 'a';
        $customerData = [
            'no' => $customerNo,
            'name' => $customerName,
            'entity_type' => '1',
            'grp_id' => $customerGroup,
            'ofc_id' => $ofcId,
            'assign2' => $assignTo,
            'creator' => $creator,
            'start_date' => $customerStartDate,
            'status' => $status
        ];
        $apiResult = sendToAppikaAPI($customerData);
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Appika API result: ' . json_encode($apiResult) . PHP_EOL, FILE_APPEND);

        // If Appika returns an ID, update appika_id in user table and add location
        if ($apiResult['success'] && isset($apiResult['data'][0]['id'])) {
            $appikaCustomerId = $apiResult['data'][0]['id'];
            $appika_id = $customerNo;
            $update_sql = "UPDATE user SET appika_id = ? WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("si", $appika_id, $user_id);
            $stmt->execute();
            // Prepare location data
            $locationData = [
                'loc_code' => 'LOC-' . $customerNo,
                'loc_name' => $customerName . ' Location',
                'add1' => $address1,
                'add2' => $address2,
                'ccode' => $country,
                'state_code' => $state,
                'city' => $city,
                'status' => $status,
                'is_primary_loc' => 'y',
                'zip' => $postal_code,
                'parent_id' => 0
            ];
            $locationResult = addLocationToAppika($appikaCustomerId, $locationData);
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Appika location result: ' . json_encode($locationResult) . PHP_EOL, FILE_APPEND);
        }
    }

    // Save for success page - IMPROVED: Store the same password that can be used for sign-in
    try {
        // For new users: save the raw password that matches the hash
        // For existing users: save the raw password (they can use it for this session)
        $stmt2 = $conn->prepare("INSERT INTO payment_temp (session_id, username, email, password, user_created) VALUES (?, ?, ?, ?, ?)");
        $user_created = !$user_exists ? 1 : 0;
        $stmt2->bind_param("ssssi", $session->id, $username, $email, $password, $user_created);
        $stmt2->execute();
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - payment_temp saved with password: ' . $password . ', user_created: ' . $user_created . PHP_EOL, FILE_APPEND);
    } catch(Exception $e) {
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - DB error (payment_temp): ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    }

    // After user is created and $user_id is available, add ticket assignment and purchase history logic
    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Starting ticket processing for user_id: ' . $user_id . PHP_EOL, FILE_APPEND);
    try {
        // 1. Get purchased items from session metadata
        $purchased_items = [];
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Session metadata: ' . json_encode($session->metadata) . PHP_EOL, FILE_APPEND);

        // Try database cart session first (most reliable for unlimited items)
        $metadata = $session->metadata;
        if (!empty($metadata['cart_session_id'])) {
            // Primary method: Get cart data from database
            $cart_session_id = $metadata['cart_session_id'];
            $cart_query = "SELECT cart_data FROM cart_sessions WHERE session_id = ?";
            $cart_stmt = $conn->prepare($cart_query);
            $cart_stmt->bind_param("s", $cart_session_id);
            $cart_stmt->execute();
            $cart_result = $cart_stmt->get_result();

            if ($cart_result->num_rows > 0) {
                $cart_row = $cart_result->fetch_assoc();
                $purchased_items = json_decode($cart_row['cart_data'], true);
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Database cart data: ' . json_encode($purchased_items) . PHP_EOL, FILE_APPEND);

                // Mark cart session as processed with UTC timestamp
                $processed_at_utc = getCurrentUTC(); // Get UTC time for consistent storage
                $update_cart_query = "UPDATE cart_sessions SET processed_at = ? WHERE session_id = ?";
                $update_cart_stmt = $conn->prepare($update_cart_query);
                $update_cart_stmt->bind_param("ss", $processed_at_utc, $cart_session_id);
                $update_cart_stmt->execute();
            }
        } elseif (!empty($metadata['cart'])) {
            // Fallback method: Parse cart JSON data from metadata
            $purchased_items = json_decode($metadata['cart'], true);
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Cart JSON data: ' . json_encode($purchased_items) . PHP_EOL, FILE_APPEND);
        } elseif (isset($metadata['total_items']) && $metadata['total_items'] > 0) {
            // Backup method: Parse individual item metadata (limited to 8 items)
            for ($i = 0; $i < min($metadata['total_items'], 8); $i++) {
                if (isset($metadata["item_{$i}_type"])) {
                    $purchased_items[] = [
                        'ticket_type' => $metadata["item_{$i}_type"],
                        'package_size' => $metadata["item_{$i}_size"] ?? '',
                        'numbers_per_package' => 1, // Will be fetched from database
                        'dollar_price_per_package' => $metadata["item_{$i}_price"] ?? 0,
                        'quantity' => $metadata["item_{$i}_qty"] ?? 1
                    ];
                }
            }
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Individual metadata items: ' . json_encode($purchased_items) . PHP_EOL, FILE_APPEND);
        } else {
            // Last resort: get from line items
            $line_items = \Stripe\Checkout\Session::allLineItems($session->id, ['limit' => 100]);
            foreach ($line_items->data as $item) {
                $product_data = $item->price->product->metadata ?? [];
                $purchased_items[] = [
                    'ticket_type' => $product_data['ticket_type'] ?? 'STARTER',
                    'package_size' => $product_data['packagesize'] ?? 'XS',
                    'numbers_per_package' => 1,
                    'dollar_price_per_package' => $item->price->unit_amount / 100,
                    'quantity' => $item->quantity
                ];
            }
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Line items data: ' . json_encode($purchased_items) . PHP_EOL, FILE_APPEND);
        }

        if (empty($purchased_items)) {
            throw new Exception('No purchased items found in session');
        }

        // Begin transaction
        $conn->begin_transaction();

        // 2. For each purchased item, update tickets and save purchase history
        foreach ($purchased_items as $item) {
            $ticket_type = $item['ticket_type'];
            $package_size = $item['package_size'];
            $dollar_price_per_package = $item['dollar_price_per_package'];
            $quantity = $item['quantity'];

            // Get numbers_per_package from database if not in metadata
            $numbers_per_package = $item['numbers_per_package'] ?? 1;
            if ($numbers_per_package == 1 && !empty($package_size)) {
                // Try to get from database
                $ticket_query = $conn->prepare("SELECT numbers_per_package FROM tickets WHERE ticket_type = ? AND package_size = ? LIMIT 1");
                $ticket_query->bind_param("ss", $ticket_type, $package_size);
                $ticket_query->execute();
                $ticket_query->bind_result($db_numbers_per_package);
                if ($ticket_query->fetch()) {
                    $numbers_per_package = $db_numbers_per_package;
                }
                $ticket_query->close();
            }

            $total_tickets = $numbers_per_package * $quantity;

            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Processing item: Type=$ticket_type, Size=$package_size, Quantity=$quantity" . PHP_EOL, FILE_APPEND);

            // Update user's ticket count
            $column = '';
            if (stripos($ticket_type, 'starter') !== false) $column = 'starter_tickets';
            elseif (stripos($ticket_type, 'premium') !== false || stripos($ticket_type, 'business') !== false) $column = 'premium_tickets';
            elseif (stripos($ticket_type, 'ultimate') !== false) $column = 'ultimate_tickets';
            else $column = 'premium_tickets'; // default

            $update = $conn->prepare("UPDATE user SET $column = $column + ? WHERE id = ?");
            $update->bind_param("ii", $total_tickets, $user_id);
            if (!$update->execute()) {
                throw new Exception("Failed to update ticket count: " . $update->error);
            }
            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Updated $column for user $user_id with $total_tickets tickets" . PHP_EOL, FILE_APPEND);

            // Insert into purchasetickets with UTC timestamp
            $purchase_time_utc = getCurrentUTC(); // Get UTC time for consistent storage
            $insert = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssiiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $purchase_time_utc, $session_id, $total_tickets);
            if (!$insert->execute()) {
                throw new Exception("Failed to insert purchase history: " . $insert->error);
            }
            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Inserted purchase history for user $username" . PHP_EOL, FILE_APPEND);
        }

        // Commit transaction
        $conn->commit();
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Successfully processed all tickets and purchase history' . PHP_EOL, FILE_APPEND);

        // Send receipt email after successful purchase
        try {
            include_once(__DIR__ . '/../functions/send-purchase-receipt.php');
            $receiptSent = sendPurchaseReceiptEmail($session_id, $email);

            if ($receiptSent) {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Receipt email sent successfully to $email for session $session_id" . PHP_EOL, FILE_APPEND);
            } else {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Failed to send receipt email to $email for session $session_id" . PHP_EOL, FILE_APPEND);
            }
        } catch (Exception $receipt_e) {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Error sending receipt email: " . $receipt_e->getMessage() . PHP_EOL, FILE_APPEND);
        }

        // IMPROVEMENT: Save payment method if user opted to save it
        try {
            // Check if user wanted to save payment method (from metadata or default to true for guests)
            $save_payment_method = true; // Default for guest users
            if (isset($metadata['save_payment_method'])) {
                $save_payment_method = $metadata['save_payment_method'] === 'true' || $metadata['save_payment_method'] === '1';
            }

            if ($save_payment_method) {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Attempting to save payment method for user_id: ' . $user_id . PHP_EOL, FILE_APPEND);

                // Get the payment intent and payment method
                $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
                if ($payment_intent && $payment_intent->payment_method) {
                    $payment_method = \Stripe\PaymentMethod::retrieve($payment_intent->payment_method);

                    // Update user's stripe_customer_id if not set
                    if (empty($stripe_customer_id)) {
                        $stripe_customer_id = $session->customer;
                        $update_customer_stmt = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
                        $update_customer_stmt->bind_param("si", $stripe_customer_id, $user_id);
                        $update_customer_stmt->execute();
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Updated stripe_customer_id for user: ' . $user_id . PHP_EOL, FILE_APPEND);
                    }

                    // Attach payment method to customer if not already attached
                    try {
                        $payment_method->attach(['customer' => $stripe_customer_id]);
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method attached to customer: ' . $stripe_customer_id . PHP_EOL, FILE_APPEND);
                    } catch (\Stripe\Exception\InvalidRequestException $e) {
                        // Payment method might already be attached
                        if (strpos($e->getMessage(), 'already been attached') !== false) {
                            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method already attached to customer' . PHP_EOL, FILE_APPEND);
                        } else {
                            throw $e;
                        }
                    }

                    // Save to database ONLY for guest users (non-logged-in purchases)
                    // Logged-in users should only have payment methods in Stripe, not database
                    if (!$user_exists) {
                        // This is a guest purchase - save to database for account creation
                        $payment_method_id = $payment_method->id;
                        $card = $payment_method->card;
                        $card_brand = $card->brand;
                        $card_last4 = $card->last4;
                        $card_exp_month = $card->exp_month;
                        $card_exp_year = $card->exp_year;

                        // Check if payment method already exists
                        $check_stmt = $conn->prepare("SELECT id FROM payment_methods WHERE user_id = ? AND payment_method_id = ?");
                        $check_stmt->bind_param("is", $user_id, $payment_method_id);
                        $check_stmt->execute();
                        $check_result = $check_stmt->get_result();

                        if ($check_result->num_rows == 0) {
                            // Check if this is the user's first payment method (make it default)
                            $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM payment_methods WHERE user_id = ?");
                            $count_stmt->bind_param("i", $user_id);
                            $count_stmt->execute();
                            $count_result = $count_stmt->get_result();
                            $count_row = $count_result->fetch_assoc();
                            $is_default = ($count_row['count'] == 0) ? 1 : 0;

                            // Insert new payment method with UTC timestamp
                            $created_at_utc = getCurrentUTC(); // Get UTC time for consistent storage
                            $insert_pm_stmt = $conn->prepare("INSERT INTO payment_methods (user_id, payment_method_id, card_brand, card_last4, card_exp_month, card_exp_year, is_default, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                            $insert_pm_stmt->bind_param("isssiiis", $user_id, $payment_method_id, $card_brand, $card_last4, $card_exp_month, $card_exp_year, $is_default, $created_at_utc);

                            if ($insert_pm_stmt->execute()) {
                                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method saved to database for guest user: ' . $user_id . PHP_EOL, FILE_APPEND);

                                // Enforce 2-card limit in database after adding new payment method
                                enforceDatabasePaymentMethodLimit($user_id);
                            } else {
                                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error saving payment method to database: ' . $insert_pm_stmt->error . PHP_EOL, FILE_APPEND);
                            }
                        } else {
                            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Payment method already exists in database' . PHP_EOL, FILE_APPEND);
                        }
                    } else {
                        // This is a logged-in user purchase - payment method is already saved to Stripe, skip database
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Skipping database save for logged-in user (Stripe-only): ' . $user_id . PHP_EOL, FILE_APPEND);
                    }
                } else {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - No payment method found in payment intent' . PHP_EOL, FILE_APPEND);
                }
            } else {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - User opted not to save payment method' . PHP_EOL, FILE_APPEND);
            }
        } catch (Exception $e) {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error saving payment method: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }

        // IMPROVEMENT: Clean up payment_temp, cart, and cart_sessions after successful purchase processing
        try {
            if (!$user_exists) {
                // For new users (guest purchases), delete payment_temp data since user account is now created
                $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
                $cleanup_stmt->bind_param("s", $session->id);
                $cleanup_stmt->execute();
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - payment_temp data deleted for session: ' . $session->id . ' (guest user account created)' . PHP_EOL, FILE_APPEND);
            } else {
                // For existing users (logged-in purchases), delete payment_temp data and clear cart
                $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ? OR username = ?");
                $cleanup_stmt->bind_param("ss", $session->id, $username);
                $cleanup_stmt->execute();
                $deleted_rows = $cleanup_stmt->affected_rows;
                if ($deleted_rows > 0) {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - payment_temp data deleted for logged-in user: $username (session: {$session->id}, rows: $deleted_rows)" . PHP_EOL, FILE_APPEND);
                } else {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - No payment_temp data found to delete for user: $username" . PHP_EOL, FILE_APPEND);
                }

                // Clear the user's active cart after successful purchase
                try {
                    // First, delete all cart items for active carts
                    $clear_items_stmt = $conn->prepare("DELETE ci FROM cart_items ci INNER JOIN cart c ON ci.cart_id = c.cart_id WHERE c.user_id = ? AND c.status = 'active'");
                    $clear_items_stmt->bind_param("i", $user_id);
                    $clear_items_stmt->execute();
                    $cleared_items = $clear_items_stmt->affected_rows;

                    // Then, update cart status to 'completed' or delete the cart entirely
                    $clear_cart_stmt = $conn->prepare("UPDATE cart SET status = 'completed' WHERE user_id = ? AND status = 'active'");
                    $clear_cart_stmt->bind_param("i", $user_id);
                    $clear_cart_stmt->execute();
                    $cleared_carts = $clear_cart_stmt->affected_rows;

                    if ($cleared_items > 0 || $cleared_carts > 0) {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Cart cleared for logged-in user: $username (items: $cleared_items, carts: $cleared_carts)" . PHP_EOL, FILE_APPEND);
                    } else {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - No active cart found to clear for user: $username" . PHP_EOL, FILE_APPEND);
                    }
                } catch (Exception $cart_e) {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error clearing cart: ' . $cart_e->getMessage() . PHP_EOL, FILE_APPEND);
                }
            }

            // Clean up cart_sessions data for both logged-in and guest users to save storage space
            try {
                $deleted_sessions = 0;

                // First, try to clean up the specific cart session used for this purchase
                if (!empty($metadata['cart_session_id'])) {
                    // We have the exact cart_session_id from metadata - delete it directly
                    $cart_session_id = $metadata['cart_session_id'];
                    $cleanup_specific_query = "DELETE FROM cart_sessions WHERE session_id = ?";
                    $cleanup_specific_stmt = $conn->prepare($cleanup_specific_query);
                    $cleanup_specific_stmt->bind_param("s", $cart_session_id);
                    $cleanup_specific_stmt->execute();
                    $deleted_specific = $cleanup_specific_stmt->affected_rows;
                    $deleted_sessions += $deleted_specific;

                    if ($deleted_specific > 0) {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Deleted specific cart session: $cart_session_id" . PHP_EOL, FILE_APPEND);
                    } else {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Cart session not found for deletion: $cart_session_id" . PHP_EOL, FILE_APPEND);
                    }
                } else {
                    // Fallback: If no cart_session_id in metadata, try to find and clean up recent unprocessed sessions
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - No cart_session_id in metadata, trying fallback cleanup" . PHP_EOL, FILE_APPEND);

                    if ($user_exists && $user_id) {
                        // For logged-in users: clean up recent unprocessed sessions for this user
                        $fallback_query = "DELETE FROM cart_sessions WHERE user_id = ? AND processed_at IS NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
                        $fallback_stmt = $conn->prepare($fallback_query);
                        $fallback_stmt->bind_param("i", $user_id);
                        $fallback_stmt->execute();
                        $deleted_fallback = $fallback_stmt->affected_rows;
                        $deleted_sessions += $deleted_fallback;

                        if ($deleted_fallback > 0) {
                            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Fallback: Deleted $deleted_fallback recent unprocessed cart sessions for user_id: $user_id" . PHP_EOL, FILE_APPEND);
                        }
                    } else {
                        // For guest users: clean up recent unprocessed sessions without user_id
                        $fallback_query = "DELETE FROM cart_sessions WHERE user_id IS NULL AND processed_at IS NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
                        $fallback_stmt = $conn->prepare($fallback_query);
                        $fallback_stmt->execute();
                        $deleted_fallback = $fallback_stmt->affected_rows;
                        $deleted_sessions += $deleted_fallback;

                        if ($deleted_fallback > 0) {
                            file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Fallback: Deleted $deleted_fallback recent guest cart sessions" . PHP_EOL, FILE_APPEND);
                        }
                    }
                }

                // For logged-in users, also clean up any other processed sessions
                if ($user_exists && $user_id) {
                    $cleanup_user_query = "DELETE FROM cart_sessions WHERE user_id = ? AND processed_at IS NOT NULL";
                    $cleanup_user_stmt = $conn->prepare($cleanup_user_query);
                    $cleanup_user_stmt->bind_param("i", $user_id);
                    $cleanup_user_stmt->execute();
                    $deleted_user_sessions = $cleanup_user_stmt->affected_rows;
                    $deleted_sessions += $deleted_user_sessions;

                    if ($deleted_user_sessions > 0) {
                        file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Deleted $deleted_user_sessions additional processed cart sessions for user_id: $user_id" . PHP_EOL, FILE_APPEND);
                    }
                }

                if ($deleted_sessions > 0) {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Cart sessions cleanup: Total deleted $deleted_sessions cart sessions" . PHP_EOL, FILE_APPEND);
                } else {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Cart sessions cleanup: No cart sessions found to delete" . PHP_EOL, FILE_APPEND);
                }

                // Also clean up old unprocessed cart sessions (older than 24 hours) to prevent storage waste
                $cleanup_old_sessions_query = "DELETE FROM cart_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND processed_at IS NULL";
                $cleanup_old_stmt = $conn->prepare($cleanup_old_sessions_query);
                $cleanup_old_stmt->execute();
                $deleted_old_sessions = $cleanup_old_stmt->affected_rows;

                if ($deleted_old_sessions > 0) {
                    file_put_contents(__DIR__ . '/webhook.log', date('c') . " - Cart sessions cleanup: Deleted $deleted_old_sessions old unprocessed cart sessions" . PHP_EOL, FILE_APPEND);
                }
            } catch (Exception $cart_sessions_e) {
                file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error cleaning up cart sessions: ' . $cart_sessions_e->getMessage() . PHP_EOL, FILE_APPEND);
            }

        } catch (Exception $e) {
            file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error in cleanup process: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }

    } catch (Exception $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Error processing tickets: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    }

    file_put_contents(__DIR__ . '/webhook.log', date('c') . ' - Webhook processed for session: ' . $session->id . PHP_EOL, FILE_APPEND);
}

http_response_code(200);