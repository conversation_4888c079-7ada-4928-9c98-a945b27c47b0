<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database connection for notification counts
include_once('../functions/server.php');

// Debug: Get current page for active menu detection
$current_page = basename($_SERVER['PHP_SELF']);
// Uncomment the line below for debugging active menu issues
// echo "<!-- Current page: $current_page -->";

// Get notification counts and user email
$user_id = isset($_SESSION['user_id']) ? intval($_SESSION['user_id']) : 0;
$unread_admin_messages = 0;
$user_email = '';

if ($user_id > 0) {
    // Count unread messages from admin
    $unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'admin' AND is_read = 0 AND ticket_id IN (SELECT id FROM support_tickets WHERE user_id = $user_id)";
    $unread_messages_result = mysqli_query($conn, $unread_messages_query);
    if ($unread_messages_result) {
        $unread_admin_messages = mysqli_fetch_assoc($unread_messages_result)['count'];
    }

    // Get user email
    $email_query = "SELECT email FROM user WHERE id = ?";
    $email_stmt = $conn->prepare($email_query);
    $email_stmt->bind_param("i", $user_id);
    $email_stmt->execute();
    $email_result = $email_stmt->get_result();
    if ($email_row = $email_result->fetch_assoc()) {
        $user_email = $email_row['email'];
    }
    $email_stmt->close();
}
?>

<div class="dashboard-menu">
    <h5 class="menu-title">Account Menu <span class="mobile-menu-toggle"><i class="fa fa-chevron-down"></i></span></h5>
    <ul class="list-unstyled menu-items">
        <?php if (!empty($user_email)): ?>
            <li class="user-email-display">
            <span class="user-email-text">
               Welcome user: <?php echo htmlspecialchars($user_email); ?>
            </span>
            </li>
        <?php endif; ?>
        <li class="<?= (basename($_SERVER['PHP_SELF']) == 'my-ticket.php' || basename($_SERVER['PHP_SELF']) == 'ticket-detail.php') ? 'active' : '' ?>">
            <a href="my-ticket"><i class="fas fa-ticket-alt" aria-hidden="true"></i> Tickets</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'my-ticket-log.php' ? 'active' : '' ?>">
            <a href="my-ticket-log"><i class="fa fa-clipboard-list"></i> Ticket Logs</a>
        </li>
        <li class="<?= (basename($_SERVER['PHP_SELF']) == 'my-ratings.php' || basename($_SERVER['PHP_SELF']) == 'rate-ticket.php') ? 'active' : '' ?>">
            <a href="my-ratings"><i class="fas fa-star"></i> My Ratings</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'purchase-history.php' ? 'active' : '' ?>">
            <a href="purchase-history"><i class="fa fa-shopping-basket"></i> Purchase History</a>
        </li>
        <!-- <li><a href="downloads"><i class="fa fa-download"></i> Downloads</a></li> -->
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : '' ?>">
            <a href="profile"><i class="fa fa-user"></i> Account Details</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'payment-methods.php' ? 'active' : '' ?>">
            <a href="payment-methods"><i class="fa fa-credit-card"></i> Payment Methods</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'create-ticket.php' ? 'active' : '' ?>">
            <a href="create-ticket"><i class="fa fa-headset"></i> Open a Support Ticket</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'chat-support.php' ? 'active' : '' ?>">
            <a href="chat-support">
                <i class="fa fa-comments"></i> Live Chat Support
                <?php if ($unread_admin_messages > 0): ?>
                <span id="user-chat-badge"
                    class="notification-badge"><?php echo $unread_admin_messages >= 10 ? '9+' : $unread_admin_messages; ?></span>
                <?php endif; ?>
            </a>
        </li>

        <li>
            <hr>
        </li>


        <li><a href="#" id="logout-link"><i class="fa fa-sign-out-alt"></i> Log Out</a></li>
    </ul>
</div>

<!-- Logout Confirmation Modal -->
<div class="modal fade" id="logoutConfirmModal" tabindex="-1" role="dialog" aria-labelledby="logoutConfirmModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white" id="logoutConfirmModalLabel">Confirm Logout</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="logout-modal-close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to log out?</p>
            </div>
            <div class="modal-footer justify-content-center">
                <a href="../index?logout=1" class="btn btn-primary">Yes, Log Out</a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="logout-cancel-btn">Cancel</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard Menu */
.dashboard-menu {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
    border: 1px solid #e0e6ed !important;
    box-sizing: border-box !important;
    height: auto !important;
    margin-right: 30px !important;
}

.dashboard-menu .menu-title {
    font-size: 22px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    margin-bottom: 10px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e0e6ed !important;
}

.mobile-menu-toggle {
    display: none !important;
    float: right !important;
    cursor: pointer !important;
    transition: transform 0.3s ease !important;
}

.mobile-menu-toggle.active {
    transform: rotate(90deg) !important;
}

.mobile-menu-toggle i {
    transition: transform 0.3s ease !important;
    display: inline-block !important;
}

.mobile-menu-toggle.active i {
    transform: rotate(180deg) !important;
}

.dashboard-menu ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.dashboard-menu ul li {
    margin-bottom: 8px !important;
}

.dashboard-menu ul li a {
    text-decoration: none !important;
    color: #555 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    transition: background-color 0.3s ease, color 0.3s ease !important;
    font-size: 14px !important;
}

.dashboard-menu ul li a i {
    margin-right: 8px !important;
    font-size: 14px !important;
    color: #777 !important;
}

.dashboard-menu ul li a:hover {
    background-color: #f0f2f5 !important;
    color: #473BF0 !important;
}

.dashboard-menu ul li a:hover i {
    color: #473BF0 !important;
}

.dashboard-menu ul li.active a {
    background-color: #473BF0 !important;
    color: #fff !important;
    font-weight: bold !important;
}

.dashboard-menu ul li.active a i {
    color: #fff !important;
}

/* Mobile styles */
@media (max-width: 767px) {
    .dashboard-menu {
        margin-right: 0 !important;
        margin-bottom: 15px !important;
    }

    .mobile-menu-toggle {
        display: block !important;
    }

    .dashboard-menu .menu-title {
        cursor: pointer !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .dashboard-menu ul.menu-items {
        max-height: 0 !important;
        overflow: hidden !important;
        transition: max-height 0.5s ease !important;
    }

    .dashboard-menu ul.menu-items.expanded {
        max-height: 500px !important;
    }
}

/* Logout Modal Styles */
#logoutConfirmModal .modal-header {
    background-color: #473BF0 !important;
    color: white !important;
}

#logoutConfirmModal .modal-title {
    font-size: 22px !important;
    color: white !important;
    font-weight: 500 !important;
}

#logoutConfirmModal .modal-body {
    padding: 20px !important;
    font-size: 16px !important;
}

#logoutConfirmModal .modal-footer .btn-primary {
    background-color: #473BF0 !important;
    border-color: #473BF0 !important;
}

#logoutConfirmModal .modal-footer .btn-secondary {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

#logoutConfirmModal .modal-footer {
    display: flex !important;
    justify-content: center !important;
    padding: 1rem !important;
}

#logoutConfirmModal .modal-footer .btn {
    min-width: 120px !important;
    margin: 0 10px !important;
}

@media (max-width: 767px) {
    #logoutConfirmModal .modal-body {
        font-size: 14px !important;
    }

    #logoutConfirmModal .modal-footer .btn {
        min-width: 100px !important;
        margin: 0 5px !important;
        font-size: 14px !important;
    }
}

/* Notification Badge Styles */
.notification-badge {
    background-color: #dc3545 !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 2px 6px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    margin-left: 5px !important;
    min-width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    position: relative !important;
    top: -1px !important;
}

.dashboard-menu ul li.active a .notification-badge {
    background-color: #fff !important;
    color: #473BF0 !important;
}

/* User Email Display Styles */
.dashboard-menu ul li.user-email-display {
    margin-bottom: 8px !important;
}

.dashboard-menu ul li.user-email-display .user-email-text {
    display: flex !important;
    align-items: center !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    font-size: 13px !important;
    color: #666 !important;
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    font-weight: 500 !important;
    word-break: break-all !important;
}

.dashboard-menu ul li.user-email-display .user-email-text i {
    margin-right: 8px !important;
    font-size: 13px !important;
    color: #888 !important;
    flex-shrink: 0 !important;
}

@media (max-width: 767px) {
    .dashboard-menu ul li.user-email-display .user-email-text {
        font-size: 12px !important;
        padding: 5px 8px !important;
    }

    .dashboard-menu ul li.user-email-display .user-email-text i {
        font-size: 12px !important;
        margin-right: 6px !important;
    }
}
</style>

<script>
// Enhanced mobile menu script - works across all pages
document.addEventListener('DOMContentLoaded', function() {
    initializeUserMenus();
});

// Run immediately as well
(function() {
    initializeUserMenus();
    // Run again after short delays to catch any late DOM changes
    setTimeout(initializeUserMenus, 100);
    setTimeout(initializeUserMenus, 500);
})();

// Global function that can be called from any page
function initializeUserMenus() {
    console.log('Initializing all user menus');

    // Find ALL dashboard menus on the page (there might be multiple)
    var allMenus = document.querySelectorAll('.dashboard-menu');

    if (allMenus.length === 0) {
        console.log('No dashboard menus found on page');
        return;
    }

    console.log('Found ' + allMenus.length + ' dashboard menus');

    // Process each menu separately
    allMenus.forEach(function(menu, index) {
        var menuTitle = menu.querySelector('.menu-title');
        var menuToggle = menu.querySelector('.mobile-menu-toggle');
        var menuItems = menu.querySelector('.menu-items');

        if (!menuTitle || !menuToggle || !menuItems) {
            console.log('Menu #' + index + ' is missing components');
            return;
        }

        // Create a unique toggle function for this menu
        function toggleThisMenu(e) {
            console.log('Toggle clicked for menu #' + index);
            e.preventDefault();
            e.stopPropagation();
            menuItems.classList.toggle('expanded');
            menuToggle.classList.toggle('active');
        }

        // Remove any existing click handlers to prevent duplicates
        menuTitle.onclick = null;

        // Add the new click handler
        menuTitle.onclick = toggleThisMenu;

        // Also add direct click handler to the toggle icon for better mobile experience
        menuToggle.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            menuItems.classList.toggle('expanded');
            menuToggle.classList.toggle('active');
        };

        // Auto-expand if there's an active item (only on mobile)
        var hasActiveItem = menuItems.querySelector('.active');
        if (hasActiveItem && window.innerWidth <= 767) {
            console.log('Auto-expanding menu #' + index + ' with active item');
            menuItems.classList.add('expanded');
            menuToggle.classList.add('active');
        }

        console.log('Menu #' + index + ' initialized');
    });

    // Initialize logout confirmation
    initializeLogoutConfirmation();

    // Initialize notification system
    initializeUserNotificationSystem();
}

// Function to initialize logout confirmation
function initializeLogoutConfirmation() {
    var logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
        logoutLink.addEventListener('click', function(e) {
            e.preventDefault();
            $('#logoutConfirmModal').modal('show');
        });
    }

    // Add explicit event handlers for cancel buttons
    $(document).ready(function() {
        // Handle Cancel button click
        $('#logout-cancel-btn').on('click', function(e) {
            e.preventDefault();
            $('#logoutConfirmModal').modal('hide');
            console.log('Cancel button clicked - modal should close');
        });

        // Handle X button click
        $('#logout-modal-close').on('click', function(e) {
            e.preventDefault();
            $('#logoutConfirmModal').modal('hide');
            console.log('X button clicked - modal should close');
        });

        // Handle clicking outside modal to close
        $('#logoutConfirmModal').on('click', function(e) {
            if (e.target === this) {
                $('#logoutConfirmModal').modal('hide');
                console.log('Clicked outside modal - modal should close');
            }
        });

        // Handle Escape key to close modal
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('#logoutConfirmModal').hasClass('show')) {
                $('#logoutConfirmModal').modal('hide');
                console.log('Escape key pressed - modal should close');
            }
        });

        // Additional fallback: use vanilla JavaScript for cancel button
        document.getElementById('logout-cancel-btn')?.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('logoutConfirmModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();
            console.log('Vanilla JS cancel clicked - modal should close');
        });

        // Additional fallback: use vanilla JavaScript for X button
        document.getElementById('logout-modal-close')?.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('logoutConfirmModal').style.display = 'none';
            document.body.classList.remove('modal-open');
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();
            console.log('Vanilla JS X clicked - modal should close');
        });
    });
}

// Function to initialize user notification system
function initializeUserNotificationSystem() {
    // Update notification counts every 3 seconds
    setInterval(updateUserChatBadge, 3000);
    // Call immediately to get initial state
    updateUserChatBadge();
}

// --- User chat notification badge polling ---
function updateUserChatBadge() {
    fetch('get-user-notifications', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('User notifications response:', data);
            if (data.success) {
                updateNotificationBadge('chat-support', data.unread_admin_messages);
            }
        })
        .catch(error => console.error('Error updating user notifications:', error));
}

// Function to update individual notification badge
function updateNotificationBadge(page, count) {
    // Find the menu item for this page
    const menuItems = document.querySelectorAll('.dashboard-menu .menu-items li');

    menuItems.forEach(item => {
        const link = item.querySelector('a');
        if (link && link.getAttribute('href') === page) {
            // Remove existing badge
            const existingBadge = link.querySelector('.notification-badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // Add new badge if count > 0
            if (count > 0) {
                const badge = document.createElement('span');
                badge.id = 'user-chat-badge';
                badge.className = 'notification-badge';
                badge.textContent = count >= 10 ? '9+' : count;
                link.appendChild(badge);
                console.log(`User: Updated badge for ${page} with count ${count}`);
            } else {
                console.log(`User: Removed badge for ${page} (count: ${count})`);
            }
        }
    });
}

// Note: Notification polling is now handled by initializeUserNotificationSystem()

// Make function available globally
window.initializeUserMenus = initializeUserMenus;
</script>