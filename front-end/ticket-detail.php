<?php
include('../functions/server.php');
include('../functions/timezone-helper.php');
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

$username = $_SESSION['username'];

// รับ ticket ID จาก URL
$ticketId = isset($_GET['id']) ? (int) $_GET['id'] : 0;

if ($ticketId <= 0) {
    echo "Invalid ticket ID.";
    exit();
}

// ดึง user_id จาก username
$userQuery = "SELECT id FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

// ดึงข้อมูลตั๋ว
$sql = "SELECT st.*, au.username AS admin_name
        FROM support_tickets st
        LEFT JOIN admin_users au ON st.assigned_admin_id = au.id
        WHERE st.id = $ticketId AND st.user_id = $userId
        LIMIT 1";

$result = mysqli_query($conn, $sql);
$ticket = mysqli_fetch_assoc($result);

if (!$ticket) {
    echo "Ticket not found or you don’t have permission to view it.";
    exit();
}

// Check if user has rated this ticket
$ratingQuery = "SELECT * FROM ticket_ratings WHERE ticket_id = $ticketId AND user_id = $userId";
$ratingResult = mysqli_query($conn, $ratingQuery);
$userRating = mysqli_fetch_assoc($ratingResult);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Support Tickets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 200px;
        padding-bottom: 100px;
    }

    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    .badge-open {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #0d6efd;
        color: #fff;
    }

    .badge-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .badge-closed {
        background-color: #6c757d;
        color: #fff;
    }

    /* Table styles */
    .table-responsive {
        overflow-x: auto;
    }

    .table td.label {
        font-weight: bold;
        width: 30%;
        background-color: #f8f9fa;
    }

    /* Button styles */
    .ticket-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .ticket-btn {
        min-width: 180px;
        padding: 10px 20px;
        font-size: 16px;
        text-align: center;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
            padding-bottom: 80px;
        }

        .table td.label {
            width: 35%;
        }

        .ticket-btn {
            min-width: 160px;
            font-size: 15px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .table td.label {
            width: 40%;
        }

        h2 {
            font-size: 1.5rem;
        }

        .ticket-btn {
            min-width: 150px;
            font-size: 14px;
            padding: 8px 16px;
        }
    }

    @media (max-width: 575px) {

        .table td,
        .table th {
            padding: 0.5rem;
            font-size: 0.9rem;
        }

        .ticket-buttons {
            flex-direction: column;
            align-items: center;
            width: 100%;
            gap: 0px;
        }

        .ticket-btn {
            width: 80%;
            max-width: 250px;
            margin-bottom: 10px;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    .container {
        width: 1500px;
        max-width: 95%;
    }

    /* Photo Attachment Styles */
    .ticket-attachments {
        margin-top: 10px;
    }

    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 10px;
    }

    .attachment-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
        background: #f8f9fa;
    }

    .attachment-item:hover {
        transform: scale(1.05);
    }

    .attachment-thumbnail {
        width: 100%;
        height: 120px;
        object-fit: cover;
        display: block;
        transition: opacity 0.2s ease;
    }

    .attachment-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
        color: white;
        font-size: 24px;
    }

    .attachment-item:hover .attachment-overlay {
        opacity: 1;
    }

    .attachment-info {
        padding: 8px;
        text-align: center;
        background: white;
        border-top: 1px solid #eee;
    }

    .attachment-info small {
        color: #666;
        font-size: 11px;
        word-break: break-all;
    }

    /* Responsive attachment grid */
    @media (max-width: 767px) {
        .attachment-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
        }

        .attachment-thumbnail {
            height: 100px;
        }

        .attachment-info {
            padding: 6px;
        }
    }

    @media (max-width: 575px) {
        .attachment-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .attachment-thumbnail {
            height: 80px;
        }
    }

    /* Rating Display Styles */
    .rating-display .star-rating {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }

    .rating-display .star-rating i {
        font-size: 1.2rem;
        margin-right: 2px;
    }

    .rating-comment {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #ffc107;
        margin-bottom: 8px;
        font-style: italic;
    }

    @media (max-width: 767px) {
        .rating-display .star-rating i {
            font-size: 1rem;
        }
    }
    </style>


</head>

<body>

    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <h2 class="mb-4 text-center">Ticket #<?php echo $ticket['id']; ?> -
                    <?php echo htmlspecialchars($ticket['subject']); ?></h2>

                <!-- Timezone indicator -->
                <div class="text-center mb-3">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                    </small>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <td class="label">Subject</td>
                            <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Description</td>
                            <td><?php echo nl2br(htmlspecialchars($ticket['description'])); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Type</td>
                            <td><?php echo ucfirst($ticket['ticket_type']); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Status</td>
                            <td>
                                <span class="badge badge-<?php echo $ticket['status']; ?>">
                                    <?php echo ucfirst($ticket['status']); ?>
                                </span>
                            </td>
                        </tr>
<!--                        <tr>-->
<!--                            <td class="label">Priority</td>-->
<!--                            <td>--><?php //echo ucfirst($ticket['priority']); ?><!--</td>-->
<!--                        </tr>-->
<!--                        <tr>-->
<!--                            <td class="label">Severity</td>-->
<!--                            <td>--><?php //echo ucfirst($ticket['severity']); ?><!--</td>-->
<!--                        </tr>-->
                        <tr>
                            <td class="label">Created At</td>
                            <td><?php echo showCustomerTime($ticket['created_at'], 'M j, Y g:i A'); ?></td>
                        </tr>
                        <tr>
                            <td class="label">Last Updated</td>
                            <td><?php echo $ticket['updated_at'] ? showCustomerTime($ticket['updated_at'], 'M j, Y g:i A') : 'Not updated'; ?></td>
                        </tr>
                        <tr>
                            <td class="label">Assigned Admin</td>
                            <td><?php echo $ticket['admin_name'] ? $ticket['admin_name'] : 'Not assigned'; ?></td>
                        </tr>
                        <?php if (!empty($ticket['attachments'])): ?>
                        <tr>
                            <td class="label">Attachments</td>
                            <td>
                                <?php
                                $attachments = json_decode($ticket['attachments'], true);
                                if (is_array($attachments) && count($attachments) > 0):
                                ?>
                                <div class="ticket-attachments">
                                    <div class="attachment-grid">
                                        <?php foreach ($attachments as $index => $attachment): ?>
                                        <?php if (file_exists('../' . $attachment)): ?>
                                        <div class="attachment-item">
                                            <a href="../<?php echo htmlspecialchars($attachment); ?>"
                                               data-fancybox="ticket-gallery"
                                               data-caption="Attachment <?php echo $index + 1; ?>">
                                                <img src="../<?php echo htmlspecialchars($attachment); ?>"
                                                     alt="Attachment <?php echo $index + 1; ?>"
                                                     class="attachment-thumbnail">
                                                <div class="attachment-overlay">
                                                    <i class="fas fa-search-plus"></i>
                                                </div>
                                            </a>
                                            <div class="attachment-info">
                                                <small><?php echo basename($attachment); ?></small>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                    <small class="text-muted">Click on images to view full size</small>
                                </div>
                                <?php else: ?>
                                <span class="text-muted">No valid attachments found</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>

                        <!-- Rating Display -->
                        <?php if ($userRating): ?>
                        <tr>
                            <td class="label">Your Rating</td>
                            <td>
                                <div class="rating-display">
                                    <div class="star-rating mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $userRating['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                        <span class="ml-2"><?php echo $userRating['rating']; ?>/5 stars</span>
                                    </div>
                                    <?php if (!empty($userRating['comment'])): ?>
                                    <div class="rating-comment">
                                        <strong>Your feedback:</strong> <?php echo htmlspecialchars($userRating['comment']); ?>
                                    </div>
                                    <?php endif; ?>
                                    <small class="text-muted">
                                        Rated on: <?php echo showCustomerTime($userRating['created_at']); ?>
                                        <?php if ($userRating['created_at'] != $userRating['updated_at']): ?>
                                            (Updated: <?php echo showCustomerTime($userRating['updated_at']); ?>)
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>

                <div class="text-center mt-4 mb-5 pb-4">
                    <div class="ticket-buttons">
                        <a href="chat-support.php?ticket_id=<?php echo $ticket['id']; ?>"
                            class="btn btn-primary ticket-btn" style="background-color: #28a745; color: white;">Start
                            Live Chat</a>

                        <!-- Rating Button - Only show for resolved/closed tickets -->
                        <?php if (in_array($ticket['status'], ['resolved', 'closed'])): ?>
                        <a href="rate-ticket.php?id=<?php echo $ticket['id']; ?>&from=ticket-detail"
                            class="btn btn-warning ticket-btn" style="background-color: #ffc107; color: white;">
                            <i class="fas fa-star"></i> &nbsp;
                            <?php echo $userRating ? 'View Rating' : 'Rate Ticket'; ?>
                        </a>
                        <?php endif; ?>

                        <a href="my-ticket.php" class="btn btn-secondary ticket-btn"
                            style="background-color: #473bf0; color: white;">Back to My Tickets</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/vendor.min.js"></script>
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../js/customer-timezone.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize Fancybox for photo gallery
        $('[data-fancybox="ticket-gallery"]').fancybox({
            buttons: [
                "zoom",
                "slideShow",
                "fullScreen",
                "download",
                "thumbs",
                "close"
            ],
            loop: true,
            protect: true,
            animationEffect: "fade",
            transitionEffect: "slide",
            thumbs: {
                autoStart: true,
                hideOnClose: true
            },
            caption: function(instance, item) {
                return $(this).data('caption') || '';
            }
        });
    });

    // Update timezone display when detected
    document.addEventListener('customerTimezoneDetected', function(event) {
        const timezoneDisplay = document.getElementById('customer-timezone-display');
        if (timezoneDisplay) {
            timezoneDisplay.textContent = event.detail.timezone;
        }
        console.log('Customer timezone updated in ticket-detail:', event.detail.timezone);
    });

    // Also update on page load if timezone is already available
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.CustomerTimezone) {
                const timezone = window.CustomerTimezone.getCustomerTimezone();
                const timezoneDisplay = document.getElementById('customer-timezone-display');
                if (timezoneDisplay && timezone) {
                    timezoneDisplay.textContent = timezone;
                }
            }
        }, 1000);
    });
    </script>
</body>

</html>