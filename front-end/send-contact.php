<?php
session_start();

// MANUAL OVERRIDE: Force production paths for helloit.io
$base_path = '/';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ' . $base_path . 'support-ticket/contact/');
    exit;
}

// Honeypot spam protection
if (!empty($_POST['website'])) {
    // This is likely spam, redirect silently
    header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
    exit;
}

// Validate required fields
$required_fields = ['first_name', 'last_name', 'email', 'subject', 'message'];
$errors = [];

foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
    }
}

// Validate email format
if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}

// If there are validation errors, redirect back with error
if (!empty($errors)) {
    $_SESSION['contact_errors'] = $errors;
    $_SESSION['contact_data'] = $_POST;
    header('Location: ' . $base_path . 'support-ticket/contact/?status=error');
    exit;
}

// Load email configuration
$email_config = include 'email-config.php';

// Sanitize input data
$first_name = htmlspecialchars(trim($_POST['first_name']));
$last_name = htmlspecialchars(trim($_POST['last_name']));
$email = htmlspecialchars(trim($_POST['email']));
$phone = htmlspecialchars(trim($_POST['phone'] ?? ''));
$company = htmlspecialchars(trim($_POST['company'] ?? ''));
$subject = htmlspecialchars(trim($_POST['subject']));
$message = htmlspecialchars(trim($_POST['message']));

// Use PHP mail() function (simple and reliable)
$to = $email_config['to_email'];
$email_subject = $email_config['subject_prefix'] . $subject;

$email_body = "New contact form submission:\n\n";
$email_body .= "Name: $first_name $last_name\n";
$email_body .= "Email: $email\n";
$email_body .= "Phone: " . ($phone ?: 'Not provided') . "\n";
$email_body .= "Company: " . ($company ?: 'Not provided') . "\n";
$email_body .= "Subject: $subject\n\n";
$email_body .= "Message:\n$message\n\n";
$email_body .= "Submitted on: " . date('Y-m-d H:i:s') . "\n";
$email_body .= "From website: https://helloit.io/\n";
$email_body .= "IP Address: " . $_SERVER['REMOTE_ADDR'] . "\n";
$email_body .= "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "\n";

$headers = "From: $email\r\n";
$headers .= "Reply-To: $email\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
$headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

// Try to send email to admin
$email_sent = mail($to, $email_subject, $email_body, $headers);

// Send confirmation email to user
$confirmation_sent = false;
if ($email_sent) {
    $confirmation_sent = sendConfirmationEmail($email, $first_name, $last_name, $subject);
}

// If email fails, save to file as backup (for development)
if (!$email_sent) {
    $log_file = 'contact-submissions.log';
    $log_entry = "\n" . str_repeat("=", 50) . "\n";
    $log_entry .= "Date: " . date('Y-m-d H:i:s') . "\n";
    $log_entry .= "To: $to\n";
    $log_entry .= "Subject: $email_subject\n";
    $log_entry .= "Content:\n$email_body\n";
    $log_entry .= str_repeat("=", 50) . "\n";

    if (file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX)) {
        $_SESSION['contact_success'] = 'Thank you for your message! We have received it and will get back to you soon. (Note: Email server not configured - message saved locally)';
        header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
        exit;
    }
}

if ($email_sent) {
    if ($confirmation_sent) {
        $_SESSION['contact_success'] = 'Thank you for your message! We have received it and will get back to you soon. A confirmation email has been sent to your email address.';
    } else {
        $_SESSION['contact_success'] = 'Thank you for your message! We have received it and will get back to you soon.';
    }
    header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
} else {
    $_SESSION['contact_errors'] = ['Failed to send email. Please check your email configuration. For development, check contact-submissions.log file.'];
    header('Location: ' . $base_path . 'support-ticket/contact/?status=error');
}

/**
 * Send confirmation email to the user who submitted the contact form
 */
function sendConfirmationEmail($user_email, $first_name, $last_name, $original_subject) {
    $from_email = '<EMAIL>';
    $from_name = 'HelloIT Support Team';
    $subject = 'Thank you for contacting HelloIT - Message Received';

    // Create professional HTML email
    $html_body = createConfirmationEmailHTML($first_name, $last_name, $original_subject);

    // Create plain text version
    $text_body = createConfirmationEmailText($first_name, $last_name, $original_subject);

    // Email headers for HTML email
    $boundary = md5(time());
    $headers = "From: $from_name <$from_email>\r\n";
    $headers .= "Reply-To: $from_email\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: multipart/alternative; boundary=\"$boundary\"\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    // Create multipart email body
    $email_body = "--$boundary\r\n";
    $email_body .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $email_body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
    $email_body .= $text_body . "\r\n\r\n";

    $email_body .= "--$boundary\r\n";
    $email_body .= "Content-Type: text/html; charset=UTF-8\r\n";
    $email_body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
    $email_body .= $html_body . "\r\n\r\n";

    $email_body .= "--$boundary--\r\n";

    // Send the email
    return mail($user_email, $subject, $email_body, $headers);
}

/**
 * Create HTML version of confirmation email
 */
function createConfirmationEmailHTML($first_name, $last_name, $original_subject) {
    $current_year = date('Y');
    $submission_date = date('F j, Y \a\t g:i A');

    return "
<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Thank you for contacting HelloIT</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #473BF0; padding: 30px; text-align: center; }
        .logo { color: #ffffff; font-size: 28px; font-weight: bold; margin: 0; }
        .tagline { color: #ffffff; font-size: 16px; margin: 5px 0 0 0; opacity: 0.9; }
        .content { padding: 40px 30px; }
        .greeting { font-size: 24px; color: #333; margin-bottom: 20px; font-weight: 600; }
        .message { font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 25px; }
        .info-box { background-color: #f8f9fa; border-left: 4px solid #5045F1; padding: 20px; margin: 25px 0; border-radius: 0 8px 8px 0; }
        .info-title { font-weight: 600; color: #333; margin-bottom: 10px; }
        .info-text { color: #666; font-size: 14px; }
        .next-steps { background-color: #e8f4fd; padding: 25px; border-radius: 8px; margin: 25px 0; }
        .next-steps h3 { color: #1976d2; margin-top: 0; }
        .next-steps ul { margin: 15px 0; padding-left: 20px; }
        .next-steps li { margin-bottom: 8px; color: #555; }
        .contact-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; }
        .contact-row { display: flex; align-items: center; margin-bottom: 10px; }
        .contact-icon { width: 20px; margin-right: 10px; color: #5045F1; }
        .footer { background-color: #333; color: #ffffff; padding: 30px; text-align: center; }
        .footer-text { margin: 0; font-size: 14px; line-height: 1.5; }
        .social-links { margin: 20px 0; }
        .social-link { color: #5045F1; text-decoration: none; margin: 0 10px; }
        .btn { display: inline-block; background-color: #ffffff; color: #5045F1; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-weight: 600; margin: 15px 0; border: 2px solid #5045F1; }
        .btn:hover { background-color: #f8f9fa; color: #372fc7; border-color: #372fc7; }
        @media (max-width: 600px) {
            .container { width: 100% !important; }
            .header, .content, .footer { padding: 20px !important; }
            .greeting { font-size: 20px !important; }
        }
    </style>
</head>
<body>
    <div class='container'>
        <!-- Header -->
        <div class='header'>
            <img src='https://helloit.io/image/wp/HelloIT-new.png' alt='HelloIT Logo' style='height:50px;margin-bottom:15px;'>
            <h1 style='margin:0;font-size:28px;font-weight:bold;'>Thank you for contacting us!</h1>
            <p class='tagline'>We have received your message</p>
        </div>

        <!-- Main Content -->
        <div class='content'>
            <h2 class='greeting'>Hello $first_name,</h2>

            <p class='message'>
                Thank you for reaching out to HelloIT! We have successfully received your message and wanted to confirm that it's now in our system.
            </p>

            <div class='info-box'>
                <div class='info-title'>📧 Your Message Details:</div>
                <div class='info-text'>
                    <strong>Subject:</strong> $original_subject<br>
                    <strong>Submitted:</strong> $submission_date<br>
                    <strong>Reference:</strong> #CONTACT-" . date('Ymd-His') . "
                </div>
            </div>

            <div class='next-steps'>
                <h3>🚀 What happens next?</h3>
                <ul>
                    <li><strong>Review:</strong> Our support team will review your message within 24 hours</li>
                    <li><strong>Response:</strong> We'll get back to you via email with a detailed response</li>
                    <li><strong>Follow-up:</strong> If needed, we may reach out for additional information</li>
                    <li><strong>Resolution:</strong> We'll work with you to address your needs promptly</li>
                </ul>
            </div>

            <p class='message'>
                In the meantime, feel free to explore our services or check out our support resources. If you have any urgent matters, please don't hesitate to contact us directly.
            </p>

            <div style='text-align: center;'>
                <a href='https://helloit.io' class='btn'>Visit Our Website</a>
            </div>

            <div class='contact-info'>
                <h4 style='margin-top: 0; color: #333;'>📞 Need immediate assistance?</h4>
                <div class='contact-row'>
                    <span class='contact-icon'>📧</span>
                    <span>Email: <EMAIL></span>
                </div>
                <div class='contact-row'>
                    <span class='contact-icon'>🌐</span>
                    <span>Website: https://helloit.io</span>
                </div>
                <div class='contact-row'>
                    <span class='contact-icon'>⏰</span>
                    <span>Business Hours: Monday - Friday, 9:00 AM - 6:00 PM</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class='footer'>
            <p class='footer-text'>
                <strong>HelloIT - Professional IT Support Services</strong><br>
                Providing reliable, efficient, and cost-effective IT solutions.<br><br>

                This is an automated message. Please do not reply to this email.<br>
                If you need to contact us, <NAME_EMAIL>
            </p>

            <p style='font-size: 12px; color: #999; margin-top: 20px;'>
                © $current_year HelloIT. All rights reserved.<br>
                You received this email because you contacted us through our website.
            </p>
        </div>
    </div>
</body>
</html>";
}

/**
 * Create plain text version of confirmation email
 */
function createConfirmationEmailText($first_name, $last_name, $original_subject) {
    $current_year = date('Y');
    $submission_date = date('F j, Y \a\t g:i A');
    $reference = "#CONTACT-" . date('Ymd-His');

    return "
HELLOIT - PROFESSIONAL IT SUPPORT SERVICES
==========================================

Hello $first_name,

Thank you for reaching out to HelloIT! We have successfully received your message and wanted to confirm that it's now in our system.

YOUR MESSAGE DETAILS:
--------------------
Subject: $original_subject
Submitted: $submission_date
Reference: $reference

WHAT HAPPENS NEXT?
------------------
1. REVIEW: Our support team will review your message within 24 hours
2. RESPONSE: We'll get back to you via email with a detailed response
3. FOLLOW-UP: If needed, we may reach out for additional information
4. RESOLUTION: We'll work with you to address your needs promptly

In the meantime, feel free to explore our services or check out our support resources. If you have any urgent matters, please don't hesitate to contact us directly.

NEED IMMEDIATE ASSISTANCE?
-------------------------
Email: <EMAIL>
Website: https://helloit.io
Business Hours: Monday - Friday, 9:00 AM - 6:00 PM

Visit our website: https://helloit.io

==========================================
HelloIT - Professional IT Support Services
Providing reliable, efficient, and cost-effective IT solutions.

This is an automated message. Please do not reply to this email.
If you need to contact us, <NAME_EMAIL>

© $current_year HelloIT. All rights reserved.
You received this email because you contacted us through our website.
";
}
?>
