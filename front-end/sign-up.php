<?php
session_start();
include('../functions/server.php');

// Initialize variables
$username = "";
$email = "";
$tell = "";
$company_name = "";
$tax_id = "";
$address = "";
$address2 = "";
$district = "";
$city = "";
$country = "";
$postal_code = "";
$first_name = "";
$last_name = "";

// Retrieve form data from session if available
if (isset($_SESSION['form_data'])) {
    $username = $_SESSION['form_data']['username'];
    $email = $_SESSION['form_data']['email'];
    $tell = $_SESSION['form_data']['tell'];
    $company_name = $_SESSION['form_data']['company_name'];
    $tax_id = $_SESSION['form_data']['tax_id'];
    $address = $_SESSION['form_data']['address'];
    $address2 = isset($_SESSION['form_data']['address2']) ? $_SESSION['form_data']['address2'] : '';
    $district = $_SESSION['form_data']['district'];
    $city = $_SESSION['form_data']['city'];
    $country = $_SESSION['form_data']['country'];
    $postal_code = $_SESSION['form_data']['postal_code'];
    $first_name = isset($_SESSION['form_data']['first_name']) ? $_SESSION['form_data']['first_name'] : '';
    $last_name = isset($_SESSION['form_data']['last_name']) ? $_SESSION['form_data']['last_name'] : '';

    // Clear the form data from session after retrieving it
    unset($_SESSION['form_data']);
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Sign Up</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <style>
    /* Password toggle eye icon styling */
    .toggle-password {
        cursor: pointer;
        border-left: none;
    }

    .toggle-password:hover {
        color: #007bff;
    }

    .input-group-text {
        background-color: #fff;
        border-left: 0;
    }

    .input-group .form-control:focus+.input-group-append .input-group-text {
        border-color: #80bdff;
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <div class="sign-page bg-default-2">
            <!-- Header Brand Area -->
            <!-- <header class="site-header" style="background-color: #473bf0;">
        <div class="container-fluid">
          <div class="row justify-content-center">
            <div class="col-md-8 col-lg-5 col-xl-4">
              <div class="brand text-center py-9">
                <a href="../index.php">
                  <img src="../image/Hello-IT150-77.png" alt="">
                </a>
              </div>
            </div>
          </div>
        </div>
      </header> -->

            <head>
                <?php
        include '../header-footer/header.php';
        ?>
            </head>
            <br>
            <br>
            <div class="container">
                <div class="row justify-content-center py-25">
                    <div class="col-lg-8">
                        <div class="main-block">
                            <div class="form-title text-center">
                                <h2 class="title gr-text-2 mb-9">Sign Up</h2>
                                <p class="gr-text-8 mb-13">To get started, you need to sign up here.</p>
                            </div>
                            <div class="bg-white border rounded-10 px-8 py-8 shadow-1 mb-11">
                                <form id="signupForm" action="../functions/sign-up-db.php" method="post">
                                    <?php include('../functions/errors.php') ?>
                                    <?php if (isset($_SESSION['error'])) : ?>
                                    <div class="alert alert-danger gr-text-8 mb-13">
                                        <?php
                      $errorMessages = $_SESSION['error'];
                      echo implode(', ', $errorMessages);

                      // Check for specific errors
                      $usernameExists = false;
                      $emailExists = false;
                      $tellRequired = false;
                      $passwordMismatch = false;

                      foreach ($errorMessages as $error) {
                        if (strpos($error, 'Username already exists') !== false) {
                          $usernameExists = true;
                        }
                        if (strpos($error, 'Email already exists') !== false) {
                          $emailExists = true;
                        }
                        if (strpos($error, 'Tell number is required') !== false) {
                          $tellRequired = true;
                        }
                        if (strpos($error, 'two password do not match') !== false) {
                          $passwordMismatch = true;
                        }
                      }

                      // Set flags to show the appropriate modals - only one will be shown based on priority
                      // Priority: Username exists > Email exists > Password mismatch > Tell required
                      if ($usernameExists) {
                        echo '<script>var showUsernameExistsModal = true;</script>';
                      } else if ($emailExists) {
                        echo '<script>var showEmailExistsModal = true;</script>';
                      } else if ($passwordMismatch) {
                        echo '<script>var showPasswordMismatchModal = true;</script>';
                      } else if ($tellRequired) {
                        echo '<script>var showTellRequiredModal = true;</script>';
                      }

                      unset($_SESSION['error']);
                      ?>
                                    </div>
                                    <?php endif ?>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="first_name"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">First
                                                Name</label>
                                            <input class="form-control gr-text-11 border" type="text" id="first_name"
                                                name="first_name" placeholder="Enter your first name"
                                                value="<?php echo htmlspecialchars($first_name); ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="last_name"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Last Name</label>
                                            <input class="form-control gr-text-11 border" type="text" id="last_name"
                                                name="last_name" placeholder="Enter your last name"
                                                value="<?php echo htmlspecialchars($last_name); ?>" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="username"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Username</label>
                                            <input class="form-control gr-text-11 border" type="text" id="username"
                                                name="username" placeholder="Enter your username"
                                                value="<?php echo htmlspecialchars($username); ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="email"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Email</label>
                                            <input class="form-control gr-text-11 border" type="email" id="email"
                                                name="email" placeholder="Enter your email"
                                                value="<?php echo htmlspecialchars($email); ?>" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="password_1"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Password</label>
                                            <div class="input-group">
                                                <input id="password_1" name="password_1"
                                                    class="form-control gr-text-11 border" type="password"
                                                    placeholder="Enter your password" required>
                                                <div class="input-group-append">
                                                    <span class="input-group-text toggle-password" toggle="#password_1">
                                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="password_2"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Confirm
                                                Password</label>
                                            <div class="input-group">
                                                <input id="password_2" name="password_2"
                                                    class="form-control gr-text-11 border" type="password"
                                                    placeholder="Confirm your password" required>
                                                <div class="input-group-append">
                                                    <span class="input-group-text toggle-password" toggle="#password_2">
                                                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="tell"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Phone</label>
                                            <input class="form-control gr-text-11 border" type="tel" id="tell"
                                                name="tell" placeholder="Enter digits only (e.g., 66812345678)"
                                                value="<?php echo htmlspecialchars($tell); ?>" required
                                                pattern="[0-9]{7,15}" title="Please enter between 7-15 digits only">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="company_name"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Company Name
                                                (Optional)</label>
                                            <input class="form-control gr-text-11 border" type="text" id="company_name"
                                                name="company_name" placeholder="Enter your company name"
                                                value="<?php echo htmlspecialchars($company_name); ?>">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="tax_id"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Tax ID
                                                (Optional)</label>
                                            <input class="form-control gr-text-11 border" type="text" id="tax_id"
                                                name="tax_id" placeholder="Enter your tax ID"
                                                value="<?php echo htmlspecialchars($tax_id); ?>">
                                        </div>

                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-12">
                                            <label for="address"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Address</label>
                                            <textarea class="form-control gr-text-11 border" id="address" name="address"
                                                placeholder="Enter your address"
                                                required><?php echo htmlspecialchars($address); ?></textarea>
                                        </div>
                                        <div class="form-group col-md-12">
                                            <label for="address2"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Address Notes
                                                (Optional)</label>
                                            <textarea class="form-control gr-text-11 border" id="address2"
                                                name="address2"
                                                placeholder="Enter any additional notes about your address"
                                                rows="3"><?php echo htmlspecialchars($address2); ?></textarea>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="district"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">District</label>
                                            <input class="form-control gr-text-11 border" type="text" id="district"
                                                name="district" placeholder="Enter your district"
                                                value="<?php echo htmlspecialchars($district); ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="city"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">City</label>
                                            <input class="form-control gr-text-11 border" type="text" id="city"
                                                name="city" placeholder="Enter your city"
                                                value="<?php echo htmlspecialchars($city); ?>" required>
                                        </div>

                                        <div class="form-group col-md-6">
                                            <label for="state"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">State/Province</label>
                                            <input class="form-control gr-text-11 border" type="text" id="state"
                                                name="state" placeholder="Enter your state or province"
                                                value="<?php echo htmlspecialchars($state ?? ''); ?>">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="country"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Country</label>
                                            <input class="form-control gr-text-11 border" type="text" id="country"
                                                name="country" placeholder="Enter your country"
                                                value="<?php echo htmlspecialchars($country); ?>" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="postal_code"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Postal
                                                Code</label>
                                            <input class="form-control gr-text-11 border" type="text" id="postal_code"
                                                name="postal_code" placeholder="Enter your postal code"
                                                value="<?php echo htmlspecialchars($postal_code); ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <!-- Empty div for layout balance -->
                                        </div>
                                    </div>
                                    <div class="form-group button-block mb-2">
                                        <button type="button" id="createAccountBtn"
                                            class="form-btn btn btn-primary gr-hover-y w-100">Create an account</button>
                                    </div>
                                    <input type="hidden" name="reg_user" value="1">
                                </form>
                            </div>
                            <div class="form-bottom excerpt text-center">
                                <p class="sign-up-text gr-text-9 gr-text-color" style="color: black !important;">Already
                                    have an account? <br> <a href="../front-end/sign-in.php" class="text-primary">Sign
                                        in now</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- footer-->
        <?php
    include '../header-footer/footer.php';
    ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <!-- Signup Confirmation Modal -->
    <div class="modal fade" id="signupConfirmModal" tabindex="-1" role="dialog"
        aria-labelledby="signupConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white" id="signupConfirmModalLabel" style="font-size: 22px;">Confirm
                        Registration</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close"
                        id="closeConfirmModal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-question-circle text-primary" style="font-size: 60px;"></i>
                    </div>
                    <h4>Are you sure?</h4>
                    <p class="mb-4">Please confirm that you want to create a new account with the provided information.
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" id="confirmSignupBtn">Yes, Create Account</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Username Exists Modal -->
    <div class="modal fade" id="usernameExistsModal" tabindex="-1" role="dialog"
        aria-labelledby="usernameExistsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white" id="usernameExistsModalLabel" style="font-size: 22px;">Username
                        Already Exists</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle text-danger" style="font-size: 60px;"></i>
                    </div>
                    <h4>Registration Failed</h4>
                    <p class="mb-4">The username you entered is already taken. Please choose a different username and
                        try again.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal"
                        id="usernameExistsOkBtn">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Missing Fields Modal -->
    <div class="modal fade" id="missingFieldsModal" tabindex="-1" role="dialog"
        aria-labelledby="missingFieldsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title text-white" id="missingFieldsModalLabel" style="font-size: 22px;">Missing
                        Information</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 60px;"></i>
                    </div>
                    <h4>Please Complete All Required Fields</h4>
                    <p class="mb-4">The following fields are required:</p>
                    <div id="missingFieldsList" class="text-left ml-5 mb-3">
                        <!-- Missing fields will be listed here dynamically -->
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Exists Modal -->
    <div class="modal fade" id="emailExistsModal" tabindex="-1" role="dialog" aria-labelledby="emailExistsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white" id="emailExistsModalLabel" style="font-size: 22px;">Email
                        Already Exists</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle text-danger" style="font-size: 60px;"></i>
                    </div>
                    <h4>Registration Failed</h4>
                    <p class="mb-4">The email address you entered is already registered. Please use a different email
                        address or try to log in if you already have an account.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal" id="emailExistsOkBtn">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tell Number Required Modal -->
    <div class="modal fade" id="tellRequiredModal" tabindex="-1" role="dialog" aria-labelledby="tellRequiredModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title text-white" id="tellRequiredModalLabel" style="font-size: 22px;">Phone Number
                        Required</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-phone-alt text-warning" style="font-size: 60px;"></i>
                    </div>
                    <h4>Phone Number is Required</h4>
                    <p class="mb-4">Please enter a valid phone number to continue with registration.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Mismatch Modal -->
    <div class="modal fade" id="passwordMismatchModal" tabindex="-1" role="dialog"
        aria-labelledby="passwordMismatchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title text-white" id="passwordMismatchModalLabel" style="font-size: 22px;">Password
                        Mismatch</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 60px;"></i>
                    </div>
                    <h4>Passwords Do Not Match</h4>
                    <p class="mb-4">The passwords you entered do not match. Please make sure both password fields
                        contain the same value.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        // Show error modals with priority (only show the highest priority error)
        // Priority order: Username exists > Email exists > Password mismatch > Tell required

        // Setup focus handlers for all modals
        $('#usernameExistsOkBtn, #usernameExistsModal .close').on('click', function() {
            setTimeout(function() {
                $('#username').focus().select();
            }, 500);
        });

        $('#emailExistsOkBtn, #emailExistsModal .close').on('click', function() {
            setTimeout(function() {
                $('#email').focus().select();
            }, 500);
        });

        $('#tellRequiredModal button, #tellRequiredModal .close').on('click', function() {
            setTimeout(function() {
                $('#tell').focus();
            }, 500);
        });

        $('#passwordMismatchModal button, #passwordMismatchModal .close').on('click', function() {
            setTimeout(function() {
                $('#password_1').focus();
            }, 500);
        });

        // Function to clear specific fields
        function clearField(fieldId) {
            $('#' + fieldId).val('');
        }

        // Show only one modal based on priority
        if (typeof showUsernameExistsModal !== 'undefined' && showUsernameExistsModal) {
            $('#usernameExistsModal').modal('show');
            // Clear the username field when the modal is shown
            clearField('username');
        } else if (typeof showEmailExistsModal !== 'undefined' && showEmailExistsModal) {
            $('#emailExistsModal').modal('show');
            // Clear the email field when the modal is shown
            clearField('email');
        } else if (typeof showPasswordMismatchModal !== 'undefined' && showPasswordMismatchModal) {
            $('#passwordMismatchModal').modal('show');
            // Clear both password fields when the modal is shown
            clearField('password_1');
            clearField('password_2');
        } else if (typeof showTellRequiredModal !== 'undefined' && showTellRequiredModal) {
            $('#tellRequiredModal').modal('show');
        }

        // Add international phone number formatting and validation
        $('#tell').on('input', function() {
            var phoneInput = $(this).val().replace(/\D/g, ''); // Remove non-digits

            // Format the phone number as needed
            if (phoneInput.length > 0) {
                // Allow for international format with + sign
                if (phoneInput.length <= 15) { // Maximum length for international numbers
                    $(this).val(phoneInput);
                } else {
                    $(this).val(phoneInput.substring(0, 15));
                }
            }
        });

        // Handle create account button click
        $('#createAccountBtn').on('click', function() {
            // Get all required fields
            var requiredFields = $('#signupForm').find('[required]');
            var missingFields = [];
            var fieldLabels = {
                'username': 'Username',
                'email': 'Email',
                'password_1': 'Password',
                'password_2': 'Confirm Password',
                'tell': 'Phone',
                'address': 'Address',
                'district': 'District',
                'city': 'City',
                'country': 'Country',
                'postal_code': 'Postal Code'
            };

            // Check each required field
            requiredFields.each(function() {
                if ($(this).val().trim() === '') {
                    missingFields.push(fieldLabels[$(this).attr('name')] || $(this).attr(
                        'name'));
                }
            });

            // If there are missing fields, show the missing fields modal
            if (missingFields.length > 0) {
                var missingFieldsHtml = '';
                for (var i = 0; i < missingFields.length; i++) {
                    missingFieldsHtml += '<p><i class="fas fa-times-circle text-danger mr-2"></i>' +
                        missingFields[i] + '</p>';
                }
                $('#missingFieldsList').html(missingFieldsHtml);
                $('#missingFieldsModal').modal('show');
                return;
            }

            // Check if passwords match
            var password1 = $('#password_1').val();
            var password2 = $('#password_2').val();

            if (password1 !== password2) {
                $('#passwordMismatchModal').modal('show');
                return;
            }

            // Validate phone number
            var phoneNumber = $('#tell').val().trim();
            if (phoneNumber.length < 7) { // Minimum length for most phone numbers
                $('#tellRequiredModal').modal('show');
                $('#tell').focus();
                return;
            }

            // All validations passed, show confirmation modal
            $('#signupConfirmModal').modal('show');
        });

        // Handle confirm signup button click
        $('#confirmSignupBtn').on('click', function() {
            // Hide the confirmation modal
            $('#signupConfirmModal').modal('hide');

            // Submit the form
            $('#signupForm').submit();
        });

        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            // Toggle the eye icon
            $(this).find('i').toggleClass('fa-eye-slash fa-eye');

            // Toggle the password field type
            var input = $($(this).attr('toggle'));
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
            } else {
                input.attr('type', 'password');
            }
        });

        // Add hover effect to the eye icon
        $('.toggle-password').hover(
            function() {
                $(this).css('cursor', 'pointer');
            }
        );
    });
    </script>
</body>

</html>