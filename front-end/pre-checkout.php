<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
session_start();

// Auto-detect environment for URL paths
$is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost_url ? '/helloit' : '';
$url_base = $is_localhost_url ? '/helloit' : '';

// Note: Removed automatic redirect for logged-in users
// Logged-in users should be able to access pre-checkout page directly
// and see their payment methods without being redirected to cart.php

// Get cart information for display (matching cart.php logic)
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Get user email if logged in
$user_email = '';
if ($user_id) {
    $email_query = "SELECT email FROM user WHERE id = ?";
    $email_stmt = $conn->prepare($email_query);
    $email_stmt->bind_param("i", $user_id);
    $email_stmt->execute();
    $email_result = $email_stmt->get_result();
    if ($email_row = $email_result->fetch_assoc()) {
        $user_email = $email_row['email'];
    }
    $email_stmt->close();
}

// Get cart data based on user login status
if ($user_id) {
    // For logged-in users, get cart from database
    $query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);
} else {
    // For guest users, get cart from session
    $cart_items = isset($_SESSION['guest_cart']) ? $_SESSION['guest_cart'] : [];
}

// Calculate cart total and item count
$cart_total = 0;
$cart_items_count = 0;

if (!empty($cart_items)) {
    foreach ($cart_items as $item) {
        $quantity = isset($item['quantity']) ? (int)$item['quantity'] : 0;
        $price = isset($item['dollar_price_per_package']) ? (float)$item['dollar_price_per_package'] : 0.00;
        $cart_total += $quantity * $price;
        $cart_items_count += $quantity;
    }
}

// If no items in cart, redirect to cart page
if ($cart_items_count == 0) {
    header("Location: " . $url_base . "/front-end/cart.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - HelloIT</title>
    <link rel="icon" type="image/webp" href="<?php echo $base_path; ?>/image/wp/HelloIT-new.webp">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .checkout-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }

        .checkout-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .checkout-header {
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .checkout-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .checkout-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .checkout-body {
            padding: 40px;
        }

        /* Horizontal Layout for Desktop */
        .checkout-content {
            display: flex;
            gap: 40px;
            align-items: flex-start;
        }

        .left-column {
            flex: 1;
            min-width: 0;
        }

        .right-column {
            flex: 1;
            min-width: 0;
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .order-summary h3 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .order-item:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 18px;
            color: #6754e2;
            margin-top: 10px;
            padding-top: 15px;
            border-top: 2px solid #6754e2;
        }

        .email-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            margin-bottom: 25px;
        }

        .email-section h4 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .email-section p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .email-input-group {
            display: flex;
            gap: 0;
            margin-bottom: 15px;
        }

        .email-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px 0 0 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .email-input:focus {
            outline: none;
            border-color: #6754e2;
        }

        .check-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 0 8px 8px 0;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .check-btn:hover {
            background: #0056b3;
        }

        .check-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .email-help {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 0;
        }

        .checkout-options {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .save-payment-option {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .save-payment-option input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .save-payment-option label {
            margin: 0;
            font-weight: 500;
            color: #495057;
            cursor: pointer;
        }

        .save-payment-note {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            margin-left: 28px;
        }

        .proceed-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .proceed-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #6754e2;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: color 0.3s;
        }

        .back-link:hover {
            color: #5344c9;
        }

        .back-link i {
            margin-right: 8px;
        }

        .alert {
            border-radius: 8px;
            margin-top: 15px;
        }

        /* Inline Login Section */
        .inline-login-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .login-form-container h5 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-help {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #6754e2;
            box-shadow: 0 0 0 3px rgba(103, 84, 226, 0.1);
        }

        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 5px;
        }

        .password-toggle:hover {
            color: #495057;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .cancel-btn {
            width: 100%;
            background: #6c757d;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .login-error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            border: 1px solid #f5c6cb;
        }

        /* Payment Methods Section */
        .payment-methods-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .payment-methods-section h5 {
            color: #495057;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .payment-method-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .payment-method-item:hover {
            border-color: #6754e2;
            background: #f8f7ff;
        }

        .payment-method-item.selected {
            border-color: #6754e2;
            background: #f8f7ff;
            box-shadow: 0 0 0 3px rgba(103, 84, 226, 0.1);
        }

        .payment-method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-icon {
            width: 40px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-icon img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            padding: 2px;
        }

        .card-icon i {
            /* Style for icon-only card icons (like Add New Card) */
            background: none;
            border: none;
            padding: 0;
        }

        .add-card-icon {
            position: relative;
            background: #f8f9fa !important;

        }

        .add-card-icon:hover {
            background: #e7f3ff !important;
            border-color: #5344c9 !important;
        }

        .security-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .payment-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .payment-logos img {
            height: 30px;
            width: auto;
            max-width: 60px;
            object-fit: contain;
            filter: grayscale(20%);
            transition: filter 0.3s ease;
        }

        .payment-logos img:hover {
            filter: grayscale(0%);
        }

        /* Tablet and Mobile responsive - vertical layout */
        @media (max-width: 768px) {
            .checkout-content {
                flex-direction: column;
                gap: 20px;
            }

            .checkout-container {
                max-width: 600px;
            }
        }

        /* Mobile responsive for security section */
        @media (max-width: 576px) {
            .security-section {
                padding: 15px;
                margin-top: 20px;
            }

            .payment-logos {
                gap: 10px;
            }

            .payment-logos img {
                height: 25px;
                max-width: 50px;
            }
        }

        .card-details {
            display: flex;
            flex-direction: column;
        }

        .card-brand {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .card-number {
            color: #6c757d;
            font-size: 13px;
        }

        .card-expiry {
            color: #6c757d;
            font-size: 12px;
        }

        .default-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .proceed-payment-btn {
            width: 100%;
            background: linear-gradient(135deg, #6754e2 0%, #5344c9 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: 20px;
        }

        .proceed-payment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 84, 226, 0.3);
        }

        .proceed-payment-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        @media (max-width: 768px) {
            .checkout-container {
                margin: 20px auto;
                padding: 15px;
            }

            .checkout-body {
                padding: 25px;
            }

            .email-input-group {
                flex-direction: column;
                gap: 10px;
            }

            .email-input, .check-btn {
                border-radius: 8px;
            }

            .payment-method-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .payment-method-info {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="checkout-container">
        <div class="checkout-card">
            <!-- Header -->
            <div class="checkout-header">
                <h1><img src="<?php echo $base_path; ?>/image/wp/HelloIT-new.webp" alt="HelloIT" style="height: 32px; width: auto; margin-right: 12px; vertical-align: middle;">Secure Checkout</h1>
                <p>Complete your purchase securely with HelloIT</p>
            </div>

            <!-- Body -->
            <div class="checkout-body">
                <!-- Back to Cart Link -->
                <a href="<?php echo $url_base; ?>/front-end/cart.php" class="back-link">
                    <i class="fas fa-arrow-left"></i>Back to Cart
                </a>

                <!-- Horizontal Layout Container -->
                <div class="checkout-content">
                    <!-- Left Column: Order Summary -->
                    <div class="left-column">
                        <div class="order-summary">
                            <h3><i class="fas fa-shopping-cart me-2"></i>Order Summary</h3>
                            <div class="order-item">
                                <span><?php echo $cart_items_count; ?> item(s) in your cart</span>
                                <span>$<?php echo number_format($cart_total, 2); ?></span>
                            </div>
                            <div class="order-item">
                                <span><strong>Total</strong></span>
                                <span><strong>$<?php echo number_format($cart_total, 2); ?></strong></span>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Email Verification & Payment -->
                    <div class="right-column">

                <!-- Email Validation Section -->
                <div class="email-section">
                    <h4><i class="fas fa-envelope me-2"></i>Email Verification</h4>
                    <p>We need to verify your email address to ensure a smooth checkout experience and to send you your purchase receipt.</p>
                    
                    <div class="email-input-group">
                        <input type="email" class="email-input" id="guest-email" placeholder="Enter your email address" required>
                        <button type="button" class="check-btn" id="check-email-btn">
                            <i class="fas fa-search me-1"></i>Check
                        </button>
                    </div>
                    
                    <p class="email-help">
                        <i class="fas fa-info-circle me-1"></i>
                        We'll check if you have an existing account to provide the best checkout experience.
                    </p>
                    
                    <!-- Email Check Result -->
                    <div id="email-check-result"></div>

                    <!-- Inline Login Section (shown when existing email is found) -->
                    <div class="inline-login-section" id="inline-login-section" style="display: none;">
                        <div class="login-form-container">
                            <h5><i class="fas fa-sign-in-alt me-2"></i>Sign In to Continue</h5>
                            <p class="login-help">Please enter your password to access your account and saved payment methods.</p>

                            <form id="inline-login-form">
                                <div class="form-group mb-3">
                                    <label for="login-email" class="form-label">Email Address</label>
                                    <div class="email-input-group">
                                        <input type="email" class="email-input" id="login-email" placeholder="Enter your email address" required disabled>
                                        <button type="button" class="check-btn" style="display: none;"><i class="fas fa-search me-1"></i>Check</button>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="login-password" class="form-label">Password</label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="login-password" placeholder="Enter your password" required>
                                        <button type="button" class="password-toggle" id="password-toggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <button type="submit" class="login-btn" id="login-btn">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase
                                </button>

                                <button type="button" class="cancel-btn" id="cancel-login-btn">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                            </form>

                            <div id="login-error" class="login-error" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- Payment Methods Section (shown after successful login) -->
                    <div class="payment-methods-section" id="payment-methods-section" style="display: none;">
                        <h5><i class="fas fa-credit-card me-2"></i>Select Payment Method</h5>
                        <div id="payment-methods-container"></div>

                        <!-- Save Payment Method Checkbox (only shown for new card) -->
                        <div class="save-card-option" id="save-card-option" style="display: none; margin-top: 15px;">
                            <label class="save-card-checkbox" style="display: flex; align-items: center; gap: 10px; cursor: pointer; padding: 12px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                                <input type="checkbox" name="save_payment_method" id="save_payment_method" value="1" checked style="width: 18px; height: 18px; accent-color: #6754e2;">
                                <span class="checkbox-text" style="font-size: 14px; color: #495057;">
                                    <i class="fas fa-credit-card me-1" style="color: #6754e2;"></i>
                                    Save my payment method for faster checkout
                                </span>
                            </label>

                        </div>

                        <div class="payment-actions">
                            <button type="button" class="proceed-payment-btn" id="proceed-payment-btn">
                                <i class="fas fa-lock me-2"></i>Pay Now
                            </button>
                        </div>
                        <p class="save-card-note" style="margin: 8px 0 0 28px; font-size: 12px; color: #6c757d;">
                            Your payment information will be securely stored by Stripe for future purchases.
                        </p>
                    </div>

                    <!-- Guest Checkout Options (shown for new emails) -->
                    <div class="checkout-options" id="checkout-options" style="display: none;">
                        <div class="save-payment-option">
                            <input type="checkbox" id="save-payment-checkbox" checked>
                            <label for="save-payment-checkbox">
                                <i class="fas fa-credit-card me-1"></i>
                                Save my payment method for faster checkout
                            </label>
                        </div>


                        <!-- Hidden form for submission -->
                        <form id="checkout-form" action="<?php echo $url_base; ?>/front-end/create-checkout-session.php" method="POST">
                            <input type="hidden" name="save_payment_method" id="save_payment_method_hidden" value="1">
                            <input type="hidden" name="guest_email" id="guest_email_hidden" value="">
                            <input type="hidden" name="source" value="pre_checkout">

                            <button type="submit" class="proceed-btn" id="proceed-btn">
                                <i class="fas fa-lock me-2"></i>Proceed to Secure Payment
                            </button>
                            <div class="save-payment-note">
                                Your payment information will be securely stored by Stripe for future purchases.
                            </div>
                        </form>


                    </div>
                    </div> <!-- End right-column -->
                </div> <!-- End checkout-content -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const basePath = '<?php echo $url_base; ?>';
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeEmailCheck();
            initializeSavePaymentCheckbox();
            initializeInlineLogin();
            initializePasswordToggle();
            initializePaymentMethods();
            initializeCancelButton();

            // Check if user is already logged in and load payment methods directly
            checkIfUserLoggedIn();
        });

        function initializeEmailCheck() {
            const emailInput = document.getElementById('guest-email');
            const checkEmailBtn = document.getElementById('check-email-btn');
            const emailResult = document.getElementById('email-check-result');
            const guestEmailHidden = document.getElementById('guest_email_hidden');

            // Handle email check button click
            checkEmailBtn.addEventListener('click', function() {
                const email = emailInput.value.trim();

                if (!email) {
                    showEmailResult('error', 'Please enter your email address.');
                    return;
                }

                if (!isValidEmail(email)) {
                    showEmailResult('error', 'Please enter a valid email address.');
                    return;
                }

                checkEmailBtn.disabled = true;
                checkEmailBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking...';

                // Send AJAX request to check email
                const xhr = new XMLHttpRequest();
                xhr.open('POST', basePath + '/front-end/check-email-exists.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.responseType = 'json';

                xhr.onload = function() {
                    checkEmailBtn.disabled = false;
                    checkEmailBtn.innerHTML = '<i class="fas fa-search me-1"></i>Check';

                    if (xhr.status === 200 && xhr.response) {
                        if (xhr.response.exists) {
                            // Email exists - show inline login
                            showEmailResult('success', 'Account found! Please sign in to continue.');
                            showInlineLogin(email);
                        } else {
                            // Email doesn't exist - proceed as guest
                            showEmailResult('success', 'Great! You can proceed as a new customer.');
                            guestEmailHidden.value = email;

                            // Show checkout options
                            const checkoutOptions = document.getElementById('checkout-options');
                            if (checkoutOptions) {
                                checkoutOptions.style.display = 'block';
                            }
                        }
                    } else {
                        showEmailResult('error', 'Error checking email. Please try again.');
                    }
                };

                xhr.onerror = function() {
                    checkEmailBtn.disabled = false;
                    checkEmailBtn.innerHTML = '<i class="fas fa-search me-1"></i>Check';
                    showEmailResult('error', 'Connection error. Please try again.');
                };

                xhr.send('email=' + encodeURIComponent(email));
            });

            // Allow Enter key to trigger email check
            emailInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    checkEmailBtn.click();
                }
            });
        }

        function showEmailResult(type, message) {
            const emailResult = document.getElementById('email-check-result');
            let alertClass = 'alert-info';
            let icon = 'fas fa-info-circle';

            switch (type) {
                case 'success':
                    alertClass = 'alert-success';
                    icon = 'fas fa-check-circle';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    icon = 'fas fa-exclamation-triangle';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    icon = 'fas fa-exclamation-circle';
                    break;
            }

            const resultHtml = `
                <div class="alert ${alertClass}" id="email-result-alert">
                    <i class="${icon} me-2"></i>${message}
                </div>
            `;

            emailResult.innerHTML = resultHtml;

            // Auto-hide "Account found" message after 3 seconds
            if (type === 'success' && message.includes('Account found')) {
                setTimeout(() => {
                    const alertElement = document.getElementById('email-result-alert');
                    if (alertElement) {
                        alertElement.style.transition = 'opacity 0.5s ease-out';
                        alertElement.style.opacity = '0';
                        setTimeout(() => {
                            emailResult.innerHTML = '';
                        }, 500); // Wait for fade out animation to complete
                    }
                }, 3000); // 3 seconds delay
            }
        }

        function showInlineLogin(email) {
            // Hide email input section
            const emailInput = document.getElementById('guest-email');
            const checkBtn = document.getElementById('check-email-btn');
            emailInput.disabled = true;
            checkBtn.style.display = 'none';

            // Show inline login section
            const inlineLoginSection = document.getElementById('inline-login-section');
            const loginEmailField = document.getElementById('login-email');

            loginEmailField.value = email;
            inlineLoginSection.style.display = 'block';

            // Focus on password field
            setTimeout(() => {
                document.getElementById('login-password').focus();
            }, 100);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function initializeSavePaymentCheckbox() {
            const savePaymentCheckbox = document.getElementById('save-payment-checkbox');
            const savePaymentHidden = document.getElementById('save_payment_method_hidden');

            if (savePaymentCheckbox && savePaymentHidden) {
                // Set initial value based on checkbox state
                savePaymentHidden.value = savePaymentCheckbox.checked ? '1' : '0';

                // Update hidden field when checkbox changes
                savePaymentCheckbox.addEventListener('change', function() {
                    savePaymentHidden.value = this.checked ? '1' : '0';
                    console.log('Save payment method:', this.checked ? 'Yes' : 'No');
                });
            }
        }

        function initializeInlineLogin() {
            const loginForm = document.getElementById('inline-login-form');
            if (!loginForm) return;

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;
                const loginBtn = document.getElementById('login-btn');
                const loginError = document.getElementById('login-error');

                if (!password) {
                    showLoginError('Please enter your password.');
                    return;
                }

                // Disable login button and show loading
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
                loginError.style.display = 'none';

                // Send login request using the same parameters as sign-in-db.php expects
                const xhr = new XMLHttpRequest();
                xhr.open('POST', basePath + '/functions/sign-in-db.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                // Don't set responseType to 'json' so we can access responseText for debugging

                xhr.onload = function() {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase';

                    console.log('Login response status:', xhr.status);
                    console.log('Login response text:', xhr.responseText);

                    if (xhr.status === 200) {
                        // Try to parse JSON response
                        let response;
                        try {
                            response = JSON.parse(xhr.responseText);

                            if (response.success) {
                                // Login successful - load payment methods
                                loadUserPaymentMethods();
                            } else {
                                showLoginError(response.message || 'Invalid email or password.');
                            }
                        } catch (e) {
                            // Check if this is a redirect response (means login was successful but not AJAX)
                            if (xhr.responseText.includes('location:') || xhr.responseText.includes('my-ticket.php')) {
                                loadUserPaymentMethods();
                            } else if (xhr.responseText.trim() === '') {
                                // Empty response might mean successful login but no AJAX response
                                loadUserPaymentMethods();
                            } else {
                                showLoginError('Login system error. Please check your credentials and try again.');
                            }
                        }
                    } else {
                        showLoginError('Server error. Please try again.');
                    }
                };

                xhr.onerror = function() {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login & Continue Purchase';
                    showLoginError('Connection error. Please try again.');
                };

                // Use the same parameter names as the existing sign-in system
                // Note: signin_user triggers the login logic, ajax_login=1 makes it return JSON
                // cart_transfer=1 tells the system to transfer guest cart to user cart
                const formData = 'signin_user=1&username=' + encodeURIComponent(email) + '&password=' + encodeURIComponent(password) + '&ajax_login=1&cart_transfer=1&return_to=pre_checkout';
                console.log('Sending login request with data:', formData);
                xhr.send(formData);
            });
        }

        function initializePasswordToggle() {
            const passwordToggle = document.getElementById('password-toggle');
            const passwordInput = document.getElementById('login-password');

            if (passwordToggle && passwordInput) {
                passwordToggle.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    const icon = this.querySelector('i');
                    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
                });
            }
        }

        function showLoginError(message) {
            const loginError = document.getElementById('login-error');
            loginError.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + message;
            loginError.style.display = 'block';
        }

        function initializeCancelButton() {
            const cancelBtn = document.getElementById('cancel-login-btn');
            if (!cancelBtn) return;

            cancelBtn.addEventListener('click', function() {
                // Hide the inline login section
                const inlineLoginSection = document.getElementById('inline-login-section');
                inlineLoginSection.style.display = 'none';

                // Reset and re-enable the email input section
                const emailInput = document.getElementById('guest-email');
                const checkBtn = document.getElementById('check-email-btn');
                const emailResult = document.getElementById('email-check-result');

                emailInput.disabled = false;
                emailInput.value = '';
                checkBtn.style.display = 'inline-block';
                emailResult.innerHTML = '';

                // Clear login form
                document.getElementById('login-password').value = '';
                const loginError = document.getElementById('login-error');
                loginError.style.display = 'none';

                // Focus back on email input
                setTimeout(() => {
                    emailInput.focus();
                }, 100);
            });
        }

        function checkIfUserLoggedIn() {
            // Check if user is already logged in by making a simple AJAX request
            const xhr = new XMLHttpRequest();
            xhr.open('GET', basePath + '/functions/check-login-status.php', true);
            xhr.responseType = 'json';

            xhr.onload = function() {
                if (xhr.status === 200 && xhr.response) {
                    if (xhr.response.logged_in) {
                        // User is logged in, hide email verification and show payment methods directly
                        hideEmailVerificationSection();
                        loadUserPaymentMethods();
                    }
                    // If not logged in, do nothing - let user go through normal email verification
                }
            };

            xhr.onerror = function() {
                // If error checking login status, do nothing - let user go through normal flow
            };

            xhr.send();
        }

        function hideEmailVerificationSection() {
            // Hide the email verification section since user is already logged in
            const emailSection = document.querySelector('.email-section h4');
            const emailDescription = emailSection.nextElementSibling;
            const emailInputGroup = document.querySelector('.email-input-group');
            const emailHelp = document.querySelector('.email-help');

            if (emailSection) emailSection.style.display = 'none';
            if (emailDescription) emailDescription.style.display = 'none';
            if (emailInputGroup) emailInputGroup.style.display = 'none';
            if (emailHelp) emailHelp.style.display = 'none';

            // Show a message that user is logged in with their email
            const emailResult = document.getElementById('email-check-result');
            const userEmail = '<?php echo htmlspecialchars($user_email); ?>';



            // Show permanent user info directly (no temporary popup)
            if (userEmail) {
                emailResult.innerHTML = `
                    <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 12px 16px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-user-circle" style="font-size: 18px;"></i>
                        <span>You are logged-in as <strong>${userEmail}</strong></span>
                    </div>
                `;
            }
        }

        function loadUserPaymentMethods() {
            // Hide login section
            document.getElementById('inline-login-section').style.display = 'none';

            // Show permanent user info for inline login users
            const loginEmailField = document.getElementById('login-email');
            if (loginEmailField && loginEmailField.value) {
                const emailResult = document.getElementById('email-check-result');
                emailResult.innerHTML = `
                    <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 12px 16px; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-user-circle" style="font-size: 18px;"></i>
                        <span>You are logged-in as <strong>${loginEmailField.value}</strong></span>
                    </div>
                `;
            }

            // Show loading state
            const paymentMethodsSection = document.getElementById('payment-methods-section');
            const paymentMethodsContainer = document.getElementById('payment-methods-container');

            paymentMethodsSection.style.display = 'block';
            paymentMethodsContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading payment methods...</div>';

            // Load payment methods using the same approach as cart.php
            const xhr = new XMLHttpRequest();
            xhr.open('GET', basePath + '/functions/get-payment-methods.php', true);
            // Don't set responseType to allow debugging

            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);

                        if (response.success) {
                            if (response.payment_methods && response.payment_methods.length > 0) {
                                displayPaymentMethods(response.payment_methods);
                            } else {
                                paymentMethodsContainer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No saved payment methods found. You can add a new card below.</div>';
                                displayNewCardOption();
                            }
                        } else {
                            paymentMethodsContainer.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>' + (response.message || 'No payment methods found') + '<br><small>This might be because you haven\'t made any purchases yet.</small></div>';
                            displayNewCardOption();
                        }
                    } catch (e) {
                        paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Error loading payment methods. Please try again.</div>';
                    }
                } else {
                    paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Server error loading payment methods.</div>';
                }
            };

            xhr.onerror = function() {
                paymentMethodsContainer.innerHTML = '<div class="alert alert-danger">Connection error. Please try again.</div>';
            };

            xhr.send();
        }

        function displayPaymentMethods(paymentMethods) {
            const container = document.getElementById('payment-methods-container');
            let html = '';

            // Add saved payment methods (now using Stripe format)
            paymentMethods.forEach((method, index) => {
                const isDefault = method.is_default || index === 0; // Use is_default from API or first method
                const cardBrand = method.card_brand.charAt(0).toUpperCase() + method.card_brand.slice(1);

                html += `
                    <div class="payment-method-item" data-payment-method="${method.id}">
                        <div class="payment-method-info">
                            <div class="card-icon">
                                ${getCardIcon(method.card_brand)}
                            </div>
                            <div class="card-details">
                                <div class="card-brand">${cardBrand}</div>
                                <div class="card-number">•••• •••• •••• ${method.card_last4}</div>
                                <div class="card-expiry">Expires ${method.card_exp_month}/${method.card_exp_year}</div>
                            </div>
                        </div>
                        ${isDefault ? '<span class="default-badge">Default</span>' : ''}
                    </div>
                `;
            });

            // Add new card option if user has less than 2 cards (same as cart.php)
            if (paymentMethods.length < 2) {
                html += `
                    <div class="payment-method-item" data-payment-method="new_card">
                        <div class="payment-method-info">
                            <div class="card-icon add-card-icon">
                                <i class="fas fa-credit-card" style="font-size: 20px; color: #6754e2;"></i>
                                <i class="fas fa-plus" style="font-size: 12px; color: #6754e2; position: absolute; top: -2px; right: -2px; background: white; border-radius: 50%; padding: 2px;"></i>
                            </div>
                            <div class="card-details">
                                <div class="card-brand">Credit/Debit Card</div>
                                <div class="card-number">Add New Card</div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // Show warning if user has reached the limit (same as cart.php)
                html += `
                    <div class="alert alert-warning" style="margin-top: 15px;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Maximum Cards Reached</strong><br>
                        You already have 2 payment methods saved. To add a new card, please remove an existing one first.
                        <br><a href="${basePath}/front-end/payment-methods.php?return_to=pre_checkout"
                             style="color: #007bff; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; margin-top: 15px; padding: 10px 16px; background: #e7f3ff; border: 1px solid #007bff; border-radius: 6px; font-weight: 600; transition: all 0.3s ease; cursor: pointer;"
                             onmouseover="this.style.background='#007bff'; this.style.color='white';"
                             onmouseout="this.style.background='#e7f3ff'; this.style.color='#007bff';">
                            <i class="fas fa-credit-card"></i> Manage Payment Methods
                        </a>
                    </div>
                `;
            }

            container.innerHTML = html;

            // Select first payment method by default
            const firstMethod = container.querySelector('.payment-method-item');
            if (firstMethod) {
                firstMethod.classList.add('selected');
                // Initialize save payment method checkbox visibility
                toggleSavePaymentMethodCheckbox(firstMethod);
            }
        }

        function displayNewCardOption() {
            const container = document.getElementById('payment-methods-container');
            container.innerHTML = `
                <div class="payment-method-item selected" data-payment-method="new_card">
                    <div class="payment-method-info">
                        <div class="card-icon add-card-icon">
                            <i class="fas fa-credit-card" style="font-size: 20px; color: #6754e2;"></i>
                            <i class="fas fa-plus" style="font-size: 12px; color: #6754e2; position: absolute; top: -2px; right: -2px; background: white; border-radius: 50%; padding: 2px;"></i>
                        </div>
                        <div class="card-details">
                            <div class="card-brand">Add New Card</div>
                            <div class="card-number">Enter your payment information</div>
                        </div>
                    </div>
                </div>
            `;

            // Show save payment method checkbox for new card
            const saveCardOption = document.getElementById('save-card-option');
            if (saveCardOption) {
                saveCardOption.style.display = 'block';
            }
        }

        function getCardIcon(brand) {
            const brandLower = brand.toLowerCase();
            const basePath = '<?php echo $base_path; ?>';

            // Map card brands to local logo files
            const cardLogos = {
                'visa': basePath + '/image/card-logo/visa.webp',
                'mastercard': basePath + '/image/card-logo/mastercard.svg',
                'amex': basePath + '/image/card-logo/AE.svg',
                'american_express': basePath + '/image/card-logo/AE.svg',
                'discover': basePath + '/image/card-logo/discover.png',
                'maestro': basePath + '/image/card-logo/Maestro.svg',
                'diners': basePath + '/image/card-logo/dinacard.svg',
                'diners_club': basePath + '/image/card-logo/dinacard.svg',
                'jcb': basePath + '/image/card-logo/bccard.png',
                'unionpay': basePath + '/image/card-logo/bccard.png'
            };

            // Get the appropriate logo or fallback to Stripe logo
            const logoSrc = cardLogos[brandLower] || basePath + '/image/card-logo/stripe-payment-logo.png';
            const fallbackSrc = basePath + '/image/card-logo/stripe-payment-logo.png';

            return `<img src="${logoSrc}"
                         alt="${brand}"
                         style="height: 25px; width: auto; max-width: 40px; object-fit: contain;"
                         onerror="this.src='${fallbackSrc}'">`;
        }

        function toggleSavePaymentMethodCheckbox(selectedMethodElement) {
            const saveCardOption = document.getElementById('save-card-option');
            const paymentMethodId = selectedMethodElement.getAttribute('data-payment-method');

            if (paymentMethodId === 'new_card') {
                // Show checkbox for new card
                saveCardOption.style.display = 'block';
            } else {
                // Hide checkbox for saved cards
                saveCardOption.style.display = 'none';
            }
        }

        function initializePaymentMethods() {
            // Handle payment method selection
            document.addEventListener('click', function(e) {
                const paymentMethodItem = e.target.closest('.payment-method-item');
                if (paymentMethodItem) {
                    // Remove selected class from all items
                    document.querySelectorAll('.payment-method-item').forEach(item => {
                        item.classList.remove('selected');
                    });

                    // Add selected class to clicked item
                    paymentMethodItem.classList.add('selected');

                    // Show/hide save payment method checkbox based on selection
                    toggleSavePaymentMethodCheckbox(paymentMethodItem);
                }
            });

            // Handle proceed payment button
            const proceedPaymentBtn = document.getElementById('proceed-payment-btn');
            if (proceedPaymentBtn) {
                proceedPaymentBtn.addEventListener('click', function() {
                    const selectedMethod = document.querySelector('.payment-method-item.selected');
                    if (!selectedMethod) {
                        alert('Please select a payment method.');
                        return;
                    }

                    const paymentMethodId = selectedMethod.getAttribute('data-payment-method');

                    if (paymentMethodId === 'new_card') {
                        // Get save payment method checkbox value
                        const savePaymentMethodCheckbox = document.getElementById('save_payment_method');
                        const savePaymentMethod = savePaymentMethodCheckbox ? savePaymentMethodCheckbox.checked : false;

                        // Add save payment method value to the form
                        const checkoutForm = document.getElementById('checkout-form');
                        let saveMethodInput = checkoutForm.querySelector('input[name="save_payment_method"]');
                        if (!saveMethodInput) {
                            saveMethodInput = document.createElement('input');
                            saveMethodInput.type = 'hidden';
                            saveMethodInput.name = 'save_payment_method';
                            checkoutForm.appendChild(saveMethodInput);
                        }
                        saveMethodInput.value = savePaymentMethod ? '1' : '0';

                        // Submit the existing checkout form which is already configured for cart checkout
                        checkoutForm.submit();
                    } else {
                        // Show confirmation modal for saved payment method
                        showPaymentConfirmation(selectedMethod, paymentMethodId);
                    }
                });
            }
        }

        function showPaymentConfirmation(selectedMethodElement, paymentMethodId) {
            // Get payment method details
            const cardInfo = selectedMethodElement.querySelector('.card-details');
            const cardBrand = cardInfo.querySelector('.card-brand').textContent;
            const cardNumber = cardInfo.querySelector('.card-number').textContent;
            const cardExpiry = cardInfo.querySelector('.card-expiry').textContent;

            // Create and show confirmation modal
            const modalHtml = `
                <div class="modal fade" id="paymentConfirmModal" tabindex="-1" style="z-index: 9999;">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                            <div class="modal-header" style="background: linear-gradient(135deg, #6754e2 0%, #8b7cf6 100%); color: white; border-radius: 15px 15px 0 0;">
                                <h5 class="modal-title"><i class="fas fa-credit-card me-2"></i>Confirm Payment</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" style="padding: 25px;">
                                <div class="payment-summary">
                                    <h6><i class="fas fa-credit-card me-2"></i>Payment Method</h6>
                                    <div class="selected-card" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                        <div style="display: flex; align-items: center; gap: 12px;">
                                            <div class="card-icon">${getCardIcon(cardBrand.toLowerCase())}</div>
                                            <div>
                                                <div style="font-weight: 600;">${cardBrand}</div>
                                                <div style="color: #6c757d; font-size: 14px;">${cardNumber}</div>
                                                <div style="color: #6c757d; font-size: 12px;">${cardExpiry}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <h6><i class="fas fa-shopping-cart me-2"></i>Order Summary</h6>
                                    <div class="order-total" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-weight: 600;">Total Amount:</span>
                                            <span style="font-weight: 600; color: #6754e2; font-size: 18px;">$<?php echo number_format($cart_total, 2); ?></span>
                                        </div>
                                    </div>

                                    <div class="security-notice" style="background: #e8f5e8; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
                                        <small><i class="fas fa-shield-alt me-1"></i>Your payment is secured by Stripe's industry-leading encryption.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="border-top: 1px solid #dee2e6; padding: 20px;">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmPaymentBtn" style="background: #6754e2; border-color: #6754e2;">
                                    <i class="fas fa-lock me-2"></i>Confirm Payment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('paymentConfirmModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('paymentConfirmModal'));
            modal.show();

            // Remove any existing event listeners to prevent duplicates
            const confirmBtn = document.getElementById('confirmPaymentBtn');
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

            // Handle confirm payment
            document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
                // Add debugging
                console.log('Confirm payment clicked');
                console.log('Payment method ID:', paymentMethodId);
                console.log('Base path:', basePath);

                const redirectUrl = basePath + '/front-end/create-pre-checkout-payment.php?payment_method=' + paymentMethodId;
                console.log('Redirecting to:', redirectUrl);

                // Redirect to payment processing with saved payment method (pre-checkout specific)
                window.location.href = redirectUrl;
            });
        }


    </script>
</body>
</html>
