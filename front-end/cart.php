<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/cart-transfer.php');
session_start();

// ดึงข้อมูลตะกร้าโดยไม่ตรวจสอบการล็อกอิน
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// ดึงข้อมูลตะกร้าของผู้ใช้ถ้ามี session
if ($user_id) {
    $query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id); // i = integer
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);
} else {
    // สำหรับผู้ใช้ที่ไม่ได้ล็อกอิน
    $cart_items = isset($_SESSION['guest_cart']) ? $_SESSION['guest_cart'] : [];
}

// Debug: Check if user was redirected here from payment-success with an error
$payment_error = $_GET['error'] ?? null;
if ($payment_error) {
    error_log("Cart page: User redirected from payment-success with error: $payment_error");
    error_log("Cart page: Full URL: " . $_SERVER['REQUEST_URI']);
    error_log("Cart page: Referer: " . ($_SERVER['HTTP_REFERER'] ?? 'none'));
}

// คำนวณยอดรวมของตะกร้าและจำนวนสินค้า
$total_price = 0;
$cart_items_count = 0;

if (!empty($cart_items)) {
    foreach ($cart_items as $item) {
        $quantity = isset($item['quantity']) ? (int)$item['quantity'] : 0;
        $price = isset($item['dollar_price_per_package']) ? (float)$item['dollar_price_per_package'] : 0.00;
        $total_price += $quantity * $price;
        $cart_items_count += $quantity;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Your Cart</title>
    <?php
    // Auto-detect environment for URL paths
    $is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
    $base_path = $is_localhost_url ? '/helloit' : '';
    $url_base = $is_localhost_url ? '/helloit' : '';
    ?>
    <link rel="shortcut icon" href="<?php echo $base_path; ?>/image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>/css/bootstrap.css">
    <link rel="stylesheet" href="<?php echo $base_path; ?>/fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="<?php echo $base_path; ?>/fonts/typography-font/typo.css">
    <link rel="stylesheet" href="<?php echo $base_path; ?>/fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>/plugins/aos/aos.min.css">
    <link rel="stylesheet" href="<?php echo $base_path; ?>/plugins/nice-select/nice-select.min.css">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>/css/main.css">
    <!-- Custom stylesheet -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Nav bar style -->
        <style>
        body {
            margin: 0;
            font-family: 'Circular Std', sans-serif;
        }

        .navbar {
            background-color: #6B62F3;
            padding: 0.8rem 1rem;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            height: 80px;
            display: flex;
            align-items: center;
        }

        .navbar-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            position: relative;
        }

        .navbar-title {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
        }

        /* Center logo in navbar */
        .logo-center {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        /* Logo responsive sizes */
        /* Desktop (default) */
        .logo img {
            height: 60px !important;
            width: 90px !important;
            transition: height 0.3s ease;
        }

        /* Tablet */
        @media (max-width: 991px) {
            .logo img {
                height: 70px !important;
                width: 100px !important;
            }
        }

        /* Mobile */
        @media (max-width: 767px) {
            .logo img {
                height: 40px !important;
                width: 60px !important;
            }
        }

        /* Small mobile */
        @media (max-width: 480px) {
            .logo img {
                height: 40px;
            }
        }

        .back-button {
            color: #ecf0f1;
            text-decoration: none;
            background-color: #3498db;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            font-size: 0.9rem;
        }

        .back-button:hover {
            background-color: #2980b9;
            color: white !important;
            /* Override the global a:hover style */
        }

        /* Responsive layout */
        @media (max-width: 576px) {
            .navbar {
                padding: 0.7rem 0.8rem;
            }

            .back-button {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }

            .navbar-title {
                font-size: 1.1rem;
            }
        }

        @media (min-width: 577px) and (max-width: 991px) {
            .navbar {
                padding: 0.8rem 1.5rem;
            }
        }

        @media (min-width: 992px) {
            .navbar {
                padding: 1rem 2rem;
            }

            .back-button {
                padding: 0.5rem 1.2rem;
                font-size: 1rem;
            }
        }
        </style>
        <style>
        /* General styles */
        body {
            font-family: 'Circular Std', sans-serif;
            background-color: var(--bg);
            margin: 0;
            padding: 0;
        }

        /* Cart Actions Header */
        .cart-actions-header {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .bulk-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }

        /* Custom Checkbox Styles */
        .checkbox-container,
        .select-all-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .checkbox-container input[type="checkbox"],
        .select-all-container input[type="checkbox"] {
            display: none;
        }

        .checkmark-custom {
            width: 20px;
            height: 20px;
            background-color: #fff;
            border: 2px solid #6754e2;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .checkbox-container input[type="checkbox"]:checked+.checkmark-custom,
        .select-all-container input[type="checkbox"]:checked+.checkmark-custom {
            background-color: #6754e2;
        }

        .checkbox-container input[type="checkbox"]:checked+.checkmark-custom::after,
        .select-all-container input[type="checkbox"]:checked+.checkmark-custom::after {
            content: "✓";
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .select-all-container input[type="checkbox"]:indeterminate+.checkmark-custom {
            background-color: #6754e2;
        }

        .select-all-container input[type="checkbox"]:indeterminate+.checkmark-custom::after {
            content: "−";
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .select-all-text {
            font-weight: 500;
            color: #333;
        }

        /* Bulk Remove Button */
        .btn-remove-selected {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-remove-selected:hover:not(:disabled) {
            background-color: #c82333;
            transform: translateY(-1px);
        }

        .btn-remove-selected:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        /* Quantity Controls */
        .quantity-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 120px;
            /* Fixed width for consistent alignment */
            margin: 0 auto;
            /* Center the container */
            position: relative;
        }

        .qty-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.2s ease;
            position: absolute;
        }

        .qty-minus {
            left: 0;
        }

        .qty-plus {
            right: 0;
        }

        .qty-btn:hover {
            background-color: #6754e2;
            color: white;
            border-color: #6754e2;
        }

        .quantity-input {
            width: 60px;
            height: 30px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-weight: 500;
            margin: 0 auto;
            /* Center the input */
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            /* Perfect center alignment */
            -moz-appearance: textfield;
            /* Remove arrows in Firefox */
            appearance: textfield;
            /* Standard property for compatibility */
        }

        /* Remove arrows in Chrome, Safari, Edge */
        .quantity-input::-webkit-outer-spin-button,
        .quantity-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .quantity-input:focus {
            outline: none;
            border-color: #6754e2;
            box-shadow: 0 0 0 2px rgba(103, 84, 226, 0.2);
        }

        /* Quantity Limit Popup */
        .quantity-limit-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: none;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .quantity-limit-popup.show {
            opacity: 1;
            transform: translateX(0);
        }

        .popup-content {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 280px;
            position: relative;
        }

        .popup-icon {
            color: #856404;
            font-size: 20px;
            flex-shrink: 0;
        }

        .popup-message {
            color: #856404;
            font-size: 14px;
            line-height: 1.4;
            flex-grow: 1;
        }

        .popup-message strong {
            font-weight: 600;
        }

        .popup-close {
            background: none;
            border: none;
            color: #856404;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s ease;
            flex-shrink: 0;
        }

        .popup-close:hover {
            background-color: rgba(133, 100, 4, 0.1);
        }

        /* Selected Items Preview */
        .selected-items-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
        }

        .selected-items-preview .list-group-item {
            border-left: none;
            border-right: none;
            border-radius: 0;
        }

        .selected-items-preview .list-group-item:first-child {
            border-top: none;
        }

        .selected-items-preview .list-group-item:last-child {
            border-bottom: none;
        }

        /* Payment Confirmation Modal Styles */
        .payment-confirm-modal {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .payment-modal-header {
            background: linear-gradient(135deg, #6754e2 0%, #8b7cf6 100%);
            color: white;
            padding: 25px 30px;
            border-bottom: none;
            position: relative;
        }

        .payment-icon-wrapper {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .modal-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 400;
        }

        .btn-close-custom {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn-close-custom:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .payment-modal-body {
            padding: 30px;
            background: #fafbfc;
        }

        .section-title {
            color: #2d3748;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            color: #6754e2;
        }

        .payment-method-section {
            margin-bottom: 25px;
        }

        .selected-payment-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 2px solid #e2e8f0;
        }

        .card-display {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .card-icon {
            flex-shrink: 0;
        }

        .card-details {
            flex-grow: 1;
        }

        .card-brand {
            font-weight: 600;
            font-size: 16px;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #4a5568;
            margin-bottom: 4px;
        }

        .card-expiry {
            font-size: 12px;
            color: #718096;
        }

        .card-badge {
            flex-shrink: 0;
        }

        .card-badge .badge {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .order-summary-section {
            margin-bottom: 25px;
        }

        .order-details {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 2px solid #e2e8f0;
        }

        .order-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
        }

        .total-label {
            font-weight: 600;
            color: #2d3748;
            font-size: 16px;
        }

        .total-amount {
            font-weight: 700;
            color: #6754e2;
            font-size: 24px;
        }

        .order-note {
            display: flex;
            align-items: center;
            color: #718096;
            font-size: 14px;
        }

        .order-note i {
            color: #6754e2;
        }

        .security-notice {
            background: linear-gradient(135deg, #f0fff4 0%, #f7fafc 100%);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #c6f6d5;
        }

        .security-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .security-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            flex-shrink: 0;
        }

        .security-text h6 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .security-text p {
            color: #4a5568;
            font-size: 13px;
            margin: 0;
            line-height: 1.5;
        }

        .payment-modal-footer {
            background: white;
            padding: 20px 30px;
            border-top: 2px solid #e2e8f0;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .btn-cancel {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-cancel:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            color: #2d3748;
            transform: translateY(-2px);
        }

        .btn-confirm {
            background: linear-gradient(135deg, #6754e2 0%, #8b7cf6 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(103, 84, 226, 0.3);
        }

        .btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(103, 84, 226, 0.4);
            background: linear-gradient(135deg, #5a47d1 0%, #7c6df0 100%);
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .payment-modal-header {
                padding: 20px;
            }

            .payment-modal-body {
                padding: 20px;
            }

            .payment-modal-footer {
                padding: 15px 20px;
                flex-direction: column;
            }

            .btn-cancel,
            .btn-confirm {
                width: 100%;
                justify-content: center;
            }

            .card-display {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .order-total {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }

        .cart-container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .cart-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-family: 'Circular Std', sans-serif;
        }

        .cart-table th,
        .cart-table td {
            padding: 15px;
            text-align: center;
            border: 1px solid var(--border-color);
            font-family: 'Circular Std', sans-serif;
        }

        /* Align Select column to the left */
        .cart-table th:first-child,
        .cart-table td:first-child {
            text-align: left;
            padding-left: 20px;
        }

        .cart-table th {
            background-color: var(--bg-2);
            color: var(--color-headings);
            font-weight: 600;
            font-family: 'Circular Std', sans-serif;
        }

        .cart-table td {
            background-color: var(--bg-6);
            color: var(--color-texts);
            font-family: 'Circular Std', sans-serif;
        }



        .cart-summary {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid var(--border-color);
            font-family: 'Circular Std', sans-serif;
        }

        .cart-summary h3 {
            font-size: 24px;
            color: var(--color-headings);
            font-weight: 600;
            font-family: 'Circular Std', sans-serif;
        }

        /* Save Credit Card Checkbox Styles */
        .save-card-option {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .save-card-checkbox {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            font-family: 'Circular Std', sans-serif;
            margin-bottom: 8px;
            justify-content: center;
        }

        .save-card-checkbox input[type="checkbox"] {
            display: none;
        }

        .save-card-checkbox .checkmark {
            width: 20px;
            height: 20px;
            background-color: #fff;
            border: 2px solid #6754e2;
            border-radius: 4px;
            margin-right: 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .save-card-checkbox input[type="checkbox"]:checked+.checkmark {
            background-color: #6754e2;
        }

        .save-card-checkbox input[type="checkbox"]:checked+.checkmark::after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .checkbox-text {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .save-card-note {
            font-size: 13px;
            color: #6c757d;
            margin: 0;
            margin-left: 32px;
            line-height: 1.4;
        }

        .checkout-btn {
            display: inline-block;
            background-color: #6754e2;
            color: #fff !important;
            padding: 12px 25px;
            font-size: 16px;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            font-family: 'Circular Std', sans-serif;
        }

        .checkout-btn:hover {
            background-color: #5344c9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        /* Payment Methods Styles */
        .payment-methods {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            font-family: 'Circular Std', sans-serif;
        }

        .payment-methods h4 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
            font-weight: 600;
            font-family: 'Circular Std', sans-serif;
        }

        .payment-option {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            background-color: white;
        }

        .payment-option:hover {
            border-color: #6754e2;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .payment-option input {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .payment-option label {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-bottom: 0;
            font-family: 'Circular Std', sans-serif;
        }

        .payment-option img {
            height: 25px;
            margin-left: auto;
            flex-shrink: 0;
        }

        /* Payment method info styles */
        .payment-method-info {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .card-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-brand {
            font-weight: 600;
            color: #333;
        }

        .card-number {
            color: #666;
            font-family: monospace;
        }

        .card-expiry {
            font-size: 12px;
            color: #888;
            margin-top: 2px;
        }

        /* Payment limit warning styles */
        .payment-limit-warning {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }

        .warning-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .warning-content i {
            color: #f39c12;
            font-size: 24px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .warning-text {
            text-align: left;
            width: 100%;
        }

        .warning-text h5 {
            color: #856404;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 16px;
            text-align: left;
        }

        .warning-text p {
            color: #856404;
            margin-bottom: 12px;
            font-size: 14px;
            line-height: 1.4;
            text-align: left;
        }

        .manage-cards-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: #6754e2;
            color: white !important;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .manage-cards-btn:hover {
            background-color: #5344c9;
            color: white !important;
            text-decoration: none;
        }

        .manage-cards-btn i {
            color: white;
            font-size: 14px;
        }

        /* Success Animation Styles */
        .success-animation {
            margin: 0 auto;
            width: 100px;
            height: 100px;
        }

        .checkmark {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #4bb71b;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #4bb71b;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 1;
            stroke-miterlimit: 2;
            stroke: #4bb71b;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {

            0%,
            100% {
                transform: none;
            }

            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 1px #4bb71b;
            }
        }

        @keyframes resetStrokeWidth {
            0% {
                stroke-width: 1;
            }

            100% {
                stroke-width: 1;
            }
        }

        /* Responsive design */
        /* Mobile devices */
        @media (max-width: 576px) {
            .cart-container {
                width: 100%;
                padding: 10px;
                margin: 10px auto;
            }

            h1 {
                font-size: 1.5rem;
                margin-bottom: 15px;
            }

            .payment-methods {
                padding: 15px;
                margin-top: 15px;
            }

            .payment-methods h4 {
                font-size: 16px;
                margin-bottom: 10px;
            }

            .payment-option {
                padding: 8px;
                margin-bottom: 10px;
            }

            .payment-option label {
                font-size: 14px;
            }

            .payment-option img {
                height: 20px;
            }

            #checkout-button {
                width: 100%;
                margin-top: 10px;
            }

            /* Mobile bulk actions - keep on same row */
            .bulk-actions {
                flex-wrap: wrap;
                gap: 10px;
            }

            .btn-remove-selected {
                font-size: 14px;
                padding: 8px 12px;
            }



            /* Convert table to cards on mobile */
            .cart-table,
            .cart-table tbody,
            .cart-table tr {
                display: block;
                width: 100%;
            }

            .cart-table thead {
                display: none;
                /* Hide table headers on mobile */
            }

            .cart-table tr {
                margin-bottom: 15px;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                overflow: hidden;
                position: relative;
            }

            /* Checkbox positioning on mobile - show as first column */
            .cart-table td[data-label="Select"] {
                order: -1;
                background: none !important;
                border: none !important;
                padding: 10px 15px !important;
                width: auto !important;
                justify-content: flex-start !important;
                text-align: left !important;
            }

            .cart-table td[data-label="Select"]:before {
                display: none;
            }

            .cart-table td {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 15px;
                text-align: right;
                border: none;
                border-bottom: 1px solid var(--border-color);
                font-size: 14px;
            }

            /* Adjust quantity controls for mobile */
            .quantity-controls {
                width: 100px;
                /* Smaller fixed width for mobile */
            }

            .qty-btn {
                width: 25px;
                height: 25px;
                font-size: 12px;
            }

            .quantity-input {
                width: 50px;
                height: 25px;
                font-size: 14px;
            }

            /* Mobile popup adjustments */
            .quantity-limit-popup {
                top: 10px;
                right: 10px;
                left: 10px;
                right: 10px;
            }

            .popup-content {
                min-width: auto;
                padding: 12px 15px;
                font-size: 13px;
            }

            .popup-icon {
                font-size: 18px;
            }

            .popup-message {
                font-size: 13px;
            }

            /* Apply background color to Product column only on mobile */
            .cart-table td[data-label="Product"] {
                background-color: #F4F7FA !important;
                padding-left: 15px !important;
                /* Move Product cell content to the left */
            }

            /* Style the Product label on mobile */
            .cart-table td[data-label="Product"]:before {
                background-color: #F4F7FA;
                padding: 5px 10px;
                border-radius: 4px;
                margin-bottom: 5px;
                width: 100%;
                display: block;
                margin-left: -10px;
                /* Move Product label to the left */
            }

            .cart-table td:last-child {
                border-bottom: none;
            }

            .cart-table td:before {
                content: attr(data-label);
                font-weight: 600;
                text-align: left;
                color: var(--color-headings);
            }

            .cart-summary h3 {
                font-size: 18px;
            }

            .checkout-btn {
                font-size: 14px;
                padding: 8px 16px;
                width: 100%;
                text-align: center;
                margin-top: 10px;
            }

            /* Modal adjustments for mobile */
            .modal-dialog {
                margin: 0.5rem;
            }

            .modal-content {
                border-radius: 10px;
            }

            .modal-body {
                padding: 1.5rem 1rem;
            }

            .modal-footer {
                padding: 1rem;
                flex-direction: column;
            }

            .modal-footer button {
                width: 100%;
                margin: 0.25rem 0 !important;
            }
        }

        /* Tablet devices */
        @media (min-width: 577px) and (max-width: 991px) {
            .cart-container {
                width: 95%;
                padding: 15px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .cart-table th,
            .cart-table td {
                font-size: 15px;
                padding: 12px 10px;
            }

            .cart-summary h3 {
                font-size: 20px;
            }

            .checkout-btn {
                font-size: 15px;
                padding: 10px 20px;
            }

            .payment-methods {
                padding: 18px;
                margin-top: 18px;
            }

            .payment-option {
                padding: 10px;
            }

            .payment-methods h4 {
                font-size: 17px;
                margin-bottom: 12px;
            }
        }

        /* Desktop devices */
        @media (min-width: 992px) {
            .cart-container {
                width: 90%;
                max-width: 1200px;
                padding: 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .cart-table th,
            .cart-table td {
                padding: 15px;
            }

            .checkout-btn {
                font-size: 16px;
                padding: 12px 25px;
            }

            .payment-methods {
                padding: 25px;
                margin-top: 25px;
                border-radius: 12px;
            }

            .payment-option {
                padding: 15px;
                margin-bottom: 20px;
            }

            .payment-methods h4 {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .payment-option label {
                font-size: 17px;
            }

            .payment-option img {
                height: 30px;
            }
        }

        /* Modal styling improvements */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-family: 'Circular Std', sans-serif;
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
            background-color: var(--bg-2);
            padding: 1.5rem;
        }

        .modal-title {
            font-weight: 700;
            color: var(--color-headings);
            font-family: 'Circular Std', sans-serif;
            font-size: 1.5rem;
        }

        .modal-body {
            color: var(--color-texts);
            font-size: 16px;
            padding: 2rem 1.5rem;
            font-family: 'Circular Std', sans-serif;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            padding: 1.5rem;
        }

        .btn-danger,
        .confirm-btn {
            background-color: #f64b4b;
            border-color: #f64b4b;
            font-family: 'Circular Std', sans-serif;
            font-weight: 500;
            cursor: pointer;
        }

        .btn-danger:hover,
        .confirm-btn:hover {
            background-color: #e03e3e;
            border-color: #e03e3e;
        }

        .btn-secondary,
        .btn-outline-secondary,
        .cancel-btn {
            background-color: #6c757d;
            border-color: #6c757d;
            font-family: 'Circular Std', sans-serif;
            font-weight: 500;
            cursor: pointer;
        }

        .btn-outline-secondary,
        .cancel-btn {
            background-color: transparent;
            color: #6c757d;
        }

        .btn-outline-secondary:hover,
        .cancel-btn:hover {
            background-color: #6c757d;
            color: white;
        }

        .rounded-5 {
            border-radius: 5px;
        }

        /* Quantity input styling */
        #removeQuantity {
            width: 100px;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            text-align: center;
            font-family: 'Circular Std', sans-serif;
            font-size: 16px;
        }

        #currentQuantityInfo {
            color: var(--color-headings);
            font-weight: 500;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-text {
            font-size: 12px;
            margin-top: 5px;
        }

        /* Modal footer responsive styles */
        .modal-footer-responsive {
            display: flex;
            justify-content: space-between;
        }

        /* Additional responsive styles */
        @media (max-width: 576px) {
            .success-title {
                font-size: 1.5rem;
            }

            .success-message,
            .countdown-text {
                font-size: 0.9rem !important;
            }

            .success-animation {
                width: 80px;
                height: 80px;
            }

            .checkmark {
                width: 80px;
                height: 80px;
            }

            .modal-footer-responsive {
                flex-direction: column;
            }

            .modal-footer-responsive button {
                width: 100%;
                margin: 0.25rem 0 !important;
            }
        }

        @media (min-width: 577px) and (max-width: 991px) {
            .success-title {
                font-size: 1.8rem;
            }

            .success-animation {
                width: 90px;
                height: 90px;
            }

            .checkmark {
                width: 90px;
                height: 90px;
            }
        }
        </style>

        <nav class="navbar">
            <div class="navbar-container">
                <div class="logo-container" style="display: flex; align-items: center;">
                    <a href="#" class="back-button" onclick="
                        // Smart back navigation that skips Stripe checkout pages and post-login redirects
                        function smartBack() {
                            const referrer = document.referrer;
                            const currentUrl = window.location.href;
                            const urlParams = new URLSearchParams(window.location.search);
                            const isPostLogin = urlParams.has('login_success') || urlParams.has('cart_transferred');

                            // Check if referrer is a Stripe checkout page
                            if (referrer && (
                                referrer.includes('checkout.stripe.com') ||
                                referrer.includes('js.stripe.com') ||
                                referrer.includes('stripe.com/checkout')
                            )) {
                                // Skip Stripe page and go to buy-now page instead
                                window.location.href = '<?php echo $url_base; ?>/support-ticket/buy-now';
                                return;
                            }

                            // Check if referrer is pre-checkout.php (user came from pre-checkout)
                            if (referrer && referrer.includes('pre-checkout.php')) {
                                // Go to buy-now page instead of back to pre-checkout
                                window.location.href = '<?php echo $url_base; ?>/support-ticket/buy-now';
                                return;
                            }

                            // Check if this is a post-login scenario (user just logged in)
                            if (isPostLogin && referrer && referrer.includes('sign-in.php')) {
                                // Skip the sign-in page and go to buy-now page instead
                                window.location.href = '<?php echo $url_base; ?>/support-ticket/buy-now';
                                return;
                            }

                            // Check if referrer is a sign-in page (avoid redirect loops)
                            if (referrer && referrer.includes('sign-in.php')) {
                                // Don't go back to sign-in page, go to buy-now instead
                                window.location.href = '<?php echo $url_base; ?>/support-ticket/buy-now';
                                return;
                            }

                            // Check if there's a valid referrer that's not the current page
                            if (referrer && referrer !== currentUrl && !referrer.includes('stripe.com')) {
                                window.history.back();
                            } else {
                                // Default fallback to buy-now page
                                window.location.href = '<?php echo $url_base; ?>/support-ticket/buy-now';
                            }
                        }

                        smartBack();
                        return false;
                    ">Back</a>
                </div>
                <!-- href="../index.php" -->
                <div class="logo-center">
                    <a class="logo">
                        <?php
                        $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                        $logo_path = $is_localhost ? '/helloit/image/wp/HelloIT-new.png' : '/image/wp/HelloIT-new.png';
                        ?>
                        <img src="<?php echo $logo_path; ?>" alt="HelloIT Logo">
                    </a>
                </div>
                <div style="width:90px;"></div> <!-- Placeholder for right side, adjust width as needed -->
            </div>
        </nav>
        <div class="cart-container">
            <h1>My Cart</h1>

            <!-- Login Success Message -->
            <?php if (isset($_GET['login_success']) && $_GET['login_success'] == '1'): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Welcome back!</strong> You have successfully signed in. Your cart is ready for checkout.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <!-- Cart Transfer Success Message - Only show if cart has items -->
            <?php if (isset($_SESSION['cart_transfer_success']) && $cart_items_count > 0): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="margin-bottom: 20px;" id="cartTransferAlert">
                <i class="fas fa-shopping-cart me-2"></i>
                <strong>Cart Transfer Successful!</strong>
                <?php echo htmlspecialchars($_SESSION['cart_transfer_success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <script>
                // Auto-dismiss cart transfer message after 3 seconds
                setTimeout(function() {
                    const alert = document.getElementById('cartTransferAlert');
                    if (alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 3000);
            </script>
            <?php
                unset($_SESSION['cart_transfer_success']);
            elseif (isset($_SESSION['cart_transfer_success'])):
                // Clear the message if cart is empty (items were likely purchased)
                unset($_SESSION['cart_transfer_success']);
            endif; ?>

            <!-- Cart Transfer Error Message -->
            <?php if (isset($_SESSION['cart_transfer_error'])): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Cart Transfer Issue:</strong> <?php echo htmlspecialchars($_SESSION['cart_transfer_error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php
                unset($_SESSION['cart_transfer_error']);
            endif; ?>

            <!-- Payment Error Message -->
            <?php if ($payment_error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Payment Error:</strong> <?php echo htmlspecialchars($payment_error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <?php if ($cart_items): ?>
            <div class="cart-actions-header">
                <div class="bulk-actions">
                    <label class="select-all-container">
                        <input type="checkbox" id="selectAll">
                        <span class="checkmark-custom"></span>
                        <span class="select-all-text">Select All</span>
                    </label>
                    <button type="button" id="removeSelectedBtn" class="btn-remove-selected" disabled>
                        <i class="fas fa-trash"></i> Remove Selected
                    </button>
                </div>
            </div>
            <table class="cart-table">
                <thead>
                    <tr>
                        <th width="5%">Select</th>
                        <th>Product</th>
                        <th>Price</th>
                        <th>Quantity</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($cart_items as $item): ?>
                    <tr>
                        <td data-label="Select">
                            <label class="checkbox-container">
                                <input type="checkbox" class="item-checkbox"
                                    data-cart-item-id="<?php echo isset($item['cart_item_id']) ? $item['cart_item_id'] : ''; ?>">
                                <span class="checkmark-custom"></span>
                            </label>
                        </td>
                        <td data-label="Product">
                            <?php
                            $product_name = isset($item['ticket_type']) ? htmlspecialchars($item['ticket_type']) : 'Unknown';
                            if (isset($item['package_size']) && !empty($item['package_size'])) {
                                $product_name .= ' ' . htmlspecialchars($item['package_size']);
                            }
                            echo $product_name;
                            ?>
                        </td>
                        <td data-label="Price">
                            $<?php echo isset($item['dollar_price_per_package']) ? number_format($item['dollar_price_per_package'], 2) : '0.00'; ?>
                        </td>
                        <td data-label="Quantity">
                            <div class="quantity-controls">
                                <button type="button" class="qty-btn qty-minus"
                                    data-cart-item-id="<?php echo isset($item['cart_item_id']) ? $item['cart_item_id'] : ''; ?>"
                                    style="<?php echo (isset($item['quantity']) && $item['quantity'] <= 1) ? 'display: none;' : ''; ?>">-</button>
                                <input type="number" min="1" max="10"
                                    value="<?php echo isset($item['quantity']) ? $item['quantity'] : 1; ?>"
                                    class="quantity-input"
                                    data-cart-item-id="<?php echo isset($item['cart_item_id']) ? $item['cart_item_id'] : ''; ?>"
                                    data-price="<?php echo isset($item['dollar_price_per_package']) ? $item['dollar_price_per_package'] : 0; ?>">
                                <button type="button" class="qty-btn qty-plus"
                                    data-cart-item-id="<?php echo isset($item['cart_item_id']) ? $item['cart_item_id'] : ''; ?>"
                                    style="<?php echo (isset($item['quantity']) && $item['quantity'] >= 10) ? 'display: none;' : ''; ?>">+</button>
                            </div>
                        </td>


                        <td data-label="Total">
                            <span class="item-total"
                                data-cart-item-id="<?php echo isset($item['cart_item_id']) ? $item['cart_item_id'] : ''; ?>"
                                data-ticket-price="<?php echo isset($item['dollar_price_per_package']) ? $item['dollar_price_per_package'] : 0; ?>">
                                $<?php echo isset($item['quantity']) && isset($item['dollar_price_per_package']) ? number_format($item['quantity'] * $item['dollar_price_per_package'], 2) : '0.00'; ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <div class="cart-summary">
                <h3>Total Price: $<?php echo number_format($total_price, 2); ?></h3>

                <?php if ($user_id): ?>
                <!-- Logged-in users: Redirect to pre-checkout for payment method selection -->
                <div class="text-center">
                    <p class="mb-3" style="color: #6c757d;">Ready to complete your purchase?</p>
                    <a href="<?php echo $base_path; ?>/front-end/pre-checkout.php" class="checkout-btn">
                        <i class="fas fa-credit-card me-2"></i> &nbsp; Proceed to Checkout
                    </a>
                    <p class="mt-3" style="font-size: 14px; color: #6c757d;">
                        <i class="fas fa-info-circle me-1"></i>
                        Select your payment method and complete your purchase securely
                    </p>
                </div>
                <?php else: ?>
                <!-- Guest checkout - redirect to pre-checkout page -->
                <div class="text-center">
                    <p class="mb-3" style="color: #6c757d;">Ready to complete your purchase?</p>
                    <a href="<?php echo $base_path; ?>/front-end/pre-checkout.php" class="checkout-btn">
                        <i class="fas fa-credit-card me-2"></i> &nbsp; Proceed to Checkout
                    </a>
                    <p class="mt-3" style="font-size: 14px; color: #6c757d;">
                        <i class="fas fa-info-circle me-1"></i>
                        We'll verify your email address for a secure checkout experience
                    </p>
                </div>
                <!-- Old guest checkout button removed/commented out -->
                <!-- <a href="sign-in.php" class="checkout-btn">Log in to Proceed with Checkout</a> -->
                <?php endif; ?>
            </div>
            <?php else: ?>
            <p>Your cart is empty.</p>
            <?php endif; ?>
        </div>

        <script>
        // Define base path for JavaScript
        const basePath = '<?php echo $base_path; ?>';

        // Enhanced quantity input handling with auto-save
        document.addEventListener('DOMContentLoaded', function() {
            initializeQuantityControls();
            initializeCheckboxes();
            initializeBulkActions();

        });

        function initializeQuantityControls() {
            // Initialize button visibility on page load
            document.querySelectorAll('.quantity-input').forEach(function(input) {
                const quantity = parseInt(input.value);
                const cartItemId = input.dataset.cartItemId;
                updateButtonVisibility(cartItemId, quantity);
            });

            // Handle quantity input changes
            document.querySelectorAll('.quantity-input').forEach(function(input) {
                input.addEventListener('change', function() {
                    updateQuantity(this);
                });

                input.addEventListener('blur', function() {
                    updateQuantity(this);
                });
            });

            // Handle plus/minus buttons
            document.querySelectorAll('.qty-plus').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const cartItemId = this.dataset.cartItemId;
                    const input = document.querySelector(
                        `.quantity-input[data-cart-item-id="${cartItemId}"]`);
                    if (input) {
                        const currentValue = parseInt(input.value);
                        if (currentValue < 10) {
                            input.value = currentValue + 1;
                            updateQuantity(input);
                        }
                    }
                });
            });

            document.querySelectorAll('.qty-minus').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const cartItemId = this.dataset.cartItemId;
                    const input = document.querySelector(
                        `.quantity-input[data-cart-item-id="${cartItemId}"]`);
                    if (input) {
                        const currentValue = parseInt(input.value);
                        if (currentValue > 1) {
                            input.value = currentValue - 1;
                            updateQuantity(input);
                        }
                    }
                });
            });
        }

        function updateQuantity(input) {
            var quantity = parseInt(input.value);
            var showPopup = false;

            // Validate quantity range (1-10)
            if (isNaN(quantity) || quantity < 1) {
                quantity = 1;
                input.value = 1;
            } else if (quantity > 10) {
                quantity = 10;
                input.value = 10;
                showPopup = true; // Show popup for exceeding maximum
            }

            var cartItemId = input.dataset.cartItemId;
            var pricePerItem = parseFloat(input.dataset.price);
            var totalForItem = quantity * pricePerItem;

            // Show popup if user exceeded maximum quantity
            if (showPopup) {
                showQuantityLimitPopup();
            }

            // Update button visibility based on quantity
            updateButtonVisibility(cartItemId, quantity);

            // Update total for that item
            const totalSpan = document.querySelector(`.item-total[data-cart-item-id="${cartItemId}"]`);
            if (totalSpan) {
                totalSpan.textContent = '$' + totalForItem.toLocaleString(undefined, {
                    minimumFractionDigits: 2
                });
            }

            // Recalculate cart total
            updateCartTotal();

            // Auto-save quantity change to server
            saveQuantityChange(cartItemId, quantity);
        }

        function updateButtonVisibility(cartItemId, quantity) {
            const minusBtn = document.querySelector(`.qty-minus[data-cart-item-id="${cartItemId}"]`);
            const plusBtn = document.querySelector(`.qty-plus[data-cart-item-id="${cartItemId}"]`);

            // Hide minus button if quantity is 1
            if (minusBtn) {
                minusBtn.style.display = quantity <= 1 ? 'none' : 'flex';
            }

            // Hide plus button if quantity is 10
            if (plusBtn) {
                plusBtn.style.display = quantity >= 10 ? 'none' : 'flex';
            }
        }

        function showQuantityLimitPopup() {
            // Create popup if it doesn't exist
            let popup = document.getElementById('quantityLimitPopup');
            if (!popup) {
                popup = document.createElement('div');
                popup.id = 'quantityLimitPopup';
                popup.className = 'quantity-limit-popup';
                popup.innerHTML = `
                    <div class="popup-content">
                        <div class="popup-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="popup-message">
                            <strong>Maximum Quantity Reached</strong><br>
                            Maximum quantity is 10
                        </div>
                        <button class="popup-close" onclick="closeQuantityLimitPopup()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                document.body.appendChild(popup);
            }

            // Show popup
            popup.style.display = 'block';
            popup.classList.add('show');

            // Auto-hide after 3 seconds
            setTimeout(function() {
                closeQuantityLimitPopup();
            }, 3000);
        }

        function closeQuantityLimitPopup() {
            const popup = document.getElementById('quantityLimitPopup');
            if (popup) {
                popup.classList.remove('show');
                setTimeout(function() {
                    popup.style.display = 'none';
                }, 300);
            }
        }

        function updateCartTotal() {
            var total = 0;
            document.querySelectorAll('.item-total').forEach(function(totalSpan) {
                const priceText = totalSpan.textContent.replace('$', '').replace(/,/g, '');
                total += parseFloat(priceText);
            });
            document.querySelector('.cart-summary h3').textContent = 'Total Price: $' + total.toLocaleString(
                undefined, {
                    minimumFractionDigits: 2
                });
        }

        function saveQuantityChange(cartItemId, quantity) {
            // Send AJAX request to update quantity
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $base_path; ?>/front-end/cart-update-quantity.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            const requestData = 'cart_item_id=' + encodeURIComponent(cartItemId) + '&quantity=' + encodeURIComponent(
                quantity);
            xhr.send(requestData);

            xhr.onload = function() {
                if (xhr.status !== 200 || !xhr.response || xhr.response.status !== 'success') {
                    console.error('Failed to update quantity:', xhr.response?.message || 'Unknown error');
                    // Optionally show user notification
                }
            };

            xhr.onerror = function() {
                console.error('Error updating quantity');
            };
        }

        function initializeCheckboxes() {
            // Handle select all checkbox
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
                    itemCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActionButton();
                });
            }

            // Handle individual item checkboxes
            document.querySelectorAll('.item-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    updateSelectAllState();
                    updateBulkActionButton();
                });
            });
        }

        function updateSelectAllState() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const itemCheckboxes = document.querySelectorAll('.item-checkbox');
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');

            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === itemCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        function updateBulkActionButton() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            const removeSelectedBtn = document.getElementById('removeSelectedBtn');

            if (removeSelectedBtn) {
                removeSelectedBtn.disabled = checkedBoxes.length === 0;
                removeSelectedBtn.innerHTML = checkedBoxes.length > 0 ?
                    `<i class="fas fa-trash"></i> Remove Selected (${checkedBoxes.length})` :
                    '<i class="fas fa-trash"></i> Remove Selected';
            }
        }

        function initializeBulkActions() {
            // Handle bulk remove button
            const removeSelectedBtn = document.getElementById('removeSelectedBtn');
            if (removeSelectedBtn) {
                removeSelectedBtn.addEventListener('click', function() {
                    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                    if (checkedBoxes.length === 0) {
                        alert('Please select items to remove.');
                        return;
                    }

                    showBulkRemoveModal(checkedBoxes);
                });
            }

            // Handle bulk remove confirmation
            const confirmBulkRemove = document.getElementById('confirmBulkRemove');
            if (confirmBulkRemove) {
                confirmBulkRemove.addEventListener('click', function() {
                    executeBulkRemove();
                });
            }
        }

        function showBulkRemoveModal(checkedBoxes) {
            const selectedItemsCount = document.getElementById('selectedItemsCount');
            const selectedItemsList = document.getElementById('selectedItemsList');

            selectedItemsCount.textContent = `${checkedBoxes.length} item(s) selected`;

            // Build list of selected items
            let itemsHtml = '<div class="list-group">';
            checkedBoxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const productName = row.querySelector('[data-label="Product"]').textContent.trim();
                const quantity = row.querySelector('.quantity-input').value;
                const price = row.querySelector('[data-label="Price"]').textContent.trim();

                itemsHtml += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${productName}</strong><br>
                            <small class="text-muted">Quantity: ${quantity} × ${price}</small>
                        </div>
                    </div>
                `;
            });
            itemsHtml += '</div>';

            selectedItemsList.innerHTML = itemsHtml;
            $('#bulkRemoveModal').modal('show');
        }

        function executeBulkRemove() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            const cartItemIds = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.cartItemId);

            $('#bulkRemoveModal').modal('hide');

            // Send AJAX request to remove multiple items
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $base_path; ?>/front-end/cart-bulk-remove.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            const requestData = 'cart_item_ids=' + encodeURIComponent(JSON.stringify(cartItemIds));
            xhr.send(requestData);

            xhr.onload = function() {
                console.log('Bulk remove response:', xhr.response);
                console.log('Status:', xhr.status);
                if (xhr.status == 200 && xhr.response && xhr.response.status === 'success') {
                    // Show success message
                    document.getElementById('successTitle').textContent = 'Items Removed Successfully!';
                    document.getElementById('successMessage').textContent =
                        `${cartItemIds.length} item(s) have been removed from your cart.`;

                    $('#removeSuccessModal').modal('show');

                    // Start countdown for automatic redirect
                    var countdownElement = document.getElementById('countdown');
                    var secondsLeft = 3;

                    var countdownInterval = setInterval(function() {
                        secondsLeft--;
                        countdownElement.textContent = secondsLeft;

                        if (secondsLeft <= 0) {
                            clearInterval(countdownInterval);
                            location.reload();
                        }
                    }, 1000);

                    // Allow clicking outside modal to close it
                    $('#removeSuccessModal').on('click', function(event) {
                        if (event.target === this) {
                            clearInterval(countdownInterval);
                            $('#removeSuccessModal').modal('hide');
                            location.reload();
                        }
                    });

                    // Allow Escape key to close modal
                    $(document).on('keydown.removeSuccess', function(event) {
                        if (event.key === 'Escape') {
                            clearInterval(countdownInterval);
                            $('#removeSuccessModal').modal('hide');
                            $(document).off('keydown.removeSuccess');
                            location.reload();
                        }
                    });
                } else {
                    let errorMessage = 'Error removing items';
                    if (xhr.response && xhr.response.message) {
                        errorMessage = xhr.response.message;
                    }
                    alert(errorMessage);
                }
            };

            xhr.onerror = function() {
                console.error('XHR Error:', xhr);
                alert('Error: Could not connect to the server');
            };
        }

        // Save updates to cart
        // ** ใช้ไม่ได้***
        document.getElementById('saveCartBtn').addEventListener('click', function() {
            var cartItems = [];

            document.querySelectorAll('.cart-table tbody tr').forEach(function(row) {
                var cartItemId = row.querySelector('.quantity-input').dataset.cartItemId;
                var quantity = row.querySelector('.quantity-input').value;

                cartItems.push({
                    cart_item_id: cartItemId,
                    quantity: quantity
                });
            });

            var xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo $base_path; ?>/front-end/cart-save-updates.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (xhr.status == 200) {
                    alert('Cart updates saved successfully!');
                    location.reload();
                } else {
                    alert('Error saving cart updates');
                }
            };

            var formData = 'cart_items=' + encodeURIComponent(JSON.stringify(cartItems));
            xhr.send(formData);
        });


        </script>



        <!-- Success Animation Modal -->
        <div class="modal fade" id="removeSuccessModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body text-center py-5">
                        <div class="success-animation">
                            <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                                <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                            </svg>
                        </div>
                        <h3 class="mt-4 font-size-7 font-weight-bold success-title" id="successTitle">Item Removed
                            Successfully!</h3>
                        <p class="mb-4 font-size-5 success-message" id="successMessage">The item has been removed from
                            your cart.</p>
                        <p class="font-size-5 countdown-text">This popup will close automatically in <span
                                id="countdown" class="font-weight-bold">3</span> seconds...</p>

                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Remove Confirmation Modal -->
        <div class="modal fade" id="bulkRemoveModal" tabindex="-1" role="dialog" aria-labelledby="bulkRemoveModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title font-weight-bold" id="bulkRemoveModalLabel">Remove Selected Items</h5>
                        <!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button> -->
                    </div>
                    <div class="modal-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3 text-warning">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div>
                                <p class="mb-2 font-weight-bold">Are you sure you want to remove the selected items?</p>
                                <p class="text-muted mb-0" id="selectedItemsCount">0 items selected</p>
                            </div>
                        </div>
                        <div id="selectedItemsList" class="selected-items-preview">
                            <!-- Selected items will be listed here -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"
                            style="color: white;">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmBulkRemove">Remove Items</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Confirmation Modal -->
        <div class="modal fade" id="paymentConfirmModal" tabindex="-1" role="dialog"
            aria-labelledby="paymentConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content payment-confirm-modal">
                    <div class="modal-header payment-modal-header">
                        <div class="d-flex align-items-center">
                            <div class="payment-icon-wrapper me-3" style="margin-right: 20px;">
                                <i class="fas fa-credit-card"></i>
                            </div>

                            <div>
                                <h4 class="modal-title mb-0" id="paymentConfirmModalLabel" style="color: white;">Confirm
                                    Payment</h4>
                                <p class="modal-subtitle mb-0">Review your payment details</p>
                            </div>
                        </div>
                        <button type="button" class="btn-close-custom" data-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body payment-modal-body">
                        <!-- Selected Payment Method -->
                        <div class="payment-method-section">

                            <h5 class="section-title">
                                <i class="fas fa-credit-card me-2"></i> &nbsp;
                                Payment Method
                            </h5>
                            <div class="selected-payment-card" id="selectedPaymentCard">
                                <div class="card-display">
                                    <div class="card-icon" id="modalCardIcon">
                                        <!-- Card icon will be inserted here -->
                                    </div>
                                    <div class="card-details">
                                        <div class="card-brand" id="modalCardBrand">Visa</div>
                                        <div class="card-number" id="modalCardNumber">•••• •••• •••• 4242</div>
                                        <div class="card-expiry" id="modalCardExpiry">Expires 12/2026</div>
                                    </div>
                                    <div class="card-badge">
                                        <span class="badge bg-success" id="modalCardBadge">Default</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="order-summary-section">
                            <h5 class="section-title">
                                <i class="fas fa-shopping-cart me-2"></i>
                                &nbsp;
                                Order Summary
                            </h5>
                            <div class="order-details">
                                <div class="order-total">
                                    <span class="total-label">Total Amount:</span>
                                    <span class="total-amount" id="modalTotalAmount">$0.00</span>
                                </div>
                                <div class="order-note">
                                    <i class="fas fa-info-circle me-2"></i> &nbsp;
                                    Your card will be charged immediately upon confirmation.
                                </div>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="security-notice">
                            <div class="security-content">
                                <div class="security-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="security-text">
                                    <h6>Secure Payment</h6>
                                    <p>Your payment is protected by Stripe's industry-leading security. Your card
                                        details are encrypted and never stored on our servers.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer payment-modal-footer">
                        <button type="button" class="btn btn-cancel" data-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-confirm" id="confirmPaymentBtn">
                            <i class="fas fa-lock me-2"></i>Confirm Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Stripe.js -->

        <script src="https://js.stripe.com/v3/"></script>

        <!-- Handle save payment method checkbox -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle logged-in user checkbox
            const savePaymentCheckbox = document.querySelector(
                'input[name="save_payment_method"][type="checkbox"]');
            const savePaymentHidden = document.getElementById('save_payment_method_hidden');

            if (savePaymentCheckbox && savePaymentHidden) {
                // Set initial value based on checkbox state
                savePaymentHidden.value = savePaymentCheckbox.checked ? '1' : '0';

                // Update hidden field when checkbox changes
                savePaymentCheckbox.addEventListener('change', function() {
                    savePaymentHidden.value = this.checked ? '1' : '0';
                    console.log('Logged-in user - Save payment method:', this.checked ? 'Yes' : 'No');
                });
            }


        });

        // Payment method selection removed - users now go directly to pre-checkout

        // Payment confirmation functions removed - no longer needed

        // Auto-hide login success message
        document.addEventListener('DOMContentLoaded', function() {
            const loginSuccessAlert = document.querySelector('.alert-success[role="alert"]');
            if (loginSuccessAlert) {
                setTimeout(function() {
                    // Use Bootstrap's fade out animation
                    loginSuccessAlert.classList.remove('show');
                    // Remove from DOM after animation completes
                    setTimeout(function() {
                        if (loginSuccessAlert.parentNode) {
                            loginSuccessAlert.parentNode.removeChild(loginSuccessAlert);
                        }
                    }, 150); // Bootstrap fade animation duration
                }, 5000); // 5 seconds
            }

            // Debug: Show alert if redirected from payment-success with error
            <?php if ($payment_error): ?>
            alert(
                'Debug: You were redirected from payment-success with error: <?php echo htmlspecialchars($payment_error); ?>\n\nThis helps us debug the payment issue. Please check the browser console for more details.'
            );
            console.log('Payment error details:', {
                error: '<?php echo htmlspecialchars($payment_error); ?>',
                url: window.location.href,
                referer: document.referrer
            });
            <?php endif; ?>
        });
        </script>
</body>

</html>