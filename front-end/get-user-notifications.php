<?php
session_start();
include_once('../functions/server.php');
header('Content-Type: application/json');

$user_id = isset($_SESSION['user_id']) ? intval($_SESSION['user_id']) : 0;
$unread_admin_messages = 0;

if ($user_id > 0) {
    $sql = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'admin' AND is_read = 0 AND ticket_id IN (SELECT id FROM support_tickets WHERE user_id = $user_id)";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    $unread_admin_messages = (int)$row['count'];
}

echo json_encode([
    'success' => true,
    'unread_admin_messages' => $unread_admin_messages
]);
