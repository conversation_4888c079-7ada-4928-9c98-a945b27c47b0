# 📧 Email Setup Guide for HelloIT

## 🚨 Issue: Receipt Emails Not Working on Server

This guide helps you diagnose and fix email delivery issues on your production server.

## 🔍 Quick Diagnosis

### Step 1: Test Email Functionality
1. Upload all files to your server
2. Visit: `https://yourserver.com/helloit/functions/test-email-server.php`
3. Enter your email address and click "Send Test Email"
4. Check if you receive the test email

### Step 2: Test Receipt Email Function
1. Visit: `https://yourserver.com/helloit/functions/test-receipt-email.php`
2. Use a recent transaction ID to test receipt emails
3. Check if the receipt email is sent successfully

## 🛠️ Common Issues & Solutions

### Issue 1: PHP mail() Function Not Working
**Symptoms:** Test emails fail to send
**Solutions:**
1. **Contact your hosting provider** - Ask them to enable PHP mail() function
2. **Check server configuration** - Ensure mail server is properly configured
3. **Verify DNS records** - Ensure SPF and MX records are set up for your domain

### Issue 2: Emails Going to Spam
**Symptoms:** Emails are sent but go to spam folder
**Solutions:**
1. **Add SPF record** to your DNS:
   ```
   v=spf1 include:_spf.google.com ~all
   ```
2. **Add DKIM record** (contact hosting provider)
3. **Use proper From address** - Use your domain email (<EMAIL>)

### Issue 3: Server Blocks Outbound Email
**Symptoms:** No errors but emails never arrive
**Solutions:**
1. **Check firewall settings** - Ensure ports 25, 587, 465 are open
2. **Contact hosting provider** - Ask about email sending restrictions
3. **Use SMTP instead** - Configure SMTP authentication

### Issue 4: Missing Email Headers
**Symptoms:** Emails are rejected by recipient servers
**Solutions:**
- ✅ **Fixed in updated code** - Enhanced headers are now included

## 📋 Files Updated

### 1. Enhanced Email Function
- **File:** `functions/send-purchase-receipt.php`
- **Changes:**
  - Better error handling and logging
  - Enhanced email headers for deliverability
  - Automatic fallback methods
  - Timezone function safety checks

### 2. Email Configuration System
- **File:** `functions/email-config-enhanced.php`
- **Features:**
  - Automatic environment detection
  - Fallback logging system
  - Enhanced header generation
  - SMTP support (for future use)

### 3. Diagnostic Tools
- **File:** `functions/test-email-server.php`
- **Purpose:** Test basic email functionality
- **File:** `functions/test-receipt-email.php`
- **Purpose:** Test receipt email system

## 🔧 Server Configuration Requirements

### Minimum Requirements
1. **PHP mail() function enabled**
2. **Outbound email ports open** (25, 587, or 465)
3. **Proper DNS configuration** (MX, SPF records)
4. **No email sending restrictions**

### Recommended Setup
1. **SMTP authentication** (more reliable than PHP mail())
2. **SPF and DKIM records** (better deliverability)
3. **Dedicated email service** (SendGrid, Mailgun, etc.)

## 📞 Contact Your Hosting Provider

If emails still don't work, contact your hosting provider with these questions:

1. **Is PHP mail() function enabled on my server?**
2. **Are there any restrictions on sending emails?**
3. **What SMTP settings should I use for sending emails?**
4. **Can you help me set up SPF and DKIM records?**
5. **Are outbound email ports (25, 587, 465) open?**

## 🧪 Testing Checklist

- [ ] Test basic email functionality
- [ ] Test receipt email with real transaction
- [ ] Check email arrives in inbox (not spam)
- [ ] Verify email formatting looks correct
- [ ] Test with different email providers (Gmail, Yahoo, etc.)
- [ ] Check server error logs for any issues

## 📊 Monitoring

### Error Logs
- Check PHP error logs for email-related errors
- Look for "Receipt:" prefixed log entries
- Monitor email fallback log: `logs/email-fallback.log`

### Success Indicators
- "Receipt: Email sent successfully" in logs
- Emails arrive in recipient's inbox
- No PHP errors related to mail function

## 🚀 Next Steps

1. **Upload all updated files to your server**
2. **Run the diagnostic tools**
3. **Contact hosting provider if needed**
4. **Test with real purchases**
5. **Monitor email delivery**

## 💡 Alternative Solutions

If server email continues to fail, consider:

1. **Third-party email service** (SendGrid, Mailgun, Amazon SES)
2. **SMTP configuration** instead of PHP mail()
3. **Email queue system** for better reliability
4. **Manual email sending** as temporary workaround

---

**Remember:** Email functionality depends heavily on server configuration. The updated code provides better error handling and fallbacks, but the underlying server must support email sending.
