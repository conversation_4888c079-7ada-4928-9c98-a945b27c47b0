-- Add columns to track <PERSON><PERSON><PERSON> sync and updates
-- Run this SQL to add the required columns

ALTER TABLE support_tickets
ADD COLUMN last_appika_sync TIMESTAMP NULL DEFAULT NULL
COMMENT 'Last time this ticket was synced from <PERSON><PERSON><PERSON>';

ALTER TABLE support_tickets
ADD COLUMN appika_updated_at TIMESTAMP NULL DEFAULT NULL
COMMENT 'When this ticket was last updated FROM Appika';

ALTER TABLE support_tickets
ADD COLUMN appika_update_source VARCHAR(50) NULL DEFAULT NULL
COMMENT 'Source of last Appika update (admin_name, system, etc)';

-- Add index for better performance
CREATE INDEX idx_last_appika_sync ON support_tickets(last_appika_sync);
CREATE INDEX idx_appika_id_sync ON support_tickets(appika_id, last_appika_sync);
CREATE INDEX idx_appika_updated ON support_tickets(appika_updated_at);
